<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '推荐老师',
  },
}
</route>
<template>
  <sxt-container @scrolltolower="onScrolltolower">
    <!-- 搜索框 -->
    <view class="bg-white px-32rpx py-24rpx">
      <view class="flex items-center bg-#f5f5f5 rounded-full px-24rpx py-16rpx">
        <wd-icon name="search" size="32rpx" color="#999" class="mr-16rpx" />
        <input
          class="flex-1 text-28rpx text-#333 bg-transparent"
          placeholder="请输入姓名、学校"
          v-model="searchForm.teacherName"
          @input="handleSearch"
        />
        <wd-icon name="filter" size="32rpx" color="#999" @click="showFilter = !showFilter" />
      </view>
    </view>

    <!-- 记录数量 -->
    <view class="px-32rpx py-16rpx">
      <text class="text-24rpx text-#999">{{ totalCount }}条记录</text>
    </view>

    <!-- 老师列表 -->
    <view class="px-32rpx">
      <view
        v-for="teacher in teachers"
        :key="teacher.teacherId"
        class="bg-white rounded-16rpx p-32rpx mb-24rpx shadow-sm"
      >
        <view @click="handleViewTeacherDetail(teacher)" class="flex items-center justify-between">
          <!-- 左侧：头像和信息 -->
          <view class="flex items-center flex-1">
            <view class="w-120rpx h-120rpx rounded-full bg-#f0f0f0 mr-24rpx overflow-hidden">
              <image
                v-if="teacher.avatar"
                :src="teacher.avatar"
                class="w-full h-full object-cover"
                mode="aspectFill"
              />
              <view v-else class="w-full h-full flex items-center justify-center">
                <image
                  src="../../assets/images/default_teacher.png"
                  class="w-full h-full object-cover"
                  mode="aspectFill"
                />
              </view>
            </view>
            <view class="flex-1">
              <view class="text-32rpx font-500 text-#333 mb-8rpx">{{ teacher.teacherName }}</view>
              <view class="text-24rpx text-#666 mb-4rpx">
                {{ userLevelDict[teacher.userLevel].text + '级' }} {{ teacher.education || '本科' }}
              </view>
            </view>
          </view>

          <!-- 右侧：聊天按钮 -->
          <view
            class="rounded-16rpx flex items-center justify-center"
            @click.stop="handleChat(teacher)"
          >
            <image
              src="../../assets/images/ic_message.png"
              class="w-80rpx h-80rpx"
              mode="scaleToFill"
            />
            <wd-icon name="comment" size="32rpx" color="white" />
          </view>
        </view>
      </view>

      <!-- 加载提示 -->
      <view v-if="loading" class="text-center text-24rpx text-#666 py-32rpx">加载中...</view>
      <view v-if="noMore && teachers.length > 0" class="text-center text-24rpx text-#666 py-32rpx">
        没有更多数据了
      </view>
      <view
        v-if="teachers.length === 0 && !loading"
        class="text-center text-24rpx text-#666 py-64rpx"
      >
        暂无老师数据
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <wd-popup v-model="showFilter" position="bottom" :safe-area-inset-bottom="true">
      <view class="bg-white p-32rpx">
        <view class="text-32rpx font-500 text-#333 mb-32rpx text-center">筛选条件</view>
        <wd-form :model="searchForm">
          <wd-form-item label="老师姓名">
            <wd-input v-model="searchForm.teacherName" placeholder="请输入老师姓名" />
          </wd-form-item>
          <wd-picker
            label="老师学历"
            :columns="dictStore.getDictByType('teacher_educational')"
            v-model="searchForm.education"
            label-width="20%"
            :align-right="false"
          />
        </wd-form>
        <view class="flex gap-24rpx mt-32rpx">
          <wd-button block @click="resetFilter">重置</wd-button>
          <wd-button type="primary" block @click="applyFilter">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </sxt-container>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { navigateToSub } from '@/utils'
import { attendclassTeacherPageUsingPost } from '@/service/student/appStudentAttendClassPlanManagement'
import { StuSearchTeacherPageRespVO } from '@/service/student'
import { useDictStore } from '@/store'
defineOptions({
  name: 'Student',
})
const dictStore = useDictStore()
const userInfo = ref()
const education = ref('')
// 老师数据
const teachers = ref<StuSearchTeacherPageRespVO[]>([])
// 搜索关键词
const searchKeyword = ref('')
// 总记录数
const totalCount = ref(90)
// 显示筛选弹窗
const showFilter = ref(false)

// 搜索表单
const searchForm = ref({
  // score: null,
  // schoolName: '',
  // userLevel: '',
  teacherName: '',
  pageNo: 1,
  education: '',
  pageSize: 10,
})
const userLevelDict = dictStore.getDictByType('teacher_level')
console.log(userLevelDict, 'userLevelDict')
// 加载状态
const loading = ref(false)
// 没有更多数据
const noMore = ref(false)

// 查看老师详情
const handleViewTeacherDetail = (teacher: StuSearchTeacherPageRespVO) => {
  if (teacher.teacherId) {
    navigateToSub(`/teacher-detail/teacher-detail?teacherId=${teacher.teacherId}`)
  } else {
    uni.showToast({
      title: '无法查看详情，缺少老师ID',
      icon: 'none',
    })
  }
}

// 处理聊天
const handleChat = (teacher: StuSearchTeacherPageRespVO) => {
  if (teacher.teacherId) {
    navigateToSub(`/chat/chat?receiverUserID=${teacher.teacherId}`)
  } else {
    uni.showToast({
      title: '无法发起聊天，缺少老师ID',
      icon: 'none',
    })
  }
}

// 处理搜索
const handleSearch = () => {
  searchForm.value.pageNo = 1
  searchTeachers()
}

// 重置筛选
const resetFilter = () => {
  searchForm.value = {
    education: '',
    teacherName: '',
    pageNo: 1,
    pageSize: 10,
  }
  searchKeyword.value = ''
  showFilter.value = false
  searchTeachers()
}

// 应用筛选
const applyFilter = () => {
  searchForm.value.pageNo = 1
  showFilter.value = false
  searchTeachers()
  if (teachers.value.length === 0) {
    resetFilter()
  }
}

// 搜索老师
const searchTeachers = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true
  try {
    const teacherRes = await attendclassTeacherPageUsingPost({
      body: {
        ...searchForm.value,
      },
    })

    if (isLoadMore) {
      teachers.value = [...teachers.value, ...teacherRes.data.items]
    } else {
      teachers.value = teacherRes.data.items
    }

    // 更新总记录数
    totalCount.value = (teacherRes.data as any).total || teacherRes.data.items.length

    noMore.value = teacherRes.data.items.length < searchForm.value.pageSize
    if (isLoadMore) {
      searchForm.value.pageNo++
    }
  } catch (error) {
    console.error('搜索老师失败', error)
    uni.showToast({
      title: '搜索失败，请稍后重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 上拉加载更多
const onScrolltolower = () => {
  console.log('是否触发了上拉加载更多')
  if (!noMore.value) {
    searchTeachers(true)
  }
}

const getInit = async () => {
  await searchTeachers()
}
onMounted(() => {
  getInit()
  // 监听上拉触底事件
})
</script>
