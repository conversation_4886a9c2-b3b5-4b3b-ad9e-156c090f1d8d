<route lang="json5">
{
  style: {
    navigationBarTitleText: '约课',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="min-h-screen bg-#f5f5f5">
    <!-- 老师信息卡片 -->
    <view class="mx-32rpx pt-32rpx mb-32rpx">
      <view class="bg-white rounded-24rpx p-32rpx">
        <wd-loading v-if="isFetchingTeacher" custom-class="loading-spinner" />
        <template v-if="!isFetchingTeacher">
          <view class="flex items-center mb-24rpx">
            <!-- 老师头像 -->
            <view class="w-96rpx h-96rpx bg-#f0f0f0 rounded-full mr-24rpx overflow-hidden">
              <image
                :src="teacherInfo.avatar || '/static/images/avatar_placeholder.png'"
                class="w-full h-full"
                mode="aspectFill"
              />
            </view>

            <!-- 老师信息 -->
            <view class="flex-1">
              <view class="text-32rpx font-600 text-#333 mb-8rpx">{{ teacherInfo.name }}</view>
              <view class="text-24rpx text-#666">
                {{ teacherInfo.school }} {{ teacherInfo.major ? '·' : '' }} {{ teacherInfo.major }}
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>

    <!-- 约课表单 -->
    <view class="mx-32rpx mb-32rpx">
      <view class="bg-white rounded-24rpx p-32rpx">
        <wd-form ref="formRef" :model="form">
          <!-- 上课时长 -->
          <view class="form-item">
            <view class="form-label">上课时长</view>
            <wd-picker
              v-model="form.courseDuration"
              :columns="dictStore.getDictByType('teacher_course_duration')"
              :value="form.courseDuration"
              @confirm="handleDurationChange"
              custom-class="form-picker"
              placeholder="请选择上课时长"
            />
          </view>

          <view class="form-item">
            <view class="form-label">上课日期</view>
            <wd-calendar
              @Change="handleChange"
              v-model="form.trainingDateList"
              type="dates"
              @confirm="confirmTrainingDateList"
              placeholder="请选择上课日期"
              :display-format="displayFormat"
              :min-date="dateRange.minDate"
              :max-date="dateRange.maxDate"
            />
          </view>

          <!-- 开始时间 -->
          <view class="form-item">
            <view class="form-label">开始时间</view>
            <wd-datetime-picker
              v-model="form.trainingTime"
              type="time"
              :default-value="dateRange.minTime"
              placeholder="请选择开始时间"
              custom-class="form-picker"
            />
          </view>
        </wd-form>
      </view>
    </view>

    <!-- 确认约课按钮 -->
    <view class="mx-32rpx mb-32rpx">
      <wd-button
        custom-class="w-full! h-96rpx! rounded-24rpx! text-32rpx! font-600! bg-#3d5af5! border-#3d5af5!"
        type="primary"
        @click="handleSubmit"
      >
        确认约课
      </wd-button>
    </view>
    <!-- 约课成功弹窗 -->
    <wd-message-box
      selector="bookClass"
      v-model="showSuccessDialog"
      title="约课成功"
      custom-class="success-dialog"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <view class="success-dialog-content">
        <!-- 成功图标 -->
        <view class="success-icon">
          <image
            class="w-120rpx h-120rpx"
            src="../../assets/images/book-class-success.png"
            mode="scaleToFill"
          />
        </view>

        <!-- 成功文字 -->
        <view class="success-title">约课成功</view>
        <view class="success-subtitle">请及时前往上课！</view>
      </view>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { reservationCreateUsingPost } from '@/service/student/appStudentReservationRecordManagement'
import { attendclassTeacherDetailUsingPost } from '@/service/student/appStudentAttendClassPlanManagement'
import { baseinfoCurrentUsingPost } from '@/service/student/appStudentBaseInfoManagement'
import { navigateTo, showToast } from '@/utils'
import dayjs from 'dayjs'
import { useDictStore } from '@/store'
import { useMessage } from 'wot-design-uni'

defineOptions({
  name: 'BookClass',
})

const studentInfo = ref<any>(null)
const teacherInfo = reactive({
  id: null,
  name: '',
  school: '',
  major: '',
  avatar: '',
})

// 弹窗状态
const showSuccessDialog = ref(false)
const dateRange = {
  minDate: dayjs().startOf('day').valueOf(),
  maxDate: dayjs().add(6, 'month').endOf('day').valueOf(),
  minTime: dayjs().valueOf(),
  maxTime: dayjs().endOf('day').valueOf(),
}
const dictStore = useDictStore()
const isFetchingTeacher = ref(true)
const formRef = ref(null)
const form = reactive({
  courseDuration: '2', // 默认体验课
  trainingDateList: [],
  trainingTime: '09:00',
})
const isTrialClass = computed(() => form.courseDuration === '2')
const message = useMessage('bookClass')

// 计算结束时间
const endTime = computed(() => {
  if (!form.trainingTime) return ''

  // 根据课程时长计算结束时间
  const duration = dictStore
    .getDictByType('teacher_course_duration')
    .find((item) => item.value === form.courseDuration)
  if (!duration) return ''

  const startTime = dayjs(`2024-01-01 ${form.trainingTime}`)
  const durationMinutes = duration.label.includes('60') ? 60 : 30 // 简单解析，实际应该更严格
  const endTime = startTime.add(durationMinutes, 'minute')

  return endTime.format('HH:mm')
})
const displayFormat = (value) => {
  if (form.courseDuration === '2' && value.length > 1) {
    return ''
  }
  if (value.length > 0) {
    const datas = value.map((item) => {
      return dayjs(item).format('YYYY-MM-DD')
    })
    return datas.join(',')
  }
}
const handleDurationChange = ({ value }) => {
  form.courseDuration = value
}
const confirmTrainingDateList = ({ value }) => {
  if (form.courseDuration === '2' && value.length > 1) {
    showToast('体验课只能预约一次')
    form.trainingDateList = []
    return
  }
  const datas = value.map((item) => {
    return dayjs(item).format('YYYY-MM-DD')
  })
  form.trainingDateList = datas
}

const handleChange = (value) => {
  console.log('dfadas', value)
}

const fetchTeacherDetail = async (id: number) => {
  isFetchingTeacher.value = true
  try {
    const res = await attendclassTeacherDetailUsingPost({ body: { id } })
    if (res.code === 200 && res.data && res.data.item) {
      const { name, school, major } = res.data.item
      teacherInfo.name = name || ''
      teacherInfo.school = school || ''
      teacherInfo.major = major || ''
    } else {
      showToast('获取老师信息失败')
    }
  } catch (error) {
    console.error('获取老师信息异常:', error)
  } finally {
    isFetchingTeacher.value = false
  }
}

onLoad(async (options: any) => {
  // 获取老师信息
  if (options.teacherId) {
    teacherInfo.id = Number(options.teacherId)
    await fetchTeacherDetail(teacherInfo.id)
  } else {
    isFetchingTeacher.value = false
  }

  // 获取学生信息
  try {
    const res = await baseinfoCurrentUsingPost({})
    if (res.code === 200 && res.data) {
      studentInfo.value = res.data.item
    }
  } catch (error) {
    console.error('获取学生信息失败', error)
  }
})

// 弹窗相关方法
const goBackHome = () => {
  showSuccessDialog.value = false
  uni.switchTab({
    url: '/pages/index/index',
  })
}

const viewDetails = () => {
  showSuccessDialog.value = false
  // 这里可以跳转到约课详情页面
  uni.navigateBack()
}

const handleSubmit = async () => {
  if (!teacherInfo.id) {
    showToast('缺少老师信息')
    return
  }

  const params = {
    teacherId: teacherInfo.id,
    courseDuration: form.courseDuration,
    trainingDateList: form.trainingDateList,
    trainingTime: form.trainingTime,
    // 体验课不传次数和周期
  }
  console.log('params', params)

  try {
    const res = await reservationCreateUsingPost({ body: params })
    console.log(res)
    if (res.code === 200) {
      // 显示成功弹窗
      message
        .confirm({
          // msg: '请及时前往上课',
          title: '',
          confirmButtonText: '确定',
          cancelButtonText: '返回首页',
        })
        .then(() => {
          console.log('约课成功')
        })
        .catch(() => {
          navigateTo('/index/index')
        })
    }
  } catch (error) {
    console.error('约课异常', error)
  }
}
</script>

<style lang="scss" scoped>
.loading-spinner {
  margin: auto;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-value {
  font-size: 28rpx;
  color: #666;
}

.form-picker {
  flex: 1;
  text-align: right;
}

// 成功弹窗样式
.success-dialog-content {
  text-align: center;
  padding: 40rpx 32rpx 32rpx;
}

.success-icon {
  margin-bottom: 32rpx;

  .icon-wrapper {
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border-radius: 50%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
  }

  .success-emoji {
    font-size: 48rpx;
  }
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.button-group {
  display: flex;
  gap: 24rpx;

  .back-btn {
    flex: 1;
    height: 80rpx;
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 16rpx;
    font-size: 28rpx;
  }

  .detail-btn {
    flex: 1;
    height: 80rpx;
    background: #3d5af5;
    border: none;
    border-radius: 16rpx;
    font-size: 28rpx;
  }
}
:deep(.wd-message-box .wd-button) {
  border-radius: 10rpx;
  line-height: 100rpx;
  padding-block: 20rpx;
  height: 70rpx;
}
</style>
