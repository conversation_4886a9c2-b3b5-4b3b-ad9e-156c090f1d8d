/* eslint-disable */
// @ts-ignore

export type AnswerDto = {
  /** 所属试题ID */
  questionId: number;
  /** 学生的答案（单选/多选题为选项ID(多个id按照英文的逗号隔开)，简答题为文本） */
  studentAnswer: string;
  /** 是否答题正确 -1为不知道0错误1正确，选择题必传，简答题不用传。如果是不知道就传-1 */
  isCorrectAnswered: number;
};

export type AnswerExamVo = {
  /** 试卷id */
  examId?: number;
  /** sessionId */
  sessionId?: string;
  /** 试卷名称 */
  paperName?: string;
  /** 题目列表 */
  questions?: QuestionVo[];
};

export type CreateExamVo = {
  /** 会话id */
  sessionId?: string;
};

export type DataWrapperBoolean = {
  item?: boolean;
  items?: boolean[];
};

export type DataWrapperCreateExamVo = {
  item?: CreateExamVo;
  items?: CreateExamVo[];
};

export type DataWrapperExamVo = {
  item?: ExamVo;
  items?: ExamVo[];
};

export type ExamDto = {
  /** 学生id */
  studentId: number;
  /** 老师id */
  teacherId: number;
  /** 上课计划id */
  attendId: number;
  /** 单词列表，以英文的逗号分开。比如 create,produce */
  words: string;
};

export type ExamVo = {
  /** 试卷id */
  examId?: number;
  /** sessionId */
  sessionId?: string;
  /** 试卷名称 */
  paperName?: string;
  /** 题目列表 */
  questions?: QuestionVo[];
};

export type OptionVo = {
  /** 选项内容 */
  optionContent?: string;
  /** 选项内容 */
  optionId?: number;
  /** 是否是正确选项 */
  isCorrect?: boolean;
};

export type PageDto = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
};

export type PageResultAnswerExamVo = {
  /** 数据列表 */
  items?: AnswerExamVo[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultResponsePageResultAnswerExamVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultAnswerExamVo;
};

export type QueryByTeacherIdDto = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 学生id */
  studentId: number;
};

export type QuestionVo = {
  /** 题干 */
  questionText?: string;
  /** 题干id */
  questionId?: number;
  /** 是否答题正确 -2为未作答-1为不知道0错误1正确 */
  isCorrectAnswered?: number;
  /** 选项列表 */
  options?: OptionVo[];
};

export type ResultBoolean = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperBoolean;
};

export type ResultCreateExamVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCreateExamVo;
};

export type ResultExamVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperExamVo;
};

export type SessionIdDto = {
  /** sessionId */
  sessionId: string;
};
