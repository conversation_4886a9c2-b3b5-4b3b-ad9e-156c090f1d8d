/* eslint-disable */
// @ts-ignore

export type AttendClassPlanAntiforgetStatisticsRespVO = {
  /** 上课计划id */
  id?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师id */
  teacherId?: number;
  /** 训练名称、课件资料名称 */
  coursewareName?: string;
  /** 复习抗遗忘单词总数 */
  wordCount?: number;
  /** 复习抗遗忘单词正确数 */
  correctCount?: number;
  /** 复习抗遗忘单词错误数 */
  errorCount?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
};

export type AttendClassPlanDetialPageRespVO = {
  /** 上课计划id */
  id?: number;
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 交付方式 */
  payWay?: string;
  /** 老师名称 */
  teacherName?: string;
  /** 学生名称 */
  studentName?: string;
  /** 上课日期 */
  classDate?: string;
  startTime?: LocalTime;
  endTime?: LocalTime;
  /** 上课状态：0-未开始，1-进行中，2-已结束，3-已取消 */
  progress?: string;
  /** 资料名称 */
  coursewareName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 删除标记：0-未删除，1-已删除 */
  deleted?: boolean;
  /** 开始日期 */
  startClassDate?: string;
  /** 结束日期 */
  endClassDate?: string;
  /** 是否要做抗遗忘： 0 不做 1 做 */
  isToDoAntiForget?: string;
};

export type AttendClassPlanGetReqVO = {
  /** 上课计划id */
  id: number;
};

export type AttendClassPlanPageReqVO = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 时长类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 开始日期 */
  startClassDate?: string;
  /** 结束日期 */
  endClassDate?: string;
  /** 上课状态 */
  progress?: string;
  /** 资料名称 */
  coursewareName?: string;
  /** 支付方式 */
  payWay?: string;
};

export type AttendClassPlanStatisticsRespVO = {
  /** 上课计划id */
  id?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师id */
  teacherId?: number;
  /** 学新单词总数 */
  newWordsCount?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 是否要做抗遗忘： 0 不做 1 做 */
  isToDoAntiForget?: string;
  /** 是否已经做了抗遗忘： 0 未做 1 已做 */
  isDoneAntiForget?: string;
};

export type AuthLoginReqVO = {
  /** 账号 */
  username: string;
  /** 密码 */
  password: string;
  /** 用户类型 0系统5推广机构 */
  userType?: number;
};

export type AuthLoginRespVO = {
  /** 用户编号 */
  userId?: number;
  /** 访问令牌 */
  accessToken?: string;
  /** 角色:0-系统管理, 1-老师, 2-学生, 3-运营, 4-财务, 5-机构 */
  role?: string;
  /** 腾讯im的userSig */
  userSig?: string;
  /** 过期时间戳 */
  expiresTime?: number;
};

export type CourseReservationCreateV2ReqVO = {
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration: string;
  /** 学生id */
  studentId: number;
  /** 老师id */
  teacherId: number;
  /** 资料id */
  articleTitleId: number;
  /** 上课日期集合 */
  trainingDateList: string[];
  /** 上课时间点 */
  trainingTime: string;
  /** 支付方式 0-线上  1-线下 */
  payWay: number;
  /** 价格 */
  price: number;
};

export type CourseReservationDetialRespVO = {
  /** id */
  id?: number;
  /** 课程时长类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 预约次数 */
  reservationCount?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生id */
  studentId?: number;
  /** 老师id */
  teacherId?: number;
  /** 课件资料名称 */
  coursewareName?: string;
  /** 学生姓名账号 */
  studentNameAccount?: string;
  /** 老师姓名账号 */
  teacherNameAccount?: string;
  /** 约课状态 预约待确认：0，预约成功：1,预约失败：2，取消待确认：3，取消成功：4 */
  status?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 间隔时间 0-每天 1-每周 */
  intervalTime?: string;
  /** 首次日期 */
  firstDate?: string;
  /** 资料id */
  articleTitleId?: number;
  trainingTime?: LocalTime;
  /** 支付方式 0-线上  1-线下 */
  payWay?: string;
  /** 价格 */
  price?: number;
};

export type CourseReservationPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 课程计划id */
  planId?: number;
  courseDuration?: string;
  /** 老师id */
  teacherId?: number;
  /** 学生id */
  studentId?: number;
  /** 预约开始时间（默认当前时间） */
  startTime?: string;
  /** 预约结束时间（默认当前时间） */
  endTime?: string;
  /** 资料中的文章标题id */
  articleTitleId?: number;
  /** 资料名称 */
  coursewareName?: string;
  /** 预约次数 */
  reservationCount?: number;
  status?: string;
  progress?: string;
  /** 间隔时间 0-每天 1-每周 */
  intervalTime?: string;
  /** 首次日期 */
  firstDate?: string;
  trainingTime?: LocalTime;
  /** 支付方式 0-线上  1-线下 */
  payWay?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
};

export type CoursewareByAdminPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 资料名称 */
  name?: string;
  /** 课件资料年级 */
  courseGrade?: string;
};

export type CoursewareByAdminPageRespVO = {
  /** 课件资料ID */
  id?: number;
  /** 资料名称 */
  name?: string;
  /** 课件资料年级 */
  courseGrade?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建人 */
  creator?: string;
  /** 更新人 */
  updater?: string;
};

export type CoursewareCreateReqVO = {
  /** 资料名称 */
  name: string;
  /** 课件资料年级 */
  courseGrade: string;
};

export type CoursewareGetReqVO = {
  /** 课件资料id */
  id: number;
};

export type CoursewareModifyReqVO = {
  /** 课件资料id */
  id: number;
  /** 资料名称 */
  name: string;
  /** 课件资料年级 */
  courseGrade: string;
};

export type CoursewareWordBindReqVO = {
  /** 课件资料id */
  coursewareId: number;
  /** 单词id集合 */
  coursewareWordId: number[];
};

export type CoursewareWordByAdminPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 资料id */
  coursewareId?: number;
  /** 单词 */
  word?: string;
  /** 中文释义 */
  chinese?: string;
  /** 单词与基础课件绑定类型：0-未绑定  1-已绑定 */
  wordBindType?: string;
};

export type CoursewareWordByAdminPageRespVO = {
  /** 单词id */
  id?: number;
  /** 单词 */
  word?: string;
  /** 中文释义 */
  chinese?: string;
  /** 音频地址 */
  mp3?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建人 */
  creator?: string;
  /** 更新人 */
  updater?: string;
};

export type CoursewareWordCreateReqVO = {
  /** 单词 */
  word: string;
  /** 中文释义 */
  chinese: string;
  /** 音频地址 */
  mp3?: string;
};

export type CoursewareWordGetReqVO = {
  /** 单词id */
  id: number;
};

export type CoursewareWordModifyReqVO = {
  /** 单词id */
  id: number;
  /** 单词 */
  word: string;
  /** 中文释义 */
  chinese: string;
  /** 音频地址 */
  mp3?: string;
};

export type CoursewareWordUnBindReqVO = {
  /** 课件资料id */
  coursewareId: number;
  /** 单词id集合 */
  coursewareWordId: number[];
};

export type DataWrapperAttendClassPlanAntiforgetStatisticsRespVO = {
  item?: AttendClassPlanAntiforgetStatisticsRespVO;
  items?: AttendClassPlanAntiforgetStatisticsRespVO[];
};

export type DataWrapperAttendClassPlanStatisticsRespVO = {
  item?: AttendClassPlanStatisticsRespVO;
  items?: AttendClassPlanStatisticsRespVO[];
};

export type DataWrapperAuthLoginRespVO = {
  item?: AuthLoginRespVO;
  items?: AuthLoginRespVO[];
};

export type DataWrapperBoolean = {
  item?: boolean;
  items?: boolean[];
};

export type DataWrapperCourseReservationDetialRespVO = {
  item?: CourseReservationDetialRespVO;
  items?: CourseReservationDetialRespVO[];
};

export type DataWrapperCoursewareByAdminPageRespVO = {
  item?: CoursewareByAdminPageRespVO;
  items?: CoursewareByAdminPageRespVO[];
};

export type DataWrapperCoursewareWordByAdminPageRespVO = {
  item?: CoursewareWordByAdminPageRespVO;
  items?: CoursewareWordByAdminPageRespVO[];
};

export type DataWrapperFinanceWalletInfoRespVO = {
  item?: FinanceWalletInfoRespVO;
  items?: FinanceWalletInfoRespVO[];
};

export type DataWrapperInteger = {
  item?: number;
  items?: number[];
};

export type DataWrapperListInteger = {
  item?: number[];
  items?: number[][];
};

export type DataWrapperListString = {
  item?: string[];
  items?: string[][];
};

export type DataWrapperListSysPriceConfigDO = {
  item?: SysPriceConfigDO[];
  items?: SysPriceConfigDO[][];
};

export type DataWrapperListSystemDictDataRespVO = {
  item?: SystemDictDataRespVO[];
  items?: SystemDictDataRespVO[][];
};

export type DataWrapperListSystemMenuTreeRespVO = {
  item?: SystemMenuTreeRespVO[];
  items?: SystemMenuTreeRespVO[][];
};

export type DataWrapperLong = {
  item?: number;
  items?: number[];
};

export type DataWrapperObject = {
  item?: Record<string, unknown>;
  items?: Record<string, unknown>[];
};

export type DataWrapperString = {
  item?: string;
  items?: string[];
};

export type DataWrapperStudentPractiseQuestionBankBaseRespVO = {
  item?: StudentPractiseQuestionBankBaseRespVO;
  items?: StudentPractiseQuestionBankBaseRespVO[];
};

export type DataWrapperStudentVipConfigBaseRespVO = {
  item?: StudentVipConfigBaseRespVO;
  items?: StudentVipConfigBaseRespVO[];
};

export type DataWrapperStuUserGetRespVO = {
  item?: StuUserGetRespVO;
  items?: StuUserGetRespVO[];
};

export type DataWrapperSysOrgDetialRespVO = {
  item?: SysOrgDetialRespVO;
  items?: SysOrgDetialRespVO[];
};

export type DataWrapperSysOrgWalletInfoRespVO = {
  item?: SysOrgWalletInfoRespVO;
  items?: SysOrgWalletInfoRespVO[];
};

export type DataWrapperSystemMenuBaseRespVO = {
  item?: SystemMenuBaseRespVO;
  items?: SystemMenuBaseRespVO[];
};

export type DataWrapperSystemUserBaseRespVO = {
  item?: SystemUserBaseRespVO;
  items?: SystemUserBaseRespVO[];
};

export type DataWrapperTeacherAndStudentRespVO = {
  item?: TeacherAndStudentRespVO;
  items?: TeacherAndStudentRespVO[];
};

export type DataWrapperTeacherComplaintBaseRespVO = {
  item?: TeacherComplaintBaseRespVO;
  items?: TeacherComplaintBaseRespVO[];
};

export type DataWrapperTeacherEvaluateBaseRespVO = {
  item?: TeacherEvaluateBaseRespVO;
  items?: TeacherEvaluateBaseRespVO[];
};

export type DataWrapperTeacherIncomeConfigBaseRespVO = {
  item?: TeacherIncomeConfigBaseRespVO;
  items?: TeacherIncomeConfigBaseRespVO[];
};

export type DataWrapperTeacherUserDO = {
  item?: TeacherUserDO;
  items?: TeacherUserDO[];
};

export type DataWrapperTrainingRecordDetialRespVO = {
  item?: TrainingRecordDetialRespVO;
  items?: TrainingRecordDetialRespVO[];
};

export type DictData = {
  /** 字典标签 */
  label?: string;
  /** 字典值 */
  value?: string;
  /** 备注 */
  remark?: string;
};

export type FinanceWalletInfoDetailsRespVO = {
  /** 时间 */
  time?: string;
  /** 资金类型 1收入2支出 */
  fundsTypes?: number;
  /** 资金 */
  amount?: number;
  /** 用户 */
  userName?: string;
  /** 场景 */
  scene?: string;
};

export type FinanceWalletInfoRespVO = {
  /** 总收入 */
  totalIncome?: number;
  /** 今日收入 */
  todayIncome?: number;
  /** 总利润 */
  totalProfit?: number;
  /** 今日利润 */
  todayProfit?: number;
};

export type HistoryDto = {
  /** 发送人账号 */
  from: string;
  /** 接收人账号 */
  to: string;
};

export type LocalTime = {
  hour?: number;
  minute?: number;
  second?: number;
  nano?: number;
};

export type MessageVo = {
  /** 发送人 */
  from?: string;
  /** 接收人 */
  to?: string;
  /** 消息内容 */
  msg?: string;
  /** 发送时间 */
  time?: string;
};

export type OrderDetailsDto = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 充值类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType?: number;
  /** 充值用户 */
  userName?: string;
};

export type OrderDetailsVo = {
  /** 时间 */
  time?: string;
  /** 充值类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType?: number;
  /** 充值数量 */
  number?: number;
  /** 充值金额 */
  amount?: number;
  /** 充值用户 */
  userName?: string;
};

export type PageDto = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
};

export type PageResultAttendClassPlanDetialPageRespVO = {
  /** 数据列表 */
  items?: AttendClassPlanDetialPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultCourseReservationDetialRespVO = {
  /** 数据列表 */
  items?: CourseReservationDetialRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultCoursewareByAdminPageRespVO = {
  /** 数据列表 */
  items?: CoursewareByAdminPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultCoursewareWordByAdminPageRespVO = {
  /** 数据列表 */
  items?: CoursewareWordByAdminPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultFinanceWalletInfoDetailsRespVO = {
  /** 数据列表 */
  items?: FinanceWalletInfoDetailsRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultOrderDetailsVo = {
  /** 数据列表 */
  items?: OrderDetailsVo[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultResponseListMessageVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  /** 数据 */
  data?: MessageVo[];
};

export type PageResultResponsePageResultAttendClassPlanDetialPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultAttendClassPlanDetialPageRespVO;
};

export type PageResultResponsePageResultCourseReservationDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCourseReservationDetialRespVO;
};

export type PageResultResponsePageResultCoursewareByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCoursewareByAdminPageRespVO;
};

export type PageResultResponsePageResultCoursewareWordByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCoursewareWordByAdminPageRespVO;
};

export type PageResultResponsePageResultFinanceWalletInfoDetailsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultFinanceWalletInfoDetailsRespVO;
};

export type PageResultResponsePageResultOrderDetailsVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultOrderDetailsVo;
};

export type PageResultResponsePageResultStudentPractiseQuestionBankBaseRespVO =
  {
    /** 状态码  200成功  ，  非200失败 */
    code?: number;
    /** 描述 */
    message?: string;
    data?: PageResultStudentPractiseQuestionBankBaseRespVO;
  };

export type PageResultResponsePageResultStudentVipConfigBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStudentVipConfigBaseRespVO;
};

export type PageResultResponsePageResultStuUserPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStuUserPageRespVO;
};

export type PageResultResponsePageResultSysOrgDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSysOrgDetialRespVO;
};

export type PageResultResponsePageResultSysOrgWalletDetailsRespVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSysOrgWalletDetailsRespVo;
};

export type PageResultResponsePageResultSysPriceConfigDO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSysPriceConfigDO;
};

export type PageResultResponsePageResultSystemDictDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSystemDictDetialRespVO;
};

export type PageResultResponsePageResultSystemMenuDO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSystemMenuDO;
};

export type PageResultResponsePageResultSystemRoleDO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSystemRoleDO;
};

export type PageResultResponsePageResultSystemUserBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultSystemUserBaseRespVO;
};

export type PageResultResponsePageResultTeacherComplaintBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultTeacherComplaintBaseRespVO;
};

export type PageResultResponsePageResultTeacherEvaluateBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultTeacherEvaluateBaseRespVO;
};

export type PageResultResponsePageResultTeacherIncomeConfigBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultTeacherIncomeConfigBaseRespVO;
};

export type PageResultResponsePageResultTrainingRecordDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultTrainingRecordDetialRespVO;
};

export type PageResultResponseString = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  /** 数据 */
  data?: string;
};

export type PageResultStudentPractiseQuestionBankBaseRespVO = {
  /** 数据列表 */
  items?: StudentPractiseQuestionBankBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStudentVipConfigBaseRespVO = {
  /** 数据列表 */
  items?: StudentVipConfigBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStuUserPageRespVO = {
  /** 数据列表 */
  items?: StuUserPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSysOrgDetialRespVO = {
  /** 数据列表 */
  items?: SysOrgDetialRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSysOrgWalletDetailsRespVo = {
  /** 数据列表 */
  items?: SysOrgWalletDetailsRespVo[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSysPriceConfigDO = {
  /** 数据列表 */
  items?: SysPriceConfigDO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSystemDictDetialRespVO = {
  /** 数据列表 */
  items?: SystemDictDetialRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSystemMenuDO = {
  /** 数据列表 */
  items?: SystemMenuDO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSystemRoleDO = {
  /** 数据列表 */
  items?: SystemRoleDO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultSystemUserBaseRespVO = {
  /** 数据列表 */
  items?: SystemUserBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultTeacherComplaintBaseRespVO = {
  /** 数据列表 */
  items?: TeacherComplaintBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultTeacherEvaluateBaseRespVO = {
  /** 数据列表 */
  items?: TeacherEvaluateBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultTeacherIncomeConfigBaseRespVO = {
  /** 数据列表 */
  items?: TeacherIncomeConfigBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultTrainingRecordDetialRespVO = {
  /** 数据列表 */
  items?: TrainingRecordDetialRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type pcCoursewareOpenApiImportUsingPostParams = {
  /** Excel 课件基础资料模版文件 */
  file: unknown;
};

export type pcCoursewareWordOpenApiImportUsingPostParams = {
  /** Excel 课件词汇资料模板文件 */
  file: unknown;
};

export type pcQuestionbankOpenApiImportUsingPostParams = {
  /** Excel 题库模版文件 */
  file: unknown;
};

export type pcSystemAuthRefreshTokenUsingPostParams = {
  /** 刷新令牌 */
  refreshToken: string;
};

export type RechargePcReqVo = {
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration: string;
  /** 课时节数 */
  classNumber: number;
  /** 用户id（给哪一个用户充值） */
  userId: number;
  /** 支付类型 0线上1线下 */
  payType: number;
};

export type ReservationGetReqVO = {
  /** 约课记录id */
  id: number;
};

export type ResultAttendClassPlanAntiforgetStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanAntiforgetStatisticsRespVO;
};

export type ResultAttendClassPlanStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanStatisticsRespVO;
};

export type ResultAuthLoginRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAuthLoginRespVO;
};

export type ResultBoolean = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperBoolean;
};

export type ResultCourseReservationDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCourseReservationDetialRespVO;
};

export type ResultCoursewareByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCoursewareByAdminPageRespVO;
};

export type ResultCoursewareWordByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCoursewareWordByAdminPageRespVO;
};

export type ResultFinanceWalletInfoRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperFinanceWalletInfoRespVO;
};

export type ResultInteger = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperInteger;
};

export type ResultListInteger = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListInteger;
};

export type ResultListString = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListString;
};

export type ResultListSysPriceConfigDO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListSysPriceConfigDO;
};

export type ResultListSystemDictDataRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListSystemDictDataRespVO;
};

export type ResultListSystemMenuTreeRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListSystemMenuTreeRespVO;
};

export type ResultLong = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperLong;
};

export type ResultObject = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperObject;
};

export type ResultString = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperString;
};

export type ResultStudentPractiseQuestionBankBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStudentPractiseQuestionBankBaseRespVO;
};

export type ResultStudentVipConfigBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStudentVipConfigBaseRespVO;
};

export type ResultStuUserGetRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStuUserGetRespVO;
};

export type ResultSysOrgDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperSysOrgDetialRespVO;
};

export type ResultSysOrgWalletInfoRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperSysOrgWalletInfoRespVO;
};

export type ResultSystemMenuBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperSystemMenuBaseRespVO;
};

export type ResultSystemUserBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperSystemUserBaseRespVO;
};

export type ResultTeacherAndStudentRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherAndStudentRespVO;
};

export type ResultTeacherComplaintBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherComplaintBaseRespVO;
};

export type ResultTeacherEvaluateBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherEvaluateBaseRespVO;
};

export type ResultTeacherIncomeConfigBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherIncomeConfigBaseRespVO;
};

export type ResultTeacherUserDO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherUserDO;
};

export type ResultTrainingRecordDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTrainingRecordDetialRespVO;
};

export type StudentPractiseQuestionBankBaseRespVO = {
  /** 题库ID */
  id?: number;
  /** 类型 0-评测 1-练习 */
  type?: string;
  /** 类型名称 0-评测 1-练习 */
  typeName?: string;
  /** 级别 */
  level?: string;
  /** 级别名称 */
  levelName?: string;
  /** 所属版本 */
  version?: string;
  /** 所属版本名称 */
  versionName?: string;
  /** 所属年级 */
  grade?: string;
  /** 所属年级名称 */
  gradeName?: string;
  /** 所属单元 */
  unit?: string;
  /** 所属单元名称 */
  unitName?: string;
  /** 单词内容 */
  content?: string;
  /** 备选答案1 */
  option1?: string;
  /** 备选答案2 */
  option2?: string;
  /** 备选答案3 */
  option3?: string;
  /** 备选答案4 */
  option4?: string;
  /** 备选答案5 */
  option5?: string;
  /** 正确答案：1-备选答案1，2-备选答案2，3-备选答案3，4-备选答案4，5-备选答案5 */
  answer?: number;
  /** 状态：0-下架，1-上架 */
  status?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建者， */
  creator?: string;
  /** 更新者， */
  updater?: string;
};

export type StudentPractiseQuestionBankCreateReqVO = {
  /** 类型 0-评测 1-练习 */
  type: string;
  /** 级别 */
  level?: string;
  /** 所属版本 */
  version?: string;
  /** 所属年级 */
  grade?: string;
  /** 所属单元 */
  unit?: string;
  /** 单词内容 */
  content: string;
  /** 备选答案1 */
  option1: string;
  /** 备选答案2 */
  option2: string;
  /** 备选答案3 */
  option3: string;
  /** 备选答案4 */
  option4: string;
  /** 备选答案5 */
  option5: string;
  /** 正确答案：1-备选答案1，2-备选答案2，3-备选答案3，4-备选答案4，5-备选答案5 */
  answer: number;
};

export type StudentPractiseQuestionBankGetReqVO = {
  /** 题库ID */
  id: number;
};

export type StudentPractiseQuestionBankModifyReqVO = {
  /** 题库ID */
  id: number;
  /** 类型 0-评测 1-练习 */
  type: string;
  /** 级别 */
  level?: string;
  /** 所属版本 */
  version?: string;
  /** 所属年级 */
  grade?: string;
  /** 所属单元 */
  unit?: string;
  /** 单词内容 */
  content: string;
  /** 备选答案1 */
  option1: string;
  /** 备选答案2 */
  option2: string;
  /** 备选答案3 */
  option3: string;
  /** 备选答案4 */
  option4: string;
  /** 备选答案5 */
  option5: string;
  /** 正确答案：1-备选答案1，2-备选答案2，3-备选答案3，4-备选答案4，5-备选答案5 */
  answer: number;
};

export type StudentPractiseQuestionBankPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 类型 0-评测 1-练习 */
  type?: string;
  /** 级别 */
  level?: string;
  /** 所属版本 */
  version?: string;
  /** 所属年级 */
  grade?: string;
  /** 所属单元 */
  unit?: string;
};

export type StudentVipConfigBaseRespVO = {
  /** id */
  id?: number;
  /** vip权益内容 */
  content?: string;
  /** 价格 */
  price?: number;
  /** 状态:0-下架，1-上架 */
  status?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
};

export type StudentVipConfigCreateReqVO = {
  /** vip权益内容 */
  content: string;
  /** 状态:0-下架，1-上架 */
  status: string;
  /** 价格 */
  price: number;
};

export type StudentVipConfigGetReqVO = {
  /** id */
  id: number;
};

export type StudentVipConfigPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 状态:0-下架，1-上架 */
  status?: string;
};

export type StuUserCreateReqVO = {
  /** 推广机构id */
  promotionOrgId?: number;
  /** 学生姓名 */
  studentName: string;
  /** 家长姓名 */
  parentsName: string;
  /** 家长电话 */
  parentsPhone: string;
  /** 学校 */
  school: string;
  /** 年级 详见枚举 学生年级：1一年级 2二年级 3三年级 */
  grade: string;
  /** 性别 */
  sex?: string;
  /** 年龄 */
  age?: number;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
};

export type StuUserGetReqVO = {
  /** 学生id */
  id: number;
};

export type StuUserGetRespVO = {
  /** 学生ID */
  id?: number;
  /** 账户 */
  account?: string;
  /** 推广机构 */
  promotionOrg?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 */
  grade?: string;
  /** 是否参加体验课 0 未参加  1 已参加 */
  attendExperienceClass?: string;
  /** 水平等级：1-初级, 2-中级, 3-高级, 4-雅思  */
  evaluateLevel?: string;
  /** 是否vip: 0 非vip,1 vip */
  isVip?: string;
  /** vip到期时间 */
  vipExpireTime?: string;
  /** vip权益内容 */
  vipContent?: string;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
  createTime?: string;
  updateTime?: string;
};

export type StuUserModifyReqVO = {
  /** 学生ID */
  id: number;
  /** 推广机构id */
  promotionOrgId?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 详见枚举 学生年级 */
  grade?: string;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
};

export type StuUserPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 账户 */
  account?: string;
  /** 推广机构名称:如果传空字符串就查所有的，如果传null就查没有机构的 */
  promotionOrg?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 详见枚举 学生年级 */
  grade?: number;
};

export type StuUserPageRespVO = {
  /** 学生id */
  id?: number;
  /** 账户 */
  account?: string;
  /** 推广机构 */
  promotionOrg?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 */
  grade?: string;
  createTime?: string;
  updateTime?: string;
  /** 30分钟课时剩余次数 */
  thirtyMinutesRemainNumber?: number;
  /** 60分钟课时剩余次数 */
  sixtyMinutesRemainNumber?: number;
  /** 评测水平等级：1-初级, 2-中级, 3-高级, 4-雅思  */
  evaluateLevel?: string;
  /** 是否vip: 0 非vip,1 vip */
  isVip?: string;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
};

export type SysOrgCreateReqVO = {
  /** 登录账号 */
  account: string;
  /** 推广机构名称 */
  name: string;
  /** 负责人 */
  commander: string;
  /** 负责人电话 */
  mobile: string;
  /** 机构状态 :0-停用 1-启用、2-待初审、3-初审通过 */
  status: string;
  /** 机构类型：1 - 一级代理，2 - 二级代理 */
  orgType: string;
  /** 父机构id */
  parentId?: number;
  /** 分佣比例 */
  oneIncome: number;
  /** 下级分佣比例 */
  twoIncome?: number;
};

export type SysOrgDetialRespVO = {
  /** 机构ID */
  id?: number;
  /** 登录账号 */
  account?: string;
  /** 机构编号 */
  orgNumber?: string;
  /** 推广机构名称 */
  name?: string;
  /** 负责人 */
  commander?: string;
  /** 负责人电话 */
  mobile?: string;
  /** 机构状态 :0-停用 1-启用、2-待初审、3-初审通过 */
  status?: string;
  /** 机构类型：1 - 一级代理，2 - 二级代理 */
  orgType?: string;
  /** 推广码 */
  promotionCode?: string;
  /** 父机构id */
  parentId?: number;
  /** 父机构名称 */
  parentName?: string;
  /** 学生属于本级机构，机构抽成比例 */
  oneIncome?: number;
  /** 学生属于下级机构，上级机构抽成比例 */
  twoIncome?: number;
  /** 创建时间 */
  createTime?: string;
  /** 创建者 */
  creator?: string;
  /** 角色类型 */
  roleId?: string;
  /** 菜单权限 */
  menu?: string[];
};

export type SysOrgGetReqVO = {
  /** 机构ID */
  id: number;
};

export type SysOrgModifyReqVO = {
  /** 机构ID */
  id: number;
  /** 登录账号 */
  account: string;
  /** 推广机构名称 */
  name: string;
  /** 负责人 */
  commander: string;
  /** 负责人电话 */
  mobile: string;
  /** 机构状态 :0-停用 1-启用、2-待初审、3-初审通过 */
  status: string;
  /** 机构类型：1 - 一级代理，2 - 二级代理 */
  orgType: string;
  /** 父机构id */
  parentId?: number;
  /** 分佣比例 */
  oneIncome: number;
  /** 下级分佣比例 */
  twoIncome?: number;
};

export type SysOrgPageReqVO = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 登录账号 */
  account?: string;
  /** 机构编号 */
  orgNumber?: string;
  /** 推广机构名称 */
  name?: string;
  /** 父机构名称 */
  parentName?: string;
  /** 负责人 */
  commander?: string;
  /** 负责人电话 */
  mobile?: string;
  /** 机构状态 :0-停用 1-启用、2-待初审、3-初审通过 */
  status?: string;
  /** 机构类型：1 - 一级代理，2 - 二级代理 */
  orgType?: string;
};

export type SysOrgStuUserCreateReqVO = {
  /** 学生姓名 */
  studentName: string;
  /** 家长姓名 */
  parentsName: string;
  /** 家长电话 */
  parentsPhone: string;
  /** 学校 */
  school: string;
  /** 年级 详见枚举 学生年级：1一年级 2二年级 3三年级 */
  grade: string;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
};

export type SysOrgStuUserModifyReqVO = {
  /** 学生ID */
  id: number;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 详见枚举 学生年级 */
  grade?: string;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
};

export type SysOrgStuUserPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 账户 */
  account?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 详见枚举 学生年级 */
  grade?: number;
};

export type SysOrgWalletDetailsRespVo = {
  /** 创建时间 */
  time?: string;
  /** 类型 1收入2提款 */
  type?: number;
  /** 资金 */
  price?: number;
  /** 用户名称 */
  userName?: string;
  /** 场景 类型 1消课2提现 */
  scene?: number;
};

export type SysOrgWalletIdDetailsReqVo = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 机构ID */
  id: number;
};

export type SysOrgWalletInfoRespVO = {
  /** 总分佣 */
  totalIncome?: number;
  /** 今日分佣 */
  todayIncome?: number;
  /** 可提现分佣 */
  mayIncome?: number;
};

export type SysPriceConfigDO = {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  id?: number;
  /** 课时名称 */
  durationName?: string;
  /** 课时类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType?: number;
  /** 课时价格 */
  price?: number;
  /** 课时折扣（8折显示为80） */
  discount?: number;
  /** 课时折扣价格 */
  discountPrice?: number;
  /** 备注 */
  remark?: string;
  /** 价格配置状态:0-下架，1-上架 */
  status?: string;
};

export type SystemCaclTotalPriceReqVO = {
  /** 课时类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType: number;
  /** 课时数量 */
  durationCount: number;
};

export type SystemDictAddReqVO = {
  /** 字典名称 */
  name?: string;
  /** 字典类型 */
  type?: string;
  /** 字典数据明细集合 */
  datas?: DictData[];
  /** 备注 */
  remark?: string;
};

export type SystemDictDataReqVO = {
  /** 字典类型  */
  type?: string;
};

export type SystemDictDataRespVO = {
  /** 字典标签 男 */
  label?: string;
  /** 字典值 1 */
  value?: string;
};

export type SystemDictDetialRespVO = {
  /** 字典名称 */
  name?: string;
  /** 字典类型 */
  type?: string;
  /** 字典数据明细集合 */
  datas?: DictData[];
  /** 备注 */
  remark?: string;
  /** 字典 ID */
  id?: number;
};

export type SystemDictModifyReqVO = {
  /** 字典名称 */
  name?: string;
  /** 字典类型 */
  type?: string;
  /** 字典数据明细集合 */
  datas?: DictData[];
  /** 备注 */
  remark?: string;
  /** 字典 ID */
  id?: number;
};

export type SystemDictPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 字典类型 */
  type?: string;
};

export type SystemDictRemoveReqVO = {
  /** 字典 ID */
  id?: number;
};

export type SystemMenuAddRespVO = {
  /** 菜单id */
  id?: number;
  /** 菜单名称 */
  name: string;
  /** 显示顺序 */
  sort?: number;
  /** 父菜单ID */
  parentId?: number;
  /** 菜单图标 */
  icon?: string;
  /** 是否可见（true可见 false隐藏） */
  visible: boolean;
};

export type SystemMenuBaseRespVO = {
  /** 菜单id */
  id?: number;
  /** 菜单名称 */
  name: string;
  /** 显示顺序 */
  sort?: number;
  /** 父菜单ID */
  parentId?: number;
  /** 菜单图标 */
  icon?: string;
  /** 是否可见（true可见 false隐藏） */
  visible: boolean;
};

export type SystemMenuDO = {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  /** 菜单ID */
  id?: number;
  /** 菜单名称 */
  name?: string;
  /** 显示顺序 */
  sort?: number;
  /** 父菜单ID */
  parentId?: number;
  /** 菜单图标 */
  icon?: string;
  /** 是否可见 true可见 false隐藏 */
  visible?: boolean;
};

export type SystemMenuGetReqVO = {
  /** 菜单id */
  id?: number;
};

export type SystemMenuModifyRespVO = {
  /** 菜单id */
  id?: number;
  /** 菜单名称 */
  name: string;
  /** 显示顺序 */
  sort?: number;
  /** 父菜单ID */
  parentId?: number;
  /** 菜单图标 */
  icon?: string;
  /** 是否可见（true可见 false隐藏） */
  visible: boolean;
};

export type SystemMenuPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
};

export type SystemMenuRemoveReqVO = {
  /** 菜单id */
  id?: number;
};

export type SystemMenuTreeRespVO = {
  /** 菜单id */
  id?: number;
  /** 菜单名称 */
  name: string;
  /** 显示顺序 */
  sort?: number;
  /** 父菜单ID */
  parentId?: number;
  /** 菜单图标 */
  icon?: string;
  /** 是否可见（true可见 false隐藏） */
  visible: boolean;
  /** 子级菜单 */
  children?: SystemMenuTreeRespVO[];
};

export type SystemPriceConfigActiveReqVO = {
  /** 价格配置ID */
  id: number;
};

export type SystemPriceConfigCreateReqVO = {
  /** 价格配置名称 */
  durationName?: string;
  /** 课时类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType: string;
  /** 课时价格 */
  price: number;
  /** 课时折扣价格 */
  discountPrice: number;
  /** 备注 */
  remark?: string;
  /** 价格配置状态：0-下架，1-上架 */
  status: string;
};

export type SystemPriceConfigGetReqVO = {
  /** 课时类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType?: number;
};

export type SystemPriceConfigPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 课时类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType?: number;
  /** 价格配置状态:0-下架，1-上架 */
  status?: string;
};

export type SystemRoleAllocMenuReqVO = {
  /** 角色ID */
  roleId: number;
  /** 权限列表 */
  menuIds: string[];
  /** 角色标识 */
  code: string;
};

export type SystemRoleDO = {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  id?: number;
  /** 角色名称 */
  name?: string;
  /** 角色编码: SYS-系统管理, TEC-老师, STU-学生, OPE-运营, FIN-财务, ORG-机构 */
  code?: string;
  /** 角色状态（enable - 正常 ，disable -停用） */
  status?: string;
  /** 角色类型:0-系统管理, 1-老师, 2-学生, 3-运营, 4-财务, 5-机构 */
  type?: number;
  /** 备注 */
  remark?: string;
  /** 角色拥有的权限集合 */
  paths?: string[];
};

export type SystemRoleGetMenuListReqVO = {
  /** 角色ID */
  roleId: number;
};

export type SystemRolePageRespVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 角色类型:TEC-老师,STU-学生,OPE-运营,SYS-系统管理,FIN-财务,ORG-机构 */
  code: string;
  /** 备注 */
  remark?: string;
  /** 角色状态 */
  status?: number;
};

export type SystemUserAddReqVO = {
  /** 用户账号 */
  username?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 备注 */
  remark?: string;
  /** 用户邮箱 */
  email?: string;
  /** 手机号码 */
  mobile?: string;
  /** 用户性别 male-男，female-女 */
  sex?: string;
  /** 用户头像 */
  avatar?: string;
  /** 角色code */
  roleId?: string;
};

export type SystemUserBaseRespVO = {
  /** 用户ID */
  id?: number;
  /** 用户账号 */
  username?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 备注 */
  remark?: string;
  /** 用户邮箱 */
  email?: string;
  /** 手机号码 */
  mobile?: string;
  /** 用户性别 1-男，2-女 */
  sex?: string;
  /** 用户头像 */
  avatar?: string;
  /** 帐号状态 enable - 正常 ，disable -停用 */
  status?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
  /** 角色类型 */
  roleId?: string;
  /** 菜单权限 */
  menu?: string[];
};

export type SystemUserGetReqVO = {
  /** 用户id */
  id?: number;
};

export type SystemUserModifyReqVO = {
  /** 主键id */
  id: number;
  /** 用户账号 */
  username?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 备注 */
  remark?: string;
  /** 用户邮箱 */
  email?: string;
  /** 手机号码 */
  mobile?: string;
  /** 用户性别 male-男，female-女 */
  sex?: string;
  /** 用户头像 */
  avatar?: string;
};

export type SystemUserPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 用户账号 */
  username?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 手机号码 */
  mobile?: string;
  /** 用户性别 male-男，female-女 */
  sex?: string;
  /** 状态 enable - 正常 ，disable -停用 */
  status?: string;
  /** 邮箱 */
  email?: string;
};

export type SystemUserRemoveReqVO = {
  /** 用户id */
  id?: number;
};

export type TeacherAndStudentRespVO = {
  /** 创建生成的账号 */
  account?: string;
  /** 创建生成的密码 */
  password?: string;
};

export type TeacherComplaintBaseRespVO = {
  /** 投诉id */
  id?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 投诉内容 */
  content?: string;
  /** 投诉状态：0-未处理，1-已处理 */
  status?: string;
  /** 处理方案 */
  handleScheme?: string;
  /** 投诉时间 */
  createTime?: string;
};

export type TeacherComplaintGetReqVO = {
  /** 投诉id */
  id: number;
};

export type TeacherComplaintHandleReqVO = {
  /** 投诉id */
  id: number;
  /** 处理方案 */
  handleScheme: string;
};

export type TeacherComplaintPageReqVO = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 投诉状态：0-未处理，1-已处理 */
  status?: string;
};

export type TeacherEvaluateBaseRespVO = {
  /** 评价id */
  id?: number;
  /** 上课计划id */
  attendClassPlanId?: number;
  /** 学生id */
  studentId?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师id */
  teacherId?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 发音得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  pronunciationScore?: number;
  /** 态度得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  attitudeScore?: number;
  /** 感染力得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  appealScore?: number;
  /** 责任心得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  responsibilityScore?: number;
  /** 创建时间 */
  createTime?: string;
  /** 创建人 */
  creator?: string;
};

export type TeacherEvaluateGetReqVO = {
  /** 评价记录id */
  id: number;
};

export type TeacherEvaluatePageReqVO = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生姓名 */
  studentName?: string;
};

export type TeacherIncomeConfigBaseRespVO = {
  /** id */
  id?: number;
  /** 老师等级:1，2，3，4，5 */
  level?: string;
  /** 老师等级名称:A，B，C，D，E */
  levelName?: string;
  /** 30分钟单课时抽成百分比，例如：10表示10% */
  thirtyIncomeRatio?: number;
  /** 60分钟单课时抽成百分比，例如：10表示10% */
  sixtyIncomeRatio?: number;
  /** 体验课单课时抽成百分比，例如：10表示10% */
  experienceIncomeRatio?: number;
  /** 状态:0-下架，1-上架 */
  status?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
};

export type TeacherIncomeConfigCreateReqVO = {
  /** 老师等级:1-A，2-B，3-C，4-D，5-E */
  level: string;
  /** 状态:0-下架，1-上架 */
  status: string;
  /** 30分钟单课时抽成百分比，例如：10表示10% */
  thirtyIncomeRatio: number;
  /** 60分钟单课时抽成百分比，例如：10表示10% */
  sixtyIncomeRatio: number;
  /** 体验课单课时抽成百分比，例如：10表示10% */
  experienceIncomeRatio: number;
};

export type TeacherIncomeConfigGetReqVO = {
  /** id */
  id: number;
};

export type TeacherIncomeConfigPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 状态:0-下架，1-上架 */
  status?: string;
};

export type TeacherUserDO = {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  id?: number;
  /** 老师等级:1-A，2-B，3-C，4-D，5-E */
  level?: string;
  /** 老师账号 */
  account?: string;
  /** 密码 */
  password?: string;
  /** 老师姓名 */
  name?: string;
  /** 老师编号 */
  userNo?: string;
  /** 老师考核状态（0=待考核，1=已通过，2=未通过） */
  examineSuccess?: string;
  /** 陪同方式 */
  accompanyWay?: string;
  /** 在职情况 */
  employmentStatus?: string;
  /** 职务 */
  position?: string;
  /** 技能 */
  skill?: string;
  /** 职业头衔 */
  jobTitle?: string;
  /** 服务机构 */
  serviceOrg?: string;
  /** 注册机构 */
  registOrg?: string;
  /** 身份证号 */
  idNumber?: string;
  /** 性别（1=男，2=女） */
  sex?: string;
  /** 年龄 */
  age?: number;
  /** 紧急联系人 */
  emergencyContact?: string;
  /** 紧急联系电话 */
  emergencyContactPhone?: string;
  /** 微信账号 */
  wechat?: string;
  /** 所在学校 */
  school?: string;
  /** 年级 */
  grade?: string;
  /** 班级 */
  classes?: string;
  /** 专业 */
  major?: string;
  /** 证明人 */
  certifier?: string;
  /** 籍贯 */
  nativePlace?: string;
  /** 推荐人 */
  reference?: string;
  /** 个人简介 */
  introduction?: string;
  /** 手机号码 */
  mobile?: string;
  /** 审核状态（需根据业务定义具体状态码） */
  auditStatus?: string;
  /** 教师等级 */
  userLevel?: number;
  /** 体验课授课资格（0=不能，1=能） */
  isTrialAvailable?: number;
  /** 账户状态（0=正常，1=停用） */
  status?: string;
  /** 累计已带学生数 */
  studentCount?: number;
  /** 累计已授单词数 */
  wordCount?: number;
  /** 累计评分 */
  score?: number;
  /** 学历 */
  education?: string;
};

export type testHistoryUsingGetParams = {
  from: string;
  to: string;
};

export type testInfoUsingGetParams = {
  from: string;
};

export type testRegisterUsingPostParams = {
  userId: string;
  nick?: string;
  avatar?: string;
};

export type testSendUsingPostParams = {
  from: string;
  to: string;
  text: string;
};

export type testTestGetChatResponseUsingGetParams = {
  content: string;
};

export type TrainingRecordCreatReqVO = {
  /** 培训审核记录名称 */
  name?: string;
  /** 培训审核类型：0 初审，1 复审 */
  trainingType: string;
  /** 老师id */
  teacherId: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 审核结果：是否通过 0 不通过，1 通过 */
  isPassed: string;
  /** 备注 */
  remark?: string;
  /** 老师等级:1-A，2-B，3-C，4-D，5-E */
  level?: string;
};

export type TrainingRecordDetialRespVO = {
  /** 培训审核记录ID */
  id?: number;
  /** 培训审核记录名称 */
  name?: string;
  /** 培训审核类型：0 初审，1 复审 */
  trainingType: string;
  /** 老师id */
  teacherId?: number;
  /** 老师名称 */
  teacherNameAccount?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 审核结果：是否通过 0 不通过，1 通过 */
  isPassed?: string;
  /** 备注 */
  remark?: string;
  /** 初审人 */
  firstReviewer?: string;
  /** 二审人 */
  secondReviewer?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建人 */
  creator?: string;
  /** 更新人 */
  updater?: string;
};

export type TrainingRecordGetReqVO = {
  /** 培训审核记录id */
  id: number;
};

export type TrainingRecordPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 培训审核名称 */
  name?: string;
  /** 审核类型：0 初审，1 复审 */
  trainingType?: number;
  /** 老师id */
  teacherId?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 培训结果：是否通过 0 未通过，1 已通过 */
  isPassed?: number;
};

export type UserCreateReqVO = {
  /** 姓名 */
  name: string;
  /** 考核状态（0=待考核，1=已通过,2=未通过） */
  examineSuccess?: string;
  /** 身份证号 */
  idNumber: string;
  /** 性别（1=男，2=女） */
  sex: string;
  /** 紧急联系人 */
  emergencyContact: string;
  /** 紧急联系电话 */
  emergencyContactPhone: string;
  /** 微信账号 */
  wechat?: string;
  /** 所在学校 */
  school: string;
  /** 专业 */
  major: string;
  /** 个人简介 */
  introduction?: string;
  /** 手机号码 */
  mobile: string;
  /** 学历 */
  education?: string;
};

export type UserGetReqVO = {
  /** 老师id */
  id: number;
};

export type UserModifyReqVO = {
  /** 老师id */
  id: number;
  /** 老师姓名 */
  name: string;
  /** 老师考核状态（0=待考核，1=已通过，2=未通过） */
  examineSuccess?: string;
  /** 身份证号 */
  idNumber: string;
  /** 性别（1=男，2=女） */
  sex: string;
  /** 紧急联系人 */
  emergencyContact: string;
  /** 紧急联系电话 */
  emergencyContactPhone: string;
  /** 微信账号 */
  wechat?: string;
  /** 所在学校 */
  school: string;
  /** 个人简介 */
  introduction?: string;
  /** 手机号码 */
  mobile: string;
  /** 学历 */
  education?: string;
};

export type UserPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 姓名 */
  name?: string;
  /** 手机号 */
  mobile?: string;
  /** 审核状态 */
  auditStatus?: number;
  /** 账户 */
  account?: string;
};
