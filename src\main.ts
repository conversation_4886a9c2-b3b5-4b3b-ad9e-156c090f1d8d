import '@/style/index.scss'
import { VueQueryPlugin } from '@tanstack/vue-query'
import 'virtual:uno.css'
import { createSSRApp } from 'vue'

import App from './App.vue'
import { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'
import store, { useDictStore } from './store'

export function createApp() {
  const app = createSSRApp(App)

  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(VueQueryPlugin)

  // 初始化字典数据
  const initDict = async () => {
    const dictStore = useDictStore()
    // 从服务器获取字典数据
    await dictStore.fetchDictData()
  }
  // 在应用启动时初始化字典数据
  initDict()
  return {
    app,
  }
}
