/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemMenuManagement';
import * as API from './types';

/** 【系统管理】添加菜单信息 POST /pc/system/menu/create */
export function usePcSystemMenuCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemMenuCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、运营、财务、机构、老师、学生】获取指定菜单信息 POST /pc/system/menu/get */
export function usePcSystemMenuGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSystemMenuBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemMenuGetUsingPost,
    onSuccess(data: API.ResultSystemMenuBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理】修改指定菜单信息 POST /pc/system/menu/modify */
export function usePcSystemMenuModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemMenuModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、运营、财务、机构、老师、学生】菜单列表分页 POST /pc/system/menu/page */
export function usePcSystemMenuPageUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponsePageResultSystemMenuDO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemMenuPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSystemMenuDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理】删除指定菜单信息 POST /pc/system/menu/remove */
export function usePcSystemMenuRemoveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemMenuRemoveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理】树形结构返回有权限菜单列表 POST /pc/system/menu/treeList */
export function usePcSystemMenuTreeListUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListSystemMenuTreeRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemMenuTreeListUsingPost,
    onSuccess(data: API.ResultListSystemMenuTreeRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
