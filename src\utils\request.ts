import { CustomRequestOptions } from '@/interceptors/request'
import { navigateTo, navigateToSub, redirectTo, showToast } from '.'

/**
 * 请求方法: 主要是对 uni.request 的封装，去适配 openapi-ts-request 的 request 方法
 * @param options 请求参数
 * @returns 返回 Promise 对象
 */
const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          if (typeof res.data === 'object' && res.data !== null && 'code' in res.data) {
            const data = res.data as { code: number; message?: string }
            if (data.code !== 200 && !options.hideErrorToast) {
              console.log(data, 253)
              showToast(data.message || '请求错误')
              if (data.code === 401) {
                // 401 未授权
                navigateToSub('/role-select/role-select?to=login')
              }
              // 业务状态码不为 200 时，也应该 reject Promise
              reject(data)
              return
            }
          }
          resolve(res.data as T)
        } else if (res.statusCode === 401) {
          navigateToSub('/role-select/role-select?to=login')
          reject(res)
        } else {
          if (
            typeof res.data === 'object' &&
            res.data !== null &&
            'code' in res.data &&
            (res.data as { code: number }).code === 401
          ) {
            // 401 未授权
            navigateToSub('/role-select/role-select?to=login')
            return
          }
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            showToast(
              (typeof res.data === 'object' &&
                res.data !== null &&
                'message' in res.data &&
                (res.data as { message: string }).message) ||
                '请求错误',
            )
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        showToast('网络错误，换个网络试试')
        reject(err)
      },
    })
  })
}

/*
 * openapi-ts-request 工具的 request 跨客户端适配方法
 */
export default function request<T = { code: number; message: string; data: any }>(
  url: string,
  options: Omit<CustomRequestOptions, 'url'> & {
    params?: Record<string, unknown>
    headers?: Record<string, unknown>
  },
) {
  const requestOptions = {
    url: '/api' + url,
    ...options,
  }

  if (options.params) {
    requestOptions.query = requestOptions.params
    delete requestOptions.params
  }
  const token = uni.getStorageSync('token')
  requestOptions.header = {
    ...requestOptions.header,
    Authorization: `Bearer ${token}`,
    token: `${token}`,
    ...options.headers,
  }
  delete requestOptions.headers
  return http<T>(requestOptions)
}
