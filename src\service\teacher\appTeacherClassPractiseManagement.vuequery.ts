/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appTeacherClassPractiseManagement';
import * as API from './types';

/** 【老师】生成课堂试卷 POST /app/teacher/classpractis/generateRandomPractice */
export function useClasspractisGenerateRandomPracticeUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisGenerateRandomPracticeUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】随机生成用于课后练习的单词集合 POST /app/teacher/classpractis/generateRandomPracticeAfterClass */
export function useClasspractisGenerateRandomPracticeAfterClassUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListString) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisGenerateRandomPracticeAfterClassUsingPost,
    onSuccess(data: API.ResultListString) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】课堂试卷题目分页查询 POST /app/teacher/classpractis/practisDetailPage */
export function useClasspractisPractisDetailPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStudentPractiseDetailBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisPractisDetailPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultStudentPractiseDetailBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】课堂试卷分页查询 POST /app/teacher/classpractis/practisPage */
export function useClasspractisPractisPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStudentPractiseBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisPractisPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultStudentPractiseBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
