/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemOrgWalletManagement';
import * as API from './types';

/** 机构钱包明细 POST /org/wallet/details */
export function useOrgWalletDetailsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultSysOrgWalletDetailsRespVo
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orgWalletDetailsUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSysOrgWalletDetailsRespVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 指定id机构钱包明细 POST /org/wallet/details/id */
export function useOrgWalletDetailsIdUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultSysOrgWalletDetailsRespVo
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orgWalletDetailsIdUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSysOrgWalletDetailsRespVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 机构钱包信息 POST /org/wallet/info */
export function useOrgWalletInfoUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSysOrgWalletInfoRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orgWalletInfoUsingPost,
    onSuccess(data: API.ResultSysOrgWalletInfoRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 查询指定id机构钱包信息 POST /org/wallet/info/id */
export function useOrgWalletInfoIdUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSysOrgWalletInfoRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orgWalletInfoIdUsingPost,
    onSuccess(data: API.ResultSysOrgWalletInfoRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
