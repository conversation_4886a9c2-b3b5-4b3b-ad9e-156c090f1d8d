import { defineStore } from 'pinia'
import { ref } from 'vue'
export enum RoleEmu {
  Teacher = 'teacher',
  Student = 'student',
}
export const useRoleStore = defineStore(
  'role',
  () => {
    const role = ref<RoleEmu>(RoleEmu.Teacher)

    const setRole = (type: RoleEmu) => {
      role.value = type
    }
    const getRole = () => {
      return role.value
    }
    return {
      role,
      getRole,
      setRole,
    }
  },
  {
    persist: true,
  },
)
