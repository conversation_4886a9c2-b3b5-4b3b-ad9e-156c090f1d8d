/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './coursewareManagement';
import * as API from './types';

/** 【运营】添加基础课件资料信息 POST /pc/courseware/create */
export function usePcCoursewareCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】下载课件基础资料模板 POST /pc/courseware/downTemplate */
export function usePcCoursewareDownTemplateUsingPostMutation(options?: {
  onSuccess?: (value?: string) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareDownTemplateUsingPost,
    onSuccess(data: string) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】查询基础课件资料详情信息 POST /pc/courseware/get */
export function usePcCoursewareGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCoursewareByAdminPageRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareGetUsingPost,
    onSuccess(data: API.ResultCoursewareByAdminPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】批量导入课件基础资料 POST /pc/courseware/import */
export function usePcCoursewareOpenApiImportUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareOpenApiImportUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】修改基础课件资料信息 POST /pc/courseware/modify */
export function usePcCoursewareModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询课程资料信息 POST /pc/courseware/page */
export function usePcCoursewarePageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareByAdminPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewarePageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultCoursewareByAdminPageRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】删除基础课件资料信息 POST /pc/courseware/remove */
export function usePcCoursewareRemoveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareRemoveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
