<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'common',
  style: {
    navigationStyle: 'custom',
    navigationBarBackgroundColor: '#FFFFFF',
    backgroundColor: '#F5F5F5',
  },
}
</route>
<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 顶部用户信息区域 -->
    <view class="bg-white px-32rpx py-48rpx">
      <view class="flex items-center justify-between mb-32rpx">
        <!-- 左侧用户信息 -->
        <view class="flex items-center">
          <!-- 头像 -->
          <view class="w-120rpx h-120rpx mr-32rpx">
            <image
              :src="teacherInfo?.avatar || '/static/images/default-avatar.png'"
              class="w-full h-full rounded-full object-cover"
              mode="aspectFill"
            />
          </view>
          <!-- 用户信息 -->
          <view>
            <view class="text-32rpx font-600 text-gray-800 mb-8rpx">
              Hi, {{ teacherInfo?.name || '李木子' }}
            </view>
            <view class="text-24rpx text-gray-500">
              {{ getGreeting() }}
            </view>
          </view>
        </view>
        <!-- 右侧消息图标 -->
        <view class="relative cursor-pointer" @click="goToMessageList">
          <view class="w-48rpx h-48rpx bg-blue-100 rounded-full flex items-center justify-center">
            <view class="text-24rpx">💬</view>
          </view>
          <!-- 消息数量徽章 -->
          <view
            v-if="unreadCount > 0"
            class="absolute -top-8rpx -right-8rpx w-32rpx h-32rpx bg-red-500 rounded-full flex items-center justify-center"
          >
            <view class="text-white text-20rpx font-600">
              {{ unreadCount > 9 ? '9+' : unreadCount }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据统计区域 -->
    <view class="bg-white mx-32rpx rounded-16rpx p-32rpx mb-32rpx shadow-sm mt-4">
      <view class="flex justify-between">
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.newStudents || 30 }}
          </view>
          <view class="text-24rpx text-gray-500">学生新增</view>
        </view>
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.todayClasses || 30 }}
          </view>
          <view class="text-24rpx text-gray-500">今日上课</view>
        </view>
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.todayRevenue || 30 }}
          </view>
          <view class="text-24rpx text-gray-500">今日收益</view>
        </view>
      </view>
    </view>

    <!-- 今日预约标题 -->
    <view class="mx-32rpx mb-24rpx">
      <view class="flex items-center justify-between">
        <view class="text-32rpx font-600 text-gray-800">今日预约</view>
        <view class="text-24rpx text-blue-500 cursor-pointer" @click="handleMoreBookings">
          更多
        </view>
      </view>
    </view>

    <!-- 课程列表 -->
    <view class="mx-32rpx">
      <view v-if="courseList && courseList.length > 0" class="space-y-16rpx">
        <view
          v-for="(item, index) in courseList"
          :key="item.id"
          class="bg-white rounded-16rpx p-32rpx shadow-sm"
        >
          <!-- 时间和学生信息 -->
          <view class="flex items-center justify-between mb-24rpx">
            <view class="text-28rpx font-600 text-gray-800">
              {{ formatClassDate(item.classDate) }} {{ formatTime(item.startTime) }}-{{
                formatTime(item.endTime)
              }}
            </view>
            <view class="text-24rpx text-gray-600">学生：{{ item.studentName || '--' }}</view>
          </view>

          <!-- 课程详情 -->
          <view class="bg-gray-50 rounded-12rpx p-24rpx mb-24rpx">
            <view class="text-24rpx text-gray-600 mb-8rpx">
              课程名称：{{ item.coursewareName ?? '--' }}
            </view>
            <view class="text-24rpx text-gray-600 mb-8rpx">
              腾讯会议：{{ item.tencentMeetingNo ?? '待设置' }} | {{ '备用' }}
              {{ item.tencentMeetingNoTwo ?? '待设置' }}
            </view>
            <view class="text-24rpx text-gray-600">ID：{{ item.id || '' }}</view>
          </view>

          <!-- 操作按钮 -->
          <view class="flex items-center justify-end gap-16rpx">
            <wd-button
              size="small"
              type="success"
              @click="goEvaluationPage"
              custom-class="h-64rpx px-24rpx rounded-12rpx text-24rpx"
            >
              去测评
            </wd-button>
            <wd-button
              v-if="item.progress == '2'"
              size="small"
              type="success"
              @click="handleViewTrainingResult(item.id)"
              custom-class="h-64rpx px-24rpx rounded-12rpx text-24rpx"
            >
              训练结果
            </wd-button>

            <template v-else>
              <wd-select-picker
                v-model="coursewareId"
                type="radio"
                use-default-slot
                :columns="columns"
                @confirm="handleConfirm"
              >
                <view
                  @click="(e) => item.tencentMeetingNo || e.stopPropagation()"
                  class="flex items-center"
                >
                  <wd-button
                    size="small"
                    type="primary"
                    :disabled="!item.tencentMeetingNo"
                    custom-class="h-64rpx px-24rpx rounded-12rpx text-24rpx"
                    @click="(e) => handleGoToClass(item, e)"
                  >
                    {{ item.progress === '0' ? '去上课' : '进行中' }}
                  </wd-button>
                </view>
              </wd-select-picker>

              <wd-button
                size="small"
                type="primary"
                @click="prompt(item.id)"
                custom-class="h-64rpx px-24rpx rounded-12rpx text-24rpx"
              >
                设置会议号
              </wd-button>
            </template>
          </view>
        </view>
      </view>

      <!-- 无预约时显示 -->
      <view v-else class="bg-white rounded-16rpx p-80rpx text-center shadow-sm">
        <view
          class="w-120rpx h-120rpx bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-24rpx"
        >
          <view class="text-48rpx">📅</view>
        </view>
        <view class="text-28rpx text-gray-500 mb-16rpx">今日暂无预约课程</view>
        <view class="text-24rpx text-gray-400">请稍后查看或联系管理员</view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="h-120rpx"></view>

    <!-- 设置腾讯会议弹窗 -->
    <wd-message-box class="tencentMeeting" selector="tencentMeetingNo">
      <view class="px-32rpx py-24rpx">
        <!-- 腾讯会议号1 -->
        <view class="mb-32rpx">
          <view class="text-28rpx text-gray-800 mb-16rpx">腾讯会议号1</view>
          <wd-input
            v-model="meetingForm.tencentMeetingNo"
            placeholder="请输入"
            custom-class="bg-gray-50 rounded-12rpx h-80rpx"
            custom-input-class="text-28rpx"
          />
        </view>

        <!-- 腾讯会议号2 -->
        <view class="mb-32rpx">
          <view class="text-28rpx text-gray-800 mb-16rpx">腾讯会议号2</view>
          <wd-input
            v-model="meetingForm.tencentMeetingNoTwo"
            placeholder="请输入"
            custom-class="bg-gray-50 rounded-12rpx h-80rpx"
            custom-input-class="text-28rpx"
          />
        </view>
      </view>
    </wd-message-box>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { navigateToCoursewareDetail, navigateToSub, showToast } from '@/utils'
import {
  attendclassCoursewarePageUsingPost,
  attendclassListTodayUsingPost,
  attendclassSetCoursewareUsingPost,
  attendclassSetTencentMeetingNoUsingPost,
  attendclassStartClassPlanUsingPost,
} from '@/service/teacher/appTeacherAttendClassPlanManagement'
import { baseinfoCurrentUsingPost } from '@/service/teacher/appTeacherBaseInfoManagement'
import { useDictPage } from '@/hooks/useDictPage'
import { useMessage } from 'wot-design-uni'
import { walletInfoUsingPost } from '@/service/teacher'
import { useChatStore } from '@/store/chat'

defineOptions({
  name: 'Teacher',
})

// 响应式数据
const unreadCount = ref(0)
const lastMessage = ref('')
const teacherInfo = ref()
const courseList = ref([])
const coursewareId = ref(0)
const dict = ref()
const columns = ref()
const selectRow = ref()

// 统计数据
const stats = ref({
  newStudents: 30,
  todayClasses: 30,
  todayRevenue: 30,
})

// 设置腾讯会议弹窗相关

const meetingForm = ref({
  tencentMeetingNo: '',
  tencentMeetingNoTwo: '',
})

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好，开始美好的一天吧！'
  if (hour < 18) return '下午好，继续加油！'
  return '晚上好，辛苦了！'
}
const goEvaluationPage = () => {
  navigateToSub('/evaluation-page/evaluation-page')
}
// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '00:00'
  // 如果是完整的时间字符串，提取时分
  if (timeStr.includes(':')) {
    return timeStr.substring(0, 5) // 取前5位 HH:MM
  }
  return timeStr
}
const formatClassDate = (dateStr: string) => {
  if (!dateStr) return ''
  const parts = dateStr.split('-')
  if (parts.length === 3) {
    return `${parts[1]}-${parts[2]}`
  }
  return dateStr
}
// 获取状态样式类
const getStatusClass = (progress: string) => {
  switch (progress) {
    case '0':
      return 'bg-orange-100 text-orange-600'
    case '1':
      return 'bg-blue-100 text-blue-600'
    case '2':
      return 'bg-green-100 text-green-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

// 获取状态文本
const getStatusText = (progress: string) => {
  switch (progress) {
    case '0':
      return '未开始'
    case '1':
      return '进行中'
    case '2':
      return '已完成'
    default:
      return '未知'
  }
}
// 处理更多预约
const handleMoreBookings = () => {
  showToast('跳转到更多预约页面')
  // navigateToSub('/more-bookings/more-bookings')
}

// 处理腾讯会议弹窗确认
const handleMeetingConfirm = async (id: number) => {
  if (!meetingForm.value.tencentMeetingNo || !meetingForm.value.tencentMeetingNoTwo) {
    showToast('请完善会议号')
    return
  }
  try {
    const res = await attendclassSetTencentMeetingNoUsingPost({
      body: {
        id,
        ...meetingForm.value,
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('会议号设置成功')
      // 更新课程列表中的会议号
      // 刷新课程列表
      fetchTeacherData()
    } else {
      showToast(res.message || '会议号设置失败')
    }
  } catch (error) {
    console.error('设置会议号失败:', error)
    showToast('设置会议号失败，请稍后重试')
  }
  // 重置表单
  resetMeetingForm()
}

// 处理腾讯会议弹窗取消
const resetMeetingForm = () => {
  meetingForm.value = {
    tencentMeetingNo: '',
    tencentMeetingNoTwo: '',
  }
}

const chatStore = useChatStore()
if (!chatStore.isReady) {
  chatStore.login(uni.getStorageSync('userID'), uni.getStorageSync('userSig'))
}
const init = (chat) => {
  setTimeout(async () => {
    const res = await chat.getConversationList()
    let time = 0
    unreadCount.value = res.data.conversationList.reduce((pre, item) => {
      if (time < item.lastMessage.lastTime) {
        time = item.lastMessage.lastTime
        lastMessage.value = item.lastMessage.payload.text
      }
      return pre + item.unreadCount
    }, 0)
  }, 500)
}
const handleConfirm = async () => {
  const res = await attendclassSetCoursewareUsingPost({
    body: {
      id: selectRow.value.id,
      coursewareId: coursewareId.value,
    },
  })
  if (res.code === 200) {
    showToast('设置成功')
    fetchTeacherData()
  }
}
const handleStartClass = async (id) => {
  try {
    const res = await attendclassStartClassPlanUsingPost({ body: { id } })
  } catch (err) {
    console.log('上课失败')
  }
}
// 处理去上课按钮点击
const handleGoToClass = (item, e) => {
  selectRow.value = item
  const { isToDoAntiForget, coursewareId, id: classId, isDoneAntiForget } = item
  // 需要将当前学生老师id存储下来
  uni.setStorageSync('studentId', item.studentId)
  if (!coursewareId) {
    showToast('缺少课程资料ID')
    return ''
  }
  if (item.progress === '0') {
    handleStartClass(item.id)
  }
  e.stopPropagation()
  navigateToCoursewareDetail(coursewareId, isToDoAntiForget, isDoneAntiForget, classId)
}
// 跳转到消息列表
const goToMessageList = () => {
  navigateToSub('/message-list/message-list')
}
const handleViewTrainingResult = (attendClassPlanId: number) => {
  navigateToSub(`/practise-result/practise-result?attendClassPlanId=${attendClassPlanId}`)
}
// 获取老师信息和课程列表
const fetchTeacherData = async () => {
  try {
    const resCourseware = await attendclassCoursewarePageUsingPost({
      body: {
        pageNo: 1,
        pageSize: 10000,
        name: '',
      },
    })
    columns.value = resCourseware.data.items.map((item) => ({ label: item.name, value: item.id }))

    const walletRes = await walletInfoUsingPost({})
    console.log(walletRes)

    // 获取老师信息
    const userRes: any = await baseinfoCurrentUsingPost({})
    console.log('老师详情接口返回:', userRes)

    // 根据接口返回结构处理数据
    if (userRes && userRes.code === 200) {
      // 如果返回的是DataWrapper结构
      if (userRes.data) {
        teacherInfo.value = { ...userRes.data.item, todayRevenue: walletRes.data.item.todayRevenue }
      }

      console.log('解析后的老师信息:', teacherInfo.value)
    }

    // 获取字典数据
    dict.value = await useDictPage()
    console.log(dict.value, 'dict.value')

    // 获取当天课程
    const courseRes: any = await attendclassListTodayUsingPost({})
    console.log('课程列表接口返回:', courseRes)

    if (courseRes && courseRes.code === 200) {
      // 如果返回的是item数组
      if (courseRes.data && courseRes.data.items) {
        courseList.value = courseRes.data.items
      }

      console.log('解析后的课程列表:', courseList.value)
    }

    // 更新统计数据
    stats.value = {
      newStudents: teacherInfo.value?.studentCount || 30,
      todayClasses: courseList.value?.length || 30,
      todayRevenue: teacherInfo.value?.todayRevenue || 30,
    }
  } catch (error) {
    console.error('获取老师数据失败:', error)
  }
}
const message = useMessage('tencentMeetingNo')
// 定义item的类型 - 修改为使用自定义弹窗
function prompt(id: number) {
  message
    .confirm({
      title: '设置腾讯会议号',
    })
    .then(() => {
      handleMeetingConfirm(id)
      console.log('设置成功了')
    })
    .catch((err) => {
      console.log(err)
    })
}
onMounted(() => {
  fetchTeacherData()
})

onShow(() => {
  chatStore.getChatIns().then(init)
})
</script>

<style>
.main-title-color {
  color: #d14328;
}

:deep(.wd-message-box .wd-message-box__container) {
  width: 550rpx;
}
</style>
