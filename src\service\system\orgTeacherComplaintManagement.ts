/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【机构】查询投诉详情 POST /pc/orgteacher/complaint/get */
export async function pcOrgteacherComplaintGetUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherComplaintBaseRespVO>(
    '/pc/orgteacher/complaint/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【机构】处理投诉 POST /pc/orgteacher/complaint/handle */
export async function pcOrgteacherComplaintHandleUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintHandleReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/orgteacher/complaint/handle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【机构】分页查询投诉列表 POST /pc/orgteacher/complaint/page */
export async function pcOrgteacherComplaintPageUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultTeacherComplaintBaseRespVO>(
    '/pc/orgteacher/complaint/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
