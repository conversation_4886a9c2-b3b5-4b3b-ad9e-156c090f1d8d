/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentBaseInfoManagement';
import * as API from './types';

/** 【学生】获取当前登录学生信息 POST /app/student/baseinfo/current */
export function useBaseinfoCurrentUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStuUserGetRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoCurrentUsingPost,
    onSuccess(data: API.ResultStuUserGetRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】学生使用账号密码登录 返回值: default response POST /app/student/baseinfo/login */
export function useBaseinfoLoginUsingPostMutation(options?: {
  onSuccess?: (value?: API.UserLoginVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoLoginUsingPost,
    onSuccess(data: API.UserLoginVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】学生注册 POST /app/student/baseinfo/register */
export function useBaseinfoRegisterUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherAndStudentRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoRegisterUsingPost,
    onSuccess(data: API.ResultTeacherAndStudentRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
