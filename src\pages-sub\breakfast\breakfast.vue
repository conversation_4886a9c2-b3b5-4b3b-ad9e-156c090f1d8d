<route lang="json5">
{
  style: {
    navigationBarTitleText: 'breakfast',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- 主内容区域 -->
    <view class="flex-1 flex flex-col items-center justify-center px-48rpx">
      <!-- 标题 -->
      <view class="text-64rpx font-600 text-#333 text-center mb-32rpx">
        breakfast
      </view>

      <!-- 提示文字 -->
      <view class="text-24rpx text-#999 text-center mb-80rpx">备注：错误状态和正确状态</view>

      <!-- 选项区域 -->
      <view class="w-full space-y-24rpx">
        <!-- 休息选项 -->
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center mb-24rpx cursor-pointer transition-all duration-300 bg-#fce4ec border-2 border-#f8bbd9"
          :class="{
            'bg-#e3f2fd border-#2196f3 scale-98': selectedOption === 1 && !showResult,
            'bg-#e8f5e8 border-#4caf50': showResult && correctAnswer === 1,
            'bg-#fce4ec border-#f44336': showResult && selectedOption === 1 && correctAnswer !== 1,
          }"
          @click="selectOption(1)"
        >
          <text 
            class="text-32rpx font-500"
            :class="{
              'text-#333': !showResult || (showResult && correctAnswer !== 1),
              'text-#2e7d32': showResult && correctAnswer === 1,
              'text-#c62828': showResult && selectedOption === 1 && correctAnswer !== 1,
            }"
          >
            休息
          </text>
        </view>

        <!-- 呼吸选项 -->
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center mb-24rpx cursor-pointer transition-all duration-300 bg-#e8f5e8 border-2 border-#c8e6c9"
          :class="{
            'bg-#e3f2fd border-#2196f3 scale-98': selectedOption === 2 && !showResult,
            'bg-#e8f5e8 border-#4caf50': showResult && correctAnswer === 2,
            'bg-#fce4ec border-#f44336': showResult && selectedOption === 2 && correctAnswer !== 2,
          }"
          @click="selectOption(2)"
        >
          <text 
            class="text-32rpx font-500"
            :class="{
              'text-#333': !showResult || (showResult && correctAnswer !== 2),
              'text-#2e7d32': showResult && correctAnswer === 2,
              'text-#c62828': showResult && selectedOption === 2 && correctAnswer !== 2,
            }"
          >
            呼吸
          </text>
        </view>

        <!-- 胸部选项 -->
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center mb-24rpx cursor-pointer transition-all duration-300 bg-#f3f4f6 border-2 border-#d1d5db"
          :class="{
            'bg-#e3f2fd border-#2196f3 scale-98': selectedOption === 3 && !showResult,
            'bg-#e8f5e8 border-#4caf50': showResult && correctAnswer === 3,
            'bg-#fce4ec border-#f44336': showResult && selectedOption === 3 && correctAnswer !== 3,
          }"
          @click="selectOption(3)"
        >
          <text 
            class="text-32rpx font-500"
            :class="{
              'text-#333': !showResult || (showResult && correctAnswer !== 3),
              'text-#2e7d32': showResult && correctAnswer === 3,
              'text-#c62828': showResult && selectedOption === 3 && correctAnswer !== 3,
            }"
          >
            胸部
          </text>
        </view>

        <!-- 早晨选项 -->
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center mb-24rpx cursor-pointer transition-all duration-300 bg-#f3f4f6 border-2 border-#d1d5db"
          :class="{
            'bg-#e3f2fd border-#2196f3 scale-98': selectedOption === 4 && !showResult,
            'bg-#e8f5e8 border-#4caf50': showResult && correctAnswer === 4,
            'bg-#fce4ec border-#f44336': showResult && selectedOption === 4 && correctAnswer !== 4,
          }"
          @click="selectOption(4)"
        >
          <text 
            class="text-32rpx font-500"
            :class="{
              'text-#333': !showResult || (showResult && correctAnswer !== 4),
              'text-#2e7d32': showResult && correctAnswer === 4,
              'text-#c62828': showResult && selectedOption === 4 && correctAnswer !== 4,
            }"
          >
            早晨
          </text>
        </view>

        <!-- 品种选项 -->
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center mb-24rpx cursor-pointer transition-all duration-300 bg-#f3f4f6 border-2 border-#d1d5db"
          :class="{
            'bg-#e3f2fd border-#2196f3 scale-98': selectedOption === 5 && !showResult,
            'bg-#e8f5e8 border-#4caf50': showResult && correctAnswer === 5,
            'bg-#fce4ec border-#f44336': showResult && selectedOption === 5 && correctAnswer !== 5,
          }"
          @click="selectOption(5)"
        >
          <text 
            class="text-32rpx font-500"
            :class="{
              'text-#333': !showResult || (showResult && correctAnswer !== 5),
              'text-#2e7d32': showResult && correctAnswer === 5,
              'text-#c62828': showResult && selectedOption === 5 && correctAnswer !== 5,
            }"
          >
            品种
          </text>
        </view>

        <!-- 不认识选项 -->
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center mb-24rpx cursor-pointer transition-all duration-300 bg-#f5f5f5 border-2 border-#e0e0e0"
          :class="{
            'bg-#e3f2fd border-#2196f3 scale-98': selectedOption === 6 && !showResult,
            'bg-#e8f5e8 border-#4caf50': showResult && correctAnswer === 6,
            'bg-#fce4ec border-#f44336': showResult && selectedOption === 6 && correctAnswer !== 6,
          }"
          @click="selectOption(6)"
        >
          <text 
            class="text-32rpx font-500"
            :class="{
              'text-#333': !showResult || (showResult && correctAnswer !== 6),
              'text-#2e7d32': showResult && correctAnswer === 6,
              'text-#c62828': showResult && selectedOption === 6 && correctAnswer !== 6,
            }"
          >
            不认识
          </text>
        </view>
      </view>
    </view>

    <!-- 底部提示和进度 -->
    <view class="px-48rpx pb-48rpx">
      <!-- 倒计时提示 -->
      <view class="text-center text-24rpx text-#999 mb-32rpx">
        停留页面超过10s，建议选择不认识
      </view>

      <!-- 进度和统计 -->
      <view class="flex items-center justify-between">
        <!-- 左侧进度 -->
        <view class="text-28rpx text-#666">
          {{ currentQuestion }}/{{ totalQuestions }}
        </view>

        <!-- 右侧统计 -->
        <view class="flex items-center gap-32rpx">
          <!-- 正确数量 -->
          <view class="flex items-center">
            <view class="w-32rpx h-32rpx rounded-full bg-#4caf50 mr-16rpx flex items-center justify-center">
              <text class="text-16rpx text-white">✓</text>
            </view>
            <text class="text-28rpx text-#4caf50 font-600">{{ correctCount }}</text>
          </view>

          <!-- 错误数量 -->
          <view class="flex items-center">
            <view class="w-32rpx h-32rpx rounded-full bg-#f44336 mr-16rpx flex items-center justify-center">
              <text class="text-16rpx text-white">✗</text>
            </view>
            <text class="text-28rpx text-#f44336 font-600">{{ incorrectCount }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const selectedOption = ref<number | null>(null)
const showResult = ref(false)
const correctAnswer = ref(2) // 假设正确答案是"呼吸"
const currentQuestion = ref(23)
const totalQuestions = ref(34)
const correctCount = ref(20)
const incorrectCount = ref(3)

// 定时器相关
const timer = ref<NodeJS.Timeout | null>(null)
const timeSpent = ref(0)

// 选择选项
const selectOption = (option: number) => {
  if (showResult.value) return
  
  selectedOption.value = option
  showResult.value = true
  
  // 清除定时器
  if (timer.value) {
    clearInterval(timer.value)
  }
  
  // 模拟答题结果处理
  setTimeout(() => {
    // 这里可以添加跳转到下一题的逻辑
    console.log('选择了选项:', option)
  }, 1500)
}

// 页面加载时启动定时器
onMounted(() => {
  timer.value = setInterval(() => {
    timeSpent.value += 1
    // 10秒后自动提示选择"不认识"
    if (timeSpent.value >= 10 && !selectedOption.value) {
      // 可以在这里添加提示逻辑
    }
  }, 1000)
})

// 页面卸载时清除定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>
