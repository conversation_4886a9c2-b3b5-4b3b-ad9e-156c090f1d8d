/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】查询投诉详情 POST /pc/teacher/complaint/get */
export async function pcTeacherComplaintGetUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherComplaintBaseRespVO>(
    '/pc/teacher/complaint/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】处理指定投诉 POST /pc/teacher/complaint/handle */
export async function pcTeacherComplaintHandleUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintHandleReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/teacher/complaint/handle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】分页查询投诉列表 POST /pc/teacher/complaint/page */
export async function pcTeacherComplaintPageUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultTeacherComplaintBaseRespVO>(
    '/pc/teacher/complaint/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
