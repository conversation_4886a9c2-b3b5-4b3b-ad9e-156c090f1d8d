<route lang="json5">
{
  style: {
    navigationBarTitleText: '训练结果',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="training-result-page">
    <!-- 页面头部 -->
    <view class="header-section">
      <view class="header-title">训练情况</view>
      <view class="header-subtitle">学习成果统计</view>
    </view>

    <!-- 训练结果卡片 -->
    <view class="result-card">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="info-item">
          <view class="info-label">用户姓名：</view>
          <view class="info-value">{{ trainingResult.studentName || '加载中...' }}</view>
        </view>

        <view class="info-item">
          <view class="info-label">陪练姓名：</view>
          <view class="info-value">{{ trainingResult.teacherName || '加载中...' }}</view>
        </view>

        <view class="info-item">
          <view class="info-label">训练名称：</view>
          <view class="info-value training-name">
            {{ trainingResult.coursewareName || '加载中...' }}
          </view>
        </view>
      </view>

      <!-- 学习统计 -->
      <view class="stats-section">
        <view class="stats-title">学习统计</view>

        <view class="stats-grid">
          <view class="stat-item total">
            <view class="stat-number">{{ trainingResult.newWordsCount || 0 }}</view>
            <view class="stat-label">学新单词总数</view>
          </view>

          <view class="stat-item correct">
            <view class="stat-number">{{ trainingResult.newWordsCorrectCount || 0 }}</view>
            <view class="stat-label">学新正确数</view>
          </view>

          <view class="stat-item error">
            <view class="stat-number">{{ trainingResult.newWordsErrorCount || 0 }}</view>
            <view class="stat-label">学新错误数</view>
          </view>
        </view>

        <!-- 正确率显示 -->
        <view class="accuracy-section" v-if="false">
          <view class="accuracy-label">正确率</view>
          <view class="accuracy-value">{{ accuracyRate }}%</view>
          <view class="accuracy-bar">
            <view class="accuracy-fill" :style="{ width: accuracyRate + '%' }"></view>
          </view>
        </view>
      </view>

      <!-- 时间信息 -->
      <view class="time-section">
        <view class="time-item">
          <view class="time-label">开始时间：</view>
          <view class="time-value">{{ formatTime(trainingResult.startTime) }}</view>
        </view>

        <view class="time-item">
          <view class="time-label">结束时间：</view>
          <view class="time-value">{{ formatTime(trainingResult.endTime) }}</view>
        </view>

        <view class="time-item">
          <view class="time-label">学习时长：</view>
          <view class="time-value duration">{{ learningDuration }}</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section" v-if="role === 'student'">
      <wd-button type="primary" size="large" class="action-btn evaluate-btn" @click="goToEvaluate">
        去评价
      </wd-button>

      <wd-button
        type="warning"
        size="large"
        class="action-btn complaint-btn"
        @click="goToComplaint"
      >
        去投诉
      </wd-button>
    </view>

    <!-- 加载状态 -->
    <wd-loading v-if="loading" :loading="loading" />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import { navigateToSub, showToast } from '@/utils'
import { attendclassStatisticsUsingPost } from '@/service/student/appStudentAttendClassPlanManagement'
import { attendclassStatisticsUsingPost as teacherUsingPost } from '@/service/teacher/appTeacherAttendClassPlanManagement'
import type { AttendClassPlanStatisticsRespVO } from '@/service/student/types'
import { useRoleStore } from '@/store'

const { getRole } = useRoleStore()
const role = ref(getRole())
defineOptions({
  name: 'TrainingResult',
})

// 页面参数
const attendClassPlanId = ref<number | null>(null)

// 训练结果数据
const trainingResult = ref<AttendClassPlanStatisticsRespVO>({
  studentName: '',
  teacherName: '',
  coursewareName: '',
  newWordsCount: 0,
  newWordsCorrectCount: 0,
  newWordsErrorCount: 0,
  startTime: '',
  endTime: '',
})

// 加载状态
const loading = ref(true)

// 计算正确率
const accuracyRate = computed(() => {
  if (!trainingResult.value.newWordsCount || trainingResult.value.newWordsCount === 0) return 0
  return Math.round(
    (trainingResult.value.newWordsCorrectCount! / trainingResult.value.newWordsCount) * 100,
  )
})

// 计算学习时长
const learningDuration = computed(() => {
  if (!trainingResult.value.startTime || !trainingResult.value.endTime) return '计算中...'

  const start = dayjs(trainingResult.value.startTime)
  const end = dayjs(trainingResult.value.endTime)
  const duration = end.diff(start, 'minute')

  if (duration < 60) {
    return `${duration}分钟`
  } else {
    const hours = Math.floor(duration / 60)
    const minutes = duration % 60
    return `${hours}小时${minutes}分钟`
  }
})

// 格式化时间
const formatTime = (timeStr: string | undefined) => {
  if (!timeStr) return '暂无'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 获取训练结果数据
const getTrainingResult = async () => {
  if (!attendClassPlanId.value) {
    showToast('缺少课程计划ID')
    loading.value = false
    return
  }

  try {
    loading.value = true
    const fn = role.value === 'teacher' ? teacherUsingPost : attendclassStatisticsUsingPost
    const response = await fn({
      body: {
        id: attendClassPlanId.value,
      },
    })
    console.log('训练结果API响应:', response)
    if (response.code === 200 && response.data?.item) {
      trainingResult.value = response.data.item
    }
  } finally {
    loading.value = false
  }
}

// 去评价
const goToEvaluate = () => {
  if (!attendClassPlanId.value) {
    showToast('缺少必要参数，无法进行评价')
    return
  }

  // 跳转到评价页面，传递必要参数
  navigateToSub(
    `/evaluate/evaluate?attendClassPlanId=${attendClassPlanId.value}&teacherId=${trainingResult.value.teacherId}`,
  )
}

// 去投诉
const goToComplaint = () => {
  if (!attendClassPlanId.value) {
    showToast('缺少必要参数，无法进行投诉')
    return
  }

  // 跳转到投诉页面，传递必要参数
  navigateToSub(`/complaint/complaint?attendClassPlanId=${attendClassPlanId.value}`)
}

onLoad((options: any) => {
  console.log('页面参数:', options)

  if (options.attendClassPlanId) {
    attendClassPlanId.value = parseInt(options.attendClassPlanId)
  }

  getTrainingResult()
})

onMounted(() => {
  // 页面挂载后的逻辑
})
</script>

<style lang="scss" scoped>
.training-result-page {
  min-height: 100vh;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header-section {
  margin-bottom: 40rpx;
  text-align: center;

  .header-title {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
  }

  .header-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.result-card {
  padding: 40rpx;
  margin-bottom: 40rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.info-section {
  margin-bottom: 40rpx;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      min-width: 160rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .info-value {
      flex: 1;
      font-size: 28rpx;
      color: #333333;

      &.training-name {
        font-weight: 500;
        color: #667eea;
      }
    }
  }
}

.stats-section {
  margin-bottom: 40rpx;

  .stats-title {
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .stats-grid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .stat-item {
      flex: 1;
      padding: 24rpx 16rpx;
      margin: 0 8rpx;
      text-align: center;
      border-radius: 16rpx;

      &.total {
        color: #ffffff;
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      &.correct {
        color: #ffffff;
        background: linear-gradient(135deg, #56ab2f, #a8e6cf);
      }

      &.error {
        color: #ffffff;
        background: linear-gradient(135deg, #ff6b6b, #ffa8a8);
      }

      .stat-number {
        margin-bottom: 8rpx;
        font-size: 48rpx;
        font-weight: bold;
      }

      .stat-label {
        font-size: 24rpx;
        opacity: 0.9;
      }
    }
  }

  .accuracy-section {
    text-align: center;

    .accuracy-label {
      margin-bottom: 16rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .accuracy-value {
      margin-bottom: 16rpx;
      font-size: 56rpx;
      font-weight: bold;
      color: #667eea;
    }

    .accuracy-bar {
      width: 100%;
      height: 12rpx;
      overflow: hidden;
      background: #f0f0f0;
      border-radius: 6rpx;

      .accuracy-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 6rpx;
        transition: width 0.3s ease;
      }
    }
  }
}

.time-section {
  .time-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .time-label {
      min-width: 160rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .time-value {
      flex: 1;
      font-size: 28rpx;
      color: #333333;

      &.duration {
        font-weight: 500;
        color: #667eea;
      }
    }
  }
}

.action-section {
  display: flex;
  gap: 24rpx;

  .action-btn {
    flex: 1;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;

    &.evaluate-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
    }

    &.complaint-btn {
      background: linear-gradient(135deg, #ff6b6b, #ffa8a8);
      border: none;
    }
  }
}
</style>
