import dayjs from 'dayjs'

/**
 * 格式化消息时间
 * @param time 时间
 * @returns 格式化后的时间字符串
 */
export const formatMessageTime = (time: Date | string | number): string => {
  const messageTime = new Date(time)
  const now = new Date()

  // 如果是今天，只显示时间
  if (dayjs(now).isSame(messageTime, 'day')) {
    return dayjs(time).format('HH:mm')
  }
  // 如果是昨天，显示昨天
  if (dayjs(now).subtract(1, 'day').isSame(messageTime, 'day')) {
    return `昨天 ${dayjs(time).format('HH:mm')}`
  }
  // 其他显示完整日期
  return dayjs(time).format('MM-DD HH:mm')
}

/**
 * 生成消息ID
 * @returns 消息ID
 */
export const generateMessageId = (): number => {
  return Date.now() + Math.random()
}

/**
 * 检查消息是否为空
 * @param text 消息文本
 * @returns 是否为空
 */
export const isMessageEmpty = (text: string): boolean => {
  return !text || text.trim().length === 0
}

/**
 * 截断消息文本
 * @param text 消息文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export const truncateMessage = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength) + '...'
}

/**
 * 获取用户显示名称
 * @param userInfo 用户信息
 * @param fallback 默认值
 * @returns 显示名称
 */
export const getUserDisplayName = (userInfo: any, fallback: string = '用户'): string => {
  if (!userInfo) return fallback

  return userInfo.nickname || userInfo.name || userInfo.userID || fallback
}

/**
 * 获取用户头像
 * @param userInfo 用户信息
 * @param fallback 默认头像
 * @returns 头像URL
 */
export const getUserAvatar = (userInfo: any, fallback: string = ''): string => {
  if (!userInfo) return fallback

  return userInfo.avatar || fallback
}

/**
 * 构建聊天页面URL
 * @param receiverUserID 接收者用户ID
 * @param receiverName 接收者昵称
 * @param receiverAvatar 接收者头像
 * @returns 聊天页面URL
 */
export const buildChatUrl = (
  receiverUserID: string,
  receiverName?: string,
  receiverAvatar?: string,
): string => {
  const params = new URLSearchParams()
  params.append('receiverUserID', receiverUserID)

  if (receiverName) {
    params.append('receiverName', encodeURIComponent(receiverName))
  }

  if (receiverAvatar) {
    params.append('receiverAvatar', encodeURIComponent(receiverAvatar))
  }

  return `/pages-sub/chat/chat?${params.toString()}`
}

/**
 * 跳转到聊天页面
 * @param receiverUserID 接收者用户ID
 * @param receiverName 接收者昵称
 * @param receiverAvatar 接收者头像
 */
export const navigateToChat = (
  receiverUserID: string,
  receiverName?: string,
  receiverAvatar?: string,
): void => {
  const url = buildChatUrl(receiverUserID, receiverName, receiverAvatar)
  uni.navigateTo({ url })
}

/**
 * 检查是否为有效的用户ID
 * @param userID 用户ID
 * @returns 是否有效
 */
export const isValidUserID = (userID: string): boolean => {
  return userID && userID.trim().length > 0
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}
