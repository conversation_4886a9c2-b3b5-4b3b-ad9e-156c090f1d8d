/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】获取当前登录学生信息 POST /app/student/baseinfo/current */
export async function baseinfoCurrentUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStuUserGetRespVO>('/app/student/baseinfo/current', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【学生】学生使用账号密码登录 返回值: default response POST /app/student/baseinfo/login */
export async function baseinfoLoginUsingPost({
  body,
  options,
}: {
  body: API.UserLoginReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.UserLoginVo>('/app/student/baseinfo/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生】学生注册 POST /app/student/baseinfo/register */
export async function baseinfoRegisterUsingPost({
  body,
  options,
}: {
  body: API.UserRegisterReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherAndStudentRespVO>(
    '/app/student/baseinfo/register',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
