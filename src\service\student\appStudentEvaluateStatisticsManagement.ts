/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】获取评测统计 POST /app/student/evaluatestatistics/get */
export async function evaluatestatisticsGetUsingPost({
  body,
  options,
}: {
  body: API.WordStatisticsReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultWordStatisticsRespVO>(
    '/app/student/evaluatestatistics/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
