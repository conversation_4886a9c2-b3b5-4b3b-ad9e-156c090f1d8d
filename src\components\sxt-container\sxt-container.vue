<template>
  <scroll-view
    :scroll-y="true"
    :class="`h-100vh relative bg-[#F5F6FA] ${className}`"
    @scroll="handleScroll"
    @scrolltolower="onScrolltolower"
  >
    <view class="bg-gradient-primary w-full h-[100px] !absolute !top-0" v-if="showHeaderBg"></view>
    <view class="h-screen relative">
      <view :style="`height:${safeAreaInsets + 44}px`">
        <wd-navbar
          :bordered="false"
          custom-class="!bg-transparent  !pb-2 "
          :left-arrow="!isTab"
          fixed
          :title="title"
          safeAreaInsetTop
          @click-left="handleClickLeft"
          :custom-style="`background-color: rgba(255,255,255, ${opacity})!important;padding-top:${safeAreaInsets}px`"
        >
          <template #left v-if="$slots['nav-left']">
            <slot name="nav-left"></slot>
          </template>
          <template #title v-if="$slots['nav-title']">
            <slot name="nav-title"></slot>
          </template>
          <template #right v-if="$slots['nav-right']">
            <slot name="nav-right"></slot>
          </template>
        </wd-navbar>
      </view>
      <slot></slot>
    </view>
  </scroll-view>
</template>

<script setup>
import { navigateBack } from '@/utils'
import PLATFORM from '@/utils/platform'
import { useNavigation } from '@/hooks'
const { title, getCurrentPage } = useNavigation()
const { isTab = false } = getCurrentPage()
const opacity = ref(0)
defineProps({
  style: {
    type: [Object, String],
    default: () => ({}),
  },
  showHeaderBg: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
  },
})
const handleClickLeft = () => {
  navigateBack()
}

const emit = defineEmits(['scroll', 'scrolltolower'])

const safeAreaInsets = computed(() => {
  let top = 0
  const { isH5, isApp, isMp, isMpWeixin, isMpAplipay, isMpToutiao } = PLATFORM
  if (isH5 || isMp) {
    top = 10
  } else if (isApp) {
    top = 0
  }
  return top
})
const handleScroll = (e) => {
  if (e.detail.scrollTop > 100) {
    opacity.value = 1
  } else {
    opacity.value = e.detail.scrollTop / 100
  }
  emit('scroll', e)
}
const onScrolltolower = (event) => {
  emit('scrolltolower', event)
}
</script>
