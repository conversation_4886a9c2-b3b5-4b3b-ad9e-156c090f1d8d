/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 拉取消息记录 GET /test/history */
export async function testHistoryUsingGet({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testHistoryUsingGetParams;
  options?: CustomRequestOptions;
}) {
  return request<string>('/test/history', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取用户信息 GET /test/info */
export async function testInfoUsingGet({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testInfoUsingGetParams;
  options?: CustomRequestOptions;
}) {
  return request<string>('/test/info', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 注册 POST /test/register */
export async function testRegisterUsingPost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testRegisterUsingPostParams;
  options?: CustomRequestOptions;
}) {
  return request<string>('/test/register', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 发送消息 POST /test/send */
export async function testSendUsingPost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testSendUsingPostParams;
  options?: CustomRequestOptions;
}) {
  return request<string>('/test/send', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 测试AI GET /test/testGetChatResponse */
export async function testTestGetChatResponseUsingGet({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testTestGetChatResponseUsingGetParams;
  options?: CustomRequestOptions;
}) {
  return request<string>('/test/testGetChatResponse', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
