<route lang="json5">
{
  style: {
    navigationBarTitleText: '评价老师',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="evaluate-page">
    <!-- 页面头部 -->
    <view class="header-section">
      <view class="header-title">评价老师</view>
      <view class="header-subtitle">请对本次课程进行评价</view>
    </view>

    <!-- 评价表单 -->
    <view class="form-card">
      <!-- 老师信息 -->
      <view class="teacher-info">
        <view class="teacher-avatar">
          <wd-icon name="user" size="60rpx" color="#667eea"></wd-icon>
        </view>
        <view class="teacher-details">
          <view class="teacher-name">评价老师：{{ teacherName }}</view>
          <view class="class-time">上课时间：{{ classDate }}</view>
        </view>
      </view>

      <!-- 评价项目 -->
      <view class="rating-section">
        <view class="section-title">请为老师打分</view>

        <!-- 发音评分 -->
        <view class="rating-item">
          <view class="rating-label">发音</view>
          <view class="rating-stars">
            <wd-rate
              v-model="formData.pronunciationScore"
              @change="onRatingChange('pronunciation', $event)"
            />
            <view class="rating-text">{{ getRatingText(formData.pronunciationScore) }}</view>
          </view>
        </view>

        <!-- 态度评分 -->
        <view class="rating-item">
          <view class="rating-label">态度</view>
          <view class="rating-stars">
            <wd-rate
              v-model="formData.attitudeScore"
              @change="onRatingChange('attitude', $event)"
            />
            <view class="rating-text">{{ getRatingText(formData.attitudeScore) }}</view>
          </view>
        </view>

        <!-- 感染力评分 -->
        <view class="rating-item">
          <view class="rating-label">感染力</view>
          <view class="rating-stars">
            <wd-rate v-model="formData.appealScore" @change="onRatingChange('appeal', $event)" />
            <view class="rating-text">{{ getRatingText(formData.appealScore) }}</view>
          </view>
        </view>

        <!-- 责任心评分 -->
        <view class="rating-item">
          <view class="rating-label">责任心</view>
          <view class="rating-stars">
            <wd-rate
              v-model="formData.responsibilityScore"
              @change="onRatingChange('responsibility', $event)"
            />
            <view class="rating-text">{{ getRatingText(formData.responsibilityScore) }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <wd-button
        type="primary"
        size="large"
        class="submit-btn"
        :loading="submitting"
        @click="submitEvaluation"
      >
        提交评价
      </wd-button>
    </view>

    <!-- 加载状态 -->
    <wd-loading v-if="loading" :loading="loading" />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import { showToast, navigateBack } from '@/utils'
import { evaluateCreateUsingPost } from '@/service/student/appStudentEvaluateManagement'
import { attendclassGetUsingPost } from '@/service/student/appStudentAttendClassPlanManagement'
import type {
  TeacherEvaluateCreateReqVO,
  AttendClassPlanDetialRespVO,
} from '@/service/student/types'

defineOptions({
  name: 'Evaluate',
})

// 页面参数
const attendClassPlanId = ref<number | null>(null)
const teacherId = ref<number | null>(null)

// 老师和课程信息
const teacherName = ref('张老师')
const classDate = ref('2025-08-30 到 2025-09-01')

// 表单数据
const formData = ref<TeacherEvaluateCreateReqVO>({
  attendClassPlanId: 0,
  teacherId: 0,
  pronunciationScore: 5,
  attitudeScore: 5,
  appealScore: 5,
  responsibilityScore: 5,
})

// 加载和提交状态
const loading = ref(false)
const submitting = ref(false)

// 获取评分文本
const getRatingText = (score: string) => {
  const scoreNum = parseInt(score)
  const texts = ['', '很差', '较差', '一般', '良好', '优秀']
  return texts[scoreNum] || '未评分'
}

// 评分变化处理
const onRatingChange = (type: string, value: number) => {
  console.log(`${type} 评分变化:`, value)
}

// 获取课程信息
const getClassInfo = async () => {
  if (!attendClassPlanId.value) return

  try {
    loading.value = true

    const response = await attendclassGetUsingPost({
      body: {
        id: attendClassPlanId.value,
      },
    })

    if (response.code === 200 && response.data?.item) {
      const classInfo = response.data.item
      teacherName.value = classInfo.teacherName || '张老师'

      // 设置teacherId
      if (classInfo.teacherId) {
        teacherId.value = classInfo.teacherId
        formData.value.teacherId = classInfo.teacherId
      }

      // 格式化课程时间
      if (classInfo.classDate) {
        const startTime = classInfo.startTime
        const endTime = classInfo.endTime
        classDate.value = `${classInfo.classDate} ${startTime} - ${endTime}`
      }
    }
  } catch (error) {
    console.error('获取课程信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 提交评价
const submitEvaluation = async () => {
  console.log('提交评价:', formData.value, attendClassPlanId.value, teacherId.value)
  if (!attendClassPlanId.value || !teacherId.value) {
    showToast('缺少必要参数')
    return
  }

  try {
    submitting.value = true

    const response = await evaluateCreateUsingPost({
      body: {
        attendClassPlanId: attendClassPlanId.value,
        teacherId: teacherId.value,
        pronunciationScore: formData.value.pronunciationScore,
        attitudeScore: formData.value.attitudeScore,
        appealScore: formData.value.appealScore,
        responsibilityScore: formData.value.responsibilityScore,
      },
    })

    if (response.code === 200) {
      showToast('评价提交成功')
      setTimeout(() => {
        navigateBack()
      }, 1500)
    } else {
      showToast('评价提交失败，请重试')
    }
  } catch (error) {
    console.error('提交评价失败:', error)
    showToast('评价提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

onLoad((options: any) => {
  console.log('页面参数:', options)

  if (options.attendClassPlanId) {
    attendClassPlanId.value = parseInt(options.attendClassPlanId)
    formData.value.attendClassPlanId = attendClassPlanId.value
  }

  if (options.teacherId) {
    teacherId.value = parseInt(options.teacherId)
    formData.value.teacherId = teacherId.value
  }

  getClassInfo()
})

onMounted(() => {
  // 页面挂载后的逻辑
})
</script>

<style lang="scss" scoped>
.evaluate-page {
  min-height: 100vh;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header-section {
  margin-bottom: 40rpx;
  text-align: center;

  .header-title {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
  }

  .header-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-card {
  padding: 40rpx;
  margin-bottom: 40rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.teacher-info {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 40rpx;
  background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
  border-radius: 16rpx;

  .teacher-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100rpx;
    margin-right: 24rpx;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
  }

  .teacher-details {
    flex: 1;

    .teacher-name {
      margin-bottom: 12rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }

    .class-time {
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.rating-section {
  margin-bottom: 40rpx;

  .section-title {
    margin-bottom: 32rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .rating-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .rating-label {
      min-width: 120rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
    }

    .rating-stars {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;

      .rating-text {
        min-width: 80rpx;
        margin-left: 16rpx;
        font-size: 24rpx;
        color: #666666;
        text-align: center;
      }
    }
  }
}

.overall-rating {
  padding: 32rpx;
  color: #ffffff;
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;

  .overall-label {
    margin-bottom: 16rpx;
    font-size: 28rpx;
    opacity: 0.9;
  }

  .overall-score {
    margin-bottom: 8rpx;
    font-size: 56rpx;
    font-weight: bold;
  }

  .overall-text {
    font-size: 24rpx;
    opacity: 0.8;
  }
}

.submit-section {
  .submit-btn {
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 44rpx;
  }
}
</style>
