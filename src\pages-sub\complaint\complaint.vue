<route lang="json5">
{
  style: {
    navigationBarTitleText: '投诉',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="complaint-page">
    <!-- 页面头部 -->
    <view class="header-section">
      <view class="header-title">投诉</view>
      <view class="header-subtitle">请详细描述您遇到的问题</view>
    </view>

    <!-- 投诉表单 -->
    <view class="form-card">
      <!-- 课程信息 -->
      <view class="class-info">
        <view class="info-title">课程信息</view>
        <view class="info-item">
          <view class="info-label">上课时间：</view>
          <view class="info-value">{{ classTime }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">上课老师：</view>
          <view class="info-value">{{ teacherName }}</view>
        </view>
      </view>

      <!-- 投诉内容 -->
      <view class="complaint-content">
        <view class="content-title">投诉内容</view>
        <view class="textarea-wrapper">
          <wd-textarea
            v-model="complaintContent"
            placeholder="请详细描述您遇到的问题，我们会认真处理您的投诉..."
            :maxlength="500"
            :show-word-limit="true"
            :auto-height="true"
            :min-height="200"
            class="complaint-textarea"
          />
        </view>
      </view>

      <!-- 投诉提示 -->
      <view class="complaint-tips">
        <view class="tips-title">
          <wd-icon name="info" size="32rpx" color="#667eea"></wd-icon>
          <text class="tips-text">投诉须知</text>
        </view>
        <view class="tips-content">
          <view class="tip-item">• 请如实描述问题，我们会在24小时内处理</view>
          <view class="tip-item">• 恶意投诉将影响您的信用记录</view>
          <view class="tip-item">• 投诉处理结果将通过消息通知您</view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <wd-button
        size="large"
        :loading="submitting"
        :disabled="!complaintContent.trim()"
        @click="submitComplaint"
      >
        提交投诉
      </wd-button>
    </view>

    <!-- 加载状态 -->
    <wd-loading v-if="loading" :loading="loading" />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'
import { showToast, navigateBack } from '@/utils'
import { complaintCreateUsingPost } from '@/service/student/appStudentComplaintManagement'
import { attendclassGetUsingPost } from '@/service/student/appStudentAttendClassPlanManagement'
import type {
  TeacherComplaintCreateReqVO,
  AttendClassPlanDetialRespVO,
} from '@/service/student/types'

defineOptions({
  name: 'Complaint',
})

// 页面参数
const attendClassPlanId = ref<number | null>(null)

// 课程和老师信息
const classTime = ref('2025-08-30 到 2025-09-01')
const teacherName = ref('张老师')

// 投诉内容
const complaintContent = ref('')

// 加载和提交状态
const loading = ref(false)
const submitting = ref(false)

// 获取课程信息
const getClassInfo = async () => {
  if (!attendClassPlanId.value) return

  try {
    loading.value = true

    const response = await attendclassGetUsingPost({
      body: {
        id: attendClassPlanId.value,
      },
    })

    if (response.code === 200 && response.data?.item) {
      const classInfo = response.data.item
      teacherName.value = classInfo.teacherName || '张老师'

      // 格式化课程时间
      if (classInfo.classDate) {
        const startTime = classInfo.startTime
        const endTime = classInfo.endTime
        classTime.value = `${classInfo.classDate} ${startTime} - ${endTime}`
      }
    } else {
      // 使用默认数据
      classTime.value = '2025-08-30 到 2025-09-01'
      teacherName.value = '张老师'
    }
  } catch (error) {
    console.error('获取课程信息失败:', error)
    // 使用默认数据
    classTime.value = '2025-08-30 到 2025-09-01'
    teacherName.value = '张老师'
  } finally {
    loading.value = false
  }
}

// 提交投诉
const submitComplaint = async () => {
  if (!attendClassPlanId.value) {
    showToast('缺少课程计划ID')
    return
  }

  if (!complaintContent.value.trim()) {
    showToast('请填写投诉内容')
    return
  }

  try {
    submitting.value = true

    const response = await complaintCreateUsingPost({
      body: {
        attendClassPlanId: attendClassPlanId.value,
        content: complaintContent.value.trim(),
      },
    })

    if (response.code === 200) {
      showToast('投诉提交成功，我们会尽快处理')
      setTimeout(() => {
        navigateBack()
      }, 1500)
    } else {
      showToast('投诉提交失败，请重试')
    }
  } catch (error) {
    console.error('提交投诉失败:', error)
    showToast('投诉提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

onLoad((options: any) => {
  console.log('页面参数:', options)

  if (options.attendClassPlanId) {
    attendClassPlanId.value = parseInt(options.attendClassPlanId)
  }

  getClassInfo()
})

onMounted(() => {
  // 页面挂载后的逻辑
})
</script>

<style lang="scss" scoped>
.complaint-page {
  min-height: 100vh;
  padding: 32rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
}

.header-section {
  margin-bottom: 40rpx;
  text-align: center;

  .header-title {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
  }

  .header-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-card {
  padding: 40rpx;
  margin-bottom: 40rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.class-info {
  margin-bottom: 40rpx;

  .info-title {
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      min-width: 160rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .info-value {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
    }
  }
}

.complaint-content {
  margin-bottom: 40rpx;

  .content-title {
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
  }

  .textarea-wrapper {
    .complaint-textarea {
      padding: 24rpx;
      background: #f8f9fa;
      border: 2rpx solid #e9ecef;
      border-radius: 12rpx;

      &:focus {
        border-color: #ff6b6b;
      }
    }
  }
}

.complaint-tips {
  padding: 24rpx;
  background: linear-gradient(135deg, #fff5f5, #ffe8e8);
  border-left: 8rpx solid #ff6b6b;
  border-radius: 16rpx;

  .tips-title {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .tips-text {
      margin-left: 12rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;
    }
  }

  .tips-content {
    .tip-item {
      margin-bottom: 8rpx;
      font-size: 24rpx;
      line-height: 1.6;
      color: #666666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.submit-section {
  .submit-btn {
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    background: linear-gradient(135deg, #ff6b6b, #ffa8a8);
    border: none;
    border-radius: 44rpx;

    &:disabled {
      opacity: 0.5;
    }
  }
}
</style>
