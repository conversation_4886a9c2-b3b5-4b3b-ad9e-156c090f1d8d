/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemDictManagement';
import * as API from './types';

/** 【系统管理】添加字典信息 POST /pc/system/dict/create */
export function usePcSystemDictCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemDictCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、运营、财务、机构、老师、学生】查询指定type类型的字典列表信息 POST /pc/system/dict/detial */
export function usePcSystemDictDetialUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListSystemDictDataRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemDictDetialUsingPost,
    onSuccess(data: API.ResultListSystemDictDataRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理】修改字典信息 POST /pc/system/dict/modify */
export function usePcSystemDictModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemDictModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、运营、财务、机构、老师、学生】分页查询字典信息 POST /pc/system/dict/page */
export function usePcSystemDictPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultSystemDictDetialRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemDictPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSystemDictDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理】删除指定字典信息 POST /pc/system/dict/remove */
export function usePcSystemDictRemoveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemDictRemoveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
