<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '选择单词',
  },
}
</route>

<template>
  <sxt-container @scrolltolower="onScrolltolower">
    <!-- 单词列表 -->
    <view class="p-32rpx">
      <view class="space-y-24rpx">
        <view
          v-for="(word, index) in wordList"
          :key="index"
          class="flex items-center justify-between py-24rpx px-16rpx bg-white rounded-16rpx shadow-sm border border-gray-100"
        >
          <!-- 左侧序号和单词 -->
          <view class="flex items-center flex-1">
            <!-- 序号 -->
            <view class="w-32rpx text-24rpx text-gray-500 font-500 mr-24rpx">{{ index + 1 }}.</view>

            <!-- 单词内容 -->
            <view class="flex-1">
              <view class="text-32rpx font-600 text-gray-800 mb-4rpx">
                {{ word.word }}
              </view>
            </view>
          </view>

          <!-- 右侧勾选按钮 -->
          <view
            class="w-48rpx h-48rpx rounded-full border-2 flex items-center justify-center cursor-pointer transition-all duration-300 ml-24rpx"
            @click="toggleWord(index)"
          >
            <wd-icon
              v-if="word.isNeedLearn === '1'"
              name="check-rectangle"
              color=" #80d4ff"
              size="24px"
            ></wd-icon>
            <wd-icon v-else name="rectangle" color=" #d9d9d9" size="24px"></wd-icon>
          </view>
        </view>
      </view>
      <view v-if="loading" class="text-center text-24rpx text-#666 py-32rpx">加载中...</view>
      <view v-if="noMore && wordList.length > 0" class="text-center text-24rpx text-#666 py-32rpx">
        没有更多数据了
      </view>
      <view
        v-if="wordList.length === 0 && !loading"
        class="text-center text-24rpx text-#666 py-64rpx"
      >
        暂无单词数据
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-32rpx border-t border-gray-200 shadow-lg">
      <!-- 统计信息 -->
      <view class="flex items-center justify-center mb-24rpx">
        <view class="text-28rpx text-gray-600">
          已选择
          <text class="text-blue-500 font-600">{{ selectedCount }}</text>
          个单词
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="flex gap-24rpx">
        <wd-button
          type="info"
          size="large"
          @click="handleSelectAll"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx"
        >
          {{ isAllSelected ? '取消全选' : '全选' }}
        </wd-button>

        <wd-button
          type="primary"
          size="large"
          @click="handleStartClass"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx bg-blue-500"
        >
          开始学习
        </wd-button>
      </view>
    </view>

    <!-- 底部安全间距 -->
    <view class="h-200rpx"></view>
  </sxt-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast, navigateBack, navigateToSub } from '@/utils'
import {
  attendclassSelectWordsLearnUsingPost,
  attendclassWordAllPageUsingPost,
  attendclassStartClassPlanUsingPost,
} from '@/service/teacher'
import { useDictStore } from '@/store'
const totalCount = ref(90)
const wordList = ref([])
// 加载状态
const loading = ref(false)
// 没有更多数据
const noMore = ref(false)
const coursewareId = ref('')
const classId = ref('')
const searchForm = ref({
  pageSize: 15,
  pageNo: 1,
  classPlanId: classId.value,
})

const dictStore = useDictStore()
// 搜索老师
const searchWords = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true
  try {
    const words = await attendclassWordAllPageUsingPost({
      body: {
        ...searchForm.value,
      },
    })
    if (!words?.data?.items) {
      return
    }
    if (isLoadMore) {
      wordList.value = [...wordList.value, ...words?.data?.items]
    } else {
      wordList.value = words.data.items || []
    }

    // 更新总记录数
    totalCount.value = (words.data as any)?.total || words.data.items?.length

    noMore.value = words.data.items.length < searchForm.value.pageSize
    if (isLoadMore) {
      searchForm.value.pageNo++
    }
  } catch (error) {
    console.error('搜索老师失败', error)
    uni.showToast({
      title: '搜索失败，请稍后重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

defineOptions({
  name: 'ChooseWordsPage',
})

// 计算选中的单词数量
const selectedCount = computed(() => {
  return wordList.value.filter((word) => word.isNeedLearn === '1').length
})

// 计算是否全选
const isAllSelected = computed(() => {
  return wordList.value.length > 0 && wordList.value.every((word) => word.isNeedLearn)
})

// 切换单词选中状态
const toggleWord = (index: number) => {
  if (wordList.value[index].isNeedLearn === '1') {
    wordList.value[index].isNeedLearn = '0'
  } else {
    wordList.value[index].isNeedLearn = '1'
  }
  // wordList.value[index].isNeedLearn = 1
}
// 全选/取消全选
const handleSelectAll = () => {
  const shouldSelectAll = !isAllSelected.value
  wordList.value.forEach((word) => {
    word.isNeedLearn = shouldSelectAll
  })
}

// 开始上课
const handleStartClass = async () => {
  // 先根据选择结果，过滤需要和不需要的单词
  if (wordList.value.length > 0) {
    // 这里的逻辑是找到最后一次标记为需要学习的单词的下表，那么在这之前的都是需要学习和不需要学习的
    const lastNeedLearnWordIdx = wordList.value.findLastIndex((word) => word.isNeedLearn === '1')
    const needLearnWordIds = []
    const noNeedLearnWordIds = []
    if (lastNeedLearnWordIdx !== -1) {
      wordList.value.forEach((word, index) => {
        if (index <= lastNeedLearnWordIdx) {
          if (word.isNeedLearn === '1') {
            needLearnWordIds.push(word.id)
          } else {
            noNeedLearnWordIds.push(word.id)
          }
        }
      })
    }
    console.log('needLearnWordIds', needLearnWordIds, noNeedLearnWordIds)
    const res = await attendclassSelectWordsLearnUsingPost({
      body: {
        attendClassPlanId: Number(classId.value),
        needLearnWordIds,
        notNeedLearnWordIds: noNeedLearnWordIds,
      },
    })
    if (res.code === 200) {
      showToast('选择成功进入学习页面')
      navigateToSub(
        `/start-learn-page/start-learn-page?classId=${classId.value}&coursewareId=${coursewareId.value}`,
      )
      searchForm.value.pageNo = 1
      searchWords()
    }
  }
}

// 获取单词列表

const getInit = async () => {
  await searchWords(true)
}
//  如果非抗遗忘训练，则直接开始上课

onLoad((option) => {
  if (option.classId) {
    classId.value = option.classId
    searchForm.value.classPlanId = classId.value
  }
  if (option.coursewareId) {
    coursewareId.value = option.coursewareId
  }
})
const onScrolltolower = () => {
  console.log('是否触发了上拉加载更多')
  if (!noMore.value) {
    searchWords(true)
  }
}
onMounted(() => {
  getInit()
  console.log('dictStore.dictData', dictStore.dictData)
  // 监听上拉触底事件
})
</script>
