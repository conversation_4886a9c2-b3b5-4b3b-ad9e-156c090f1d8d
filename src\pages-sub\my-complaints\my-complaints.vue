<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的投诉',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="my-complaints-page">
    <!-- 页面头部 -->
    <view class="header-section">
      <view class="header-title">我的投诉</view>
      <view class="header-subtitle">查看投诉记录和处理状态</view>
    </view>

    <!-- 投诉列表 -->
    <view class="complaints-container">
      <!-- 投诉卡片列表 -->
      <view v-if="complaintList.length > 0" class="complaints-list">
        <view
          v-for="complaint in complaintList"
          :key="complaint.id"
          class="complaint-card"
          @click="viewComplaintDetail(complaint)"
        >
          <!-- 投诉基本信息 -->
          <view class="complaint-header">
            <view class="complaint-title">
              <view class="teacher-name">投诉老师：{{ complaint.teacherName || '未知老师' }}</view>
              <view class="complaint-status" :class="getStatusClass(complaint.status)">
                {{ getStatusText(complaint.status) }}
              </view>
            </view>
            <view class="complaint-time">{{ formatTime(complaint.createTime) }}</view>
          </view>

          <!-- 投诉内容 -->
          <view class="complaint-content">
            <view class="content-label">投诉内容：</view>
            <view class="content-text">{{ complaint.content || '暂无内容' }}</view>
          </view>

          <!-- 处理方案（如果已处理） -->
          <view v-if="complaint.status === '1' && complaint.handleScheme" class="handle-scheme">
            <view class="scheme-label">处理方案：</view>
            <view class="scheme-text">{{ complaint.handleScheme }}</view>
          </view>

          <!-- 操作按钮 -->
          <!-- <view class="complaint-actions">
            <wd-button size="small" type="primary" @click.stop="viewComplaintDetail(complaint)">
              查看详情
            </wd-button>
          </view> -->
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-state">
        <wd-icon name="warn" size="120rpx" color="#cccccc"></wd-icon>
        <view class="empty-text">暂无投诉记录</view>
        <view class="empty-subtitle">您还没有提交过投诉</view>
      </view>
    </view>

    <!-- 分页加载 -->
    <view v-if="complaintList.length > 0 && hasMore" class="load-more-section">
      <wd-button type="info" size="large" :loading="loadingMore" @click="loadMoreComplaints">
        {{ loadingMore ? '加载中...' : '加载更多' }}
      </wd-button>
    </view>

    <!-- 没有更多数据提示 -->
    <view v-if="complaintList.length > 0 && !hasMore" class="no-more-tip">
      <view class="tip-text">没有更多数据了</view>
    </view>

    <!-- 加载状态 -->
    <wd-loading v-if="loading" :loading="loading" />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'
import { showToast, navigateToSub } from '@/utils'
import { complaintPageUsingPost } from '@/service/student/appStudentComplaintManagement'
import type {
  TeacherComplaintBaseRespVO,
  TeacherComplaintPageByStuReqVO,
} from '@/service/student/types'

defineOptions({
  name: 'MyComplaints',
})

// 投诉列表数据
const complaintList = ref<TeacherComplaintBaseRespVO[]>([])

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 加载状态
const loading = ref(true)
const loadingMore = ref(false)

// 获取投诉列表
const getComplaintList = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true
    } else {
      loading.value = true
    }

    const response = await complaintPageUsingPost({
      body: {
        pageNo: currentPage.value,
        pageSize: pageSize.value,
      },
    })

    console.log('投诉列表API响应:', response)

    if (response.code === 200 && response.data?.items) {
      const newComplaints = response.data.items

      if (isLoadMore) {
        // 加载更多时追加数据
        complaintList.value = [...complaintList.value, ...newComplaints]
      } else {
        // 首次加载时替换数据
        complaintList.value = newComplaints
      }

      // 判断是否还有更多数据
      const totalCount = response.data.counts || 0
      const currentTotal = complaintList.value.length
      hasMore.value = currentTotal < totalCount
    } else {
      if (!isLoadMore) {
        complaintList.value = []
      }
      if (response.message) {
        showToast(response.message)
      }
    }
  } catch (error) {
    console.error('获取投诉列表失败:', error)
    showToast('获取投诉列表失败，请稍后重试')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 加载更多投诉
const loadMoreComplaints = () => {
  if (loadingMore.value || !hasMore.value) return

  currentPage.value++
  getComplaintList(true)
}

// 格式化时间
const formatTime = (timeStr: string | undefined) => {
  if (!timeStr) return '暂无时间'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm')
}

// 获取状态文本
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case '0':
      return '未处理'
    case '1':
      return '已处理'
    default:
      return '未知状态'
  }
}

// 获取状态样式类
const getStatusClass = (status: string | undefined) => {
  switch (status) {
    case '0':
      return 'status-pending'
    case '1':
      return 'status-handled'
    default:
      return 'status-unknown'
  }
}

// 查看投诉详情
const viewComplaintDetail = (complaint: TeacherComplaintBaseRespVO) => {
  // 这里可以跳转到投诉详情页面，或者显示详情弹窗
  console.log('查看投诉详情:', complaint)
  showToast('投诉详情功能待开发')
}

onLoad(() => {
  getComplaintList()
})

onMounted(() => {
  // 页面挂载后的逻辑
})
</script>

<style lang="scss" scoped>
.my-complaints-page {
  min-height: 100vh;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header-section {
  margin-bottom: 40rpx;
  text-align: center;

  .header-title {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
  }

  .header-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.complaints-container {
  margin-bottom: 40rpx;
}

.complaints-list {
  .complaint-card {
    padding: 32rpx;
    margin-bottom: 24rpx;
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.98);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .complaint-header {
    margin-bottom: 24rpx;

    .complaint-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .teacher-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
      }

      .complaint-status {
        padding: 8rpx 16rpx;
        font-size: 24rpx;
        font-weight: 500;
        border-radius: 20rpx;

        &.status-pending {
          color: #ff6b6b;
          background: rgba(255, 107, 107, 0.1);
        }

        &.status-handled {
          color: #51cf66;
          background: rgba(81, 207, 102, 0.1);
        }

        &.status-unknown {
          color: #868e96;
          background: rgba(134, 142, 150, 0.1);
        }
      }
    }

    .complaint-time {
      font-size: 24rpx;
      color: #666666;
    }
  }

  .complaint-content {
    margin-bottom: 24rpx;

    .content-label {
      margin-bottom: 8rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .content-text {
      display: -webkit-box;
      overflow: hidden;
      font-size: 28rpx;
      line-height: 1.6;
      color: #333333;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }
  }

  .handle-scheme {
    padding: 20rpx;
    margin-bottom: 24rpx;
    background: rgba(81, 207, 102, 0.05);
    border-left: 6rpx solid #51cf66;
    border-radius: 12rpx;

    .scheme-label {
      margin-bottom: 8rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #51cf66;
    }

    .scheme-text {
      font-size: 28rpx;
      line-height: 1.6;
      color: #333333;
    }
  }

  .complaint-actions {
    display: flex;
    justify-content: flex-end;
  }
}

.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

  .empty-text {
    margin: 32rpx 0 16rpx;
    font-size: 32rpx;
    color: #666666;
  }

  .empty-subtitle {
    font-size: 28rpx;
    color: #999999;
  }
}

.load-more-section {
  margin-bottom: 40rpx;
}

.no-more-tip {
  padding: 40rpx;
  text-align: center;

  .tip-text {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
