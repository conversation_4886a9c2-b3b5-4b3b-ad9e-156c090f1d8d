/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 使用账号密码登录 POST /pc/system/auth/login */
export async function pcSystemAuthLoginUsingPost({
  body,
  options,
}: {
  body: API.AuthLoginReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAuthLoginRespVO>('/pc/system/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 登出系统 POST /pc/system/auth/logout */
export async function pcSystemAuthLogoutUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/auth/logout', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 刷新令牌 POST /pc/system/auth/refresh-token */
export async function pcSystemAuthRefreshTokenUsingPost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pcSystemAuthRefreshTokenUsingPostParams;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAuthLoginRespVO>('/pc/system/auth/refresh-token', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /pc/system/auth/test */
export async function pcSystemAuthTestUsingPost({
  body,
  options,
}: {
  body: API.AuthLoginReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultObject>('/pc/system/auth/test', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 权限测试 POST /pc/system/auth/testAuthor */
export async function pcSystemAuthTestAuthorUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultObject>('/pc/system/auth/testAuthor', {
    method: 'POST',
    ...(options || {}),
  });
}
