# 消息功能实现说明

## 功能概述

为老师和学生端都添加了消息功能，包括：

1. 个人中心首页的消息入口
2. 消息列表页面
3. 跳转到IM聊天页面的功能

## 新增文件

### 1. 消息列表页面

- 文件路径：`src/pages-sub/message-list/message-list.vue`
- 功能：显示消息列表，支持搜索，点击可跳转到聊天页面

### 2. API服务扩展

- 文件路径：`src/service/system/im.ts`
- 新增：`getConversationListUsingPost` 方法用于获取消息列表

## 修改文件

### 1. 老师个人资料组件

- 文件路径：`src/components/teacher/teacher-profile.vue`
- 新增：消息入口菜单项，包含未读消息数量显示
- 新增：`goToMessageList` 和 `fetchUnreadCount` 方法

### 2. 学生个人资料组件

- 文件路径：`src/components/student/student-profile.vue`
- 新增：消息入口菜单项，包含未读消息数量显示
- 新增：`goToMessageList` 和 `fetchUnreadCount` 方法

### 3. 聊天页面

- 文件路径：`src/pages-sub/chat/chat.vue`
- 修改：支持从本地存储读取聊天参数

### 4. 路由配置

- 文件路径：`src/pages.json`
- 新增：消息列表页面的路由配置

## 功能特点

### 1. 角色区分

- 老师端：显示学生列表
- 学生端：显示老师列表
- 根据用户角色自动切换显示内容

### 2. 未读消息提醒

- 在个人中心显示未读消息数量
- 支持99+的显示格式

### 3. 搜索功能

- 支持按联系人姓名搜索
- 实时过滤显示结果

### 4. 时间显示

- 智能时间显示（刚刚、X分钟前、X小时前、昨天、具体日期）

### 5. 参数传递

- 通过本地存储传递聊天参数
- 支持头像、姓名等信息的传递

## 使用流程

1. 用户在个人中心点击"我的消息"
2. 进入消息列表页面，查看所有联系人
3. 点击任意联系人进入聊天页面
4. 在聊天页面进行实时对话

## 技术实现

### 1. 状态管理

- 使用Vue 3的Composition API
- 响应式数据管理

### 2. 样式设计

- 使用Tailwind CSS
- 响应式设计，适配不同屏幕

### 3. 数据获取

- 支持真实API调用
- 提供模拟数据作为备用

### 4. 错误处理

- 完善的错误处理机制
- 用户友好的错误提示

## 后续优化建议

1. 添加消息推送功能
2. 实现消息已读状态管理
3. 添加消息类型支持（图片、语音等）
4. 优化消息列表的性能
5. 添加消息删除功能
6. 实现消息置顶功能
