<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 用户信息区域 -->
    <view class="bg-white px-32rpx py-48rpx">
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <!-- 头像 -->
          <view class="w-120rpx h-120rpx mr-32rpx">
            <image
              :src="teacherInfo.avatar || '/static/images/default-avatar.png'"
              class="w-full h-full rounded-full object-cover"
              mode="aspectFill"
            />
          </view>
          <!-- 用户名 -->
          <view class="text-36rpx font-600 text-gray-800">
            {{ teacherInfo.name || '李木子' }}
          </view>
        </view>
        <!-- 右箭头 -->
        <view class="text-gray-400 text-32rpx">></view>
      </view>
    </view>

    <!-- 数据统计区域 -->
    <view class="bg-white mx-32rpx rounded-16rpx p-32rpx mb-32rpx shadow-sm mt-4">
      <view class="flex justify-between">
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.completedClasses || 100 }}
          </view>
          <view class="text-24rpx text-gray-500">已上课</view>
        </view>
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.pendingClasses || 100 }}
          </view>
          <view class="text-24rpx text-gray-500">待上课</view>
        </view>
      </view>
    </view>

    <!-- 收益统计卡片 -->
    <view class="mx-32rpx mb-32rpx">
      <view
        class="bg-gradient-to-r from-orange-400 to-orange-500 rounded-16rpx p-32rpx text-white relative overflow-hidden"
      >
        <!-- 背景装饰 -->
        <view
          class="absolute -top-20rpx -right-20rpx w-120rpx h-120rpx bg-white opacity-10 rounded-full"
        ></view>
        <view
          class="absolute -bottom-40rpx -left-20rpx w-160rpx h-160rpx bg-white opacity-5 rounded-full"
        ></view>

        <!-- 标题和提现按钮 -->
        <view class="flex items-center justify-between mb-32rpx">
          <view class="text-28rpx font-600">到账收益</view>
          <view
            class="bg-white text-orange-500 text-24rpx px-24rpx py-8rpx rounded-full cursor-pointer active:scale-95 transition-all duration-300"
            @click="handleWithdraw"
          >
            去提现
          </view>
        </view>

        <!-- 收益金额 -->
        <view class="text-64rpx font-700 mb-24rpx">{{ earnings.totalAmount || 100 }}</view>

        <!-- 收益详情 -->
        <view class="flex justify-between">
          <view>
            <view class="text-20rpx opacity-80 mb-4rpx">今日收益</view>
            <view class="text-32rpx font-600">{{ earnings.todayAmount || 12344 }}</view>
          </view>
          <view>
            <view class="text-20rpx opacity-80 mb-4rpx">总收益</view>
            <view class="text-32rpx font-600">{{ earnings.totalEarnings || 12344 }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单列表 -->
    <view class="bg-white mx-32rpx rounded-16rpx overflow-hidden shadow-sm">
      <!-- 课时明细 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleClassSchedule"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-blue-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">📅</view>
          </view>
          <view class="text-32rpx text-gray-800">课时明细</view>
        </view>
        <view class="text-gray-400">></view>
      </view>
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleClassSchedule"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-blue-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">📅</view>
          </view>
          <view class="text-32rpx text-gray-800">发布测评</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的预约 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyBookings"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-green-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">⭐</view>
          </view>
          <view class="text-32rpx text-gray-800">我的预约</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的推荐码 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyReferralCode"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-cyan-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">📋</view>
          </view>
          <view class="text-32rpx text-gray-800">我的推荐码</view>
        </view>
        <view class="flex items-center">
          <view class="text-blue-500 text-28rpx mr-16rpx">
            {{ teacherInfo.referralCode || '*********' }}
          </view>
          <view class="text-gray-400">></view>
        </view>
      </view>

      <!-- 修改密码 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleChangePassword"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-yellow-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">🔒</view>
          </view>
          <view class="text-32rpx text-gray-800">修改密码</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 当前版本 -->
      <view class="flex items-center justify-between px-32rpx py-32rpx">
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-gray-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">ℹ️</view>
          </view>
          <view class="text-32rpx text-gray-800">当前版本</view>
        </view>
        <view class="text-gray-400">></view>
      </view>
      <view @click="handleLogout" class="flex items-center justify-between px-32rpx py-32rpx">
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-gray-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">ℹ️</view>
          </view>
          <view class="text-32rpx text-gray-800">退出登录</view>
        </view>
        <view class="text-gray-400">></view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="h-120rpx"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { showToast, navigateToSub } from '@/utils'

defineOptions({
  name: 'TeacherMine',
})

// 老师信息
const teacherInfo = ref({
  name: '李木子',
  avatar: '/static/images/default-avatar.png',
  referralCode: '*********',
})

// 统计数据
const stats = ref({
  completedClasses: 100,
  pendingClasses: 100,
})

// 收益数据
const earnings = ref({
  totalAmount: 100,
  todayAmount: 12344,
  totalEarnings: 12344,
})
const handleLogout = () => {
  uni.setStorageSync('token', '')
  navigateToSub('/role-select/role-select?to=login')
}
// 处理提现
const handleWithdraw = () => {
  showToast('跳转到提现页面')
  // navigateToSub('/withdraw/withdraw')
}

// 处理课时明细
const handleClassSchedule = () => {
  showToast('跳转到课时明细页面')
  // navigateToSub('/class-schedule/class-schedule')
}

// 处理我的预约
const handleMyBookings = () => {
  showToast('跳转到我的预约页面')
  // navigateToSub('/my-bookings/my-bookings')
}

// 处理我的推荐码
const handleMyReferralCode = () => {
  showToast('跳转到我的推荐码页面')
  // navigateToSub('/my-referral-code/my-referral-code')
}

// 处理修改密码
const handleChangePassword = () => {
  showToast('跳转到修改密码页面')
  // navigateToSub('/change-password/change-password')
}

// 获取老师信息
const fetchTeacherInfo = async () => {
  try {
    // 这里可以调用API获取老师信息
    // 暂时使用模拟数据
    teacherInfo.value = {
      name: '李木子',
      avatar: '/static/images/default-avatar.png',
      referralCode: '*********',
    }
  } catch (error) {
    console.error('获取老师信息失败:', error)
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里可以调用API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      completedClasses: 100,
      pendingClasses: 100,
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取收益数据
const fetchEarnings = async () => {
  try {
    // 这里可以调用API获取收益数据
    // 暂时使用模拟数据
    earnings.value = {
      totalAmount: 100,
      todayAmount: 12344,
      totalEarnings: 12344,
    }
  } catch (error) {
    console.error('获取收益数据失败:', error)
  }
}

onMounted(() => {
  fetchTeacherInfo()
  fetchStats()
  fetchEarnings()
})
</script>
