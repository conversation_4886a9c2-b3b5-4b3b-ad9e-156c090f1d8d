<route lang="json5">
{
  style: {
    navigationBarTitleText: '沟通',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>
<template>
  <view class="chat-container" style="height: calc(100vh - 44px)">
    <!-- 聊天界面 -->
    <view class="chat-main">
      <!-- 消息显示框 -->
      <view style="height: calc(100vh - 140px)" class="flex">
        <scroll-view
          class="message-box"
          scroll-y
          :scroll-into-view="scrollToId"
          scroll-with-animation
          @scrolltoupper="loadMoreMessages"
        >
          <!-- 加载更多消息 -->
          <view v-if="isLoadingMore" class="loading-more">
            <wd-loading size="small" />
            <text class="loading-text">加载中...</text>
          </view>
          <view
            v-for="message in messageList"
            :key="message.id"
            class="message-item"
            :class="{ 'message-self': message.isSelf }"
          >
            <view class="message-avatar">
              <image
                v-if="message.avatar"
                :src="message.avatar"
                class="avatar-image"
                mode="aspectFill"
              />
              <text v-else class="avatar-text">{{ message.isSelf ? '我' : '对方' }}</text>
            </view>
            <view class="message-content">
              <view class="message-info">
                <text class="message-sender">{{ message.nick || message.sender }}</text>
                <text class="message-time">{{ formatMessageTime(message.time) }}</text>
              </view>
              <view class="message-text">
                <text>{{ message.text }}</text>
                <!-- 消息状态 -->
                <view v-if="message.isSelf" class="message-status">
                  <wd-icon
                    v-if="message.status === 'sending'"
                    name="loading"
                    size="12px"
                    class="status-icon"
                  />
                  <wd-icon
                    v-else-if="message.status === 'sent'"
                    name="check"
                    size="12px"
                    class="status-icon sent"
                  />
                  <wd-icon
                    v-else-if="message.status === 'failed'"
                    name="close"
                    size="12px"
                    class="status-icon failed"
                  />
                </view>
              </view>
            </view>
          </view>
          <view v-if="messageList.length === 0 && !isLoading" class="empty-message">
            <text class="empty-text">暂无消息，开始聊天吧！</text>
          </view>
          <view id="bottom-anchor"></view>
        </scroll-view>
      </view>

      <!-- 输入框和发送按钮 -->
      <view class="input-container">
        <input
          v-model="messageInput"
          class="message-input"
          placeholder="输入消息..."
          type="text"
          :disabled="isSending"
          @confirm="sendMessage"
        />
        <wd-button
          type="primary"
          class="send-btn"
          :disabled="!messageInput.trim() || isSending"
          :loading="isSending"
          @click="sendMessage"
        >
          {{ isSending ? '发送中' : '发送' }}
        </wd-button>
      </view>
    </view>

    <!-- 加载状态 -->
    <wd-loading v-if="isLogging" :loading="isLogging" />
  </view>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, computed } from 'vue'
import { showToast } from '@/utils'
import {
  formatMessageTime,
  generateMessageId,
  isMessageEmpty,
  getUserDisplayName,
  getUserAvatar,
} from '@/utils/chat'
import TIM from 'tim-js-sdk'
import { imHistoryUsingPost } from '@/service/system/im'
import { useChatStore } from '@/store/chat'

defineOptions({
  name: 'Chat',
})

interface Message {
  id: number
  text: string
  sender: string
  receiver: string
  nick?: string
  avatar?: string
  time: number
  isSelf: boolean
  status?: 'sending' | 'sent' | 'failed'
}

// 登录表单数据
const loginForm = ref({
  sdkAppID: import.meta.env.VITE_SDKAPPID,
  userID: uni.getStorageSync('userID'),
  userSig: uni.getStorageSync('userSig'),
})
const tim = ref()
// 聊天相关状态
const isLogging = ref(false)
const isSending = ref(false)
const isLoading = ref(false)
const isLoadingMore = ref(false)
const receiverUserID = ref('')

const messageInput = ref('')
const messageList = ref<Message[]>([])
const currentUserInfo = ref<any>(null)

// 滚动相关
const scrollToId = ref('bottom-anchor')
const nextReqMessageID = ref(null)

const getImHistoryUsingPost = async () => {
  const promise = tim.value.getMessageList({
    nextReqMessageID: nextReqMessageID.value,
    conversationID: 'C2C' + receiverUserID.value,
  })
  promise.then(function (imResponse) {
    console.log('chat1232', imResponse)
    const newMessage = imResponse.data.messageList.map((message) =>
      formatMessageData(
        {
          ...message,
          text: message.payload?.text || '',
        },
        loginForm.value.userID === message.from,
      ),
    )
    messageList.value.push(...newMessage)
    if (!imResponse.data.isCompleted) {
      nextReqMessageID.value = imResponse.data.nextReqMessageID
      getImHistoryUsingPost()
    } else {
      messageList.value = messageList.value.sort((a, b) => a.time - b.time)
      console.log('chat1232', messageList.value)
      scrollToBottom()
    }
  })
}
const init = (chat) => {
  tim.value = chat
  getImHistoryUsingPost()
  chat.setMessageRead({ conversationID: `C2C${receiverUserID.value}` })
  const onMessageReceived = (event: any) => {
    const messages = event.data
    messages.forEach((message: any) => {
      if (message.conversationID === `C2C${receiverUserID.value}`) {
        const newMessage = formatMessageData(
          {
            text: message.payload?.text || '',
          },
          false,
        )
        messageList.value.push(newMessage)
        scrollToBottom()
      }
    })
  }
  tim.value.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived)
}
const chatStore = useChatStore()
if (!chatStore.isReady) {
  chatStore.login(loginForm.value.userID, loginForm.value.userSig)
}
chatStore.getChatIns().then(init)

// 格式化消息数据
const formatMessageData = (data: any, isSelf = false): Message => {
  return {
    ...data,
    id: generateMessageId(),
    text: data.text || data.msg || '',
    sender: data.from,
    receiver: data.to,
    time: data.time * 1000,
    isSelf,
    status: isSelf ? 'sending' : undefined,
  }
}
// 获取历史消息
// 加载更多消息
const loadMoreMessages = () => {}

// 发送消息
const sendMessage = () => {
  const text = messageInput.value.trim()
  if (isMessageEmpty(text) || isSending.value) {
    return
  }

  try {
    isSending.value = true

    // 发送到服务器
    if (tim.value) {
      console.log('receiverUserID', receiverUserID.value)
      const timMessage = tim.value.createTextMessage({
        to: receiverUserID.value,
        conversationType: TIM.TYPES.CONV_C2C,
        payload: { text },
      })
      tim.value
        .sendMessage(timMessage)
        .then(() => {
          const localMessage = formatMessageData({ text }, true)
          messageList.value.push(localMessage)
          messageInput.value = ''
          scrollToBottom()
        })
        .catch(() => {
          showToast('消息发送失败')
          const messageIndex = messageList.value.findIndex((msg) => msg.text === text && msg.isSelf)
          if (messageIndex !== -1) {
            messageList.value[messageIndex].status = 'failed'
          }
        })
        .finally(() => {
          isSending.value = false
        })
    } else {
      throw new Error('错误信息')
    }
  } catch (error) {
    showToast('消息发送失败')
    isSending.value = false
    // 更新消息状态为失败
    const messageIndex = messageList.value.findIndex((msg) => msg.text === text && msg.isSelf)
    if (messageIndex !== -1) {
      messageList.value[messageIndex].status = 'failed'
    }
  }
}

// 接收消息

// 滚动到底部
const scrollToBottom = () => {
  scrollToId.value = ''
  setTimeout(() => {
    scrollToId.value = 'bottom-anchor'
  }, 30)
}

// 获取当前用户信息
const getCurrentUserInfo = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo')
    if (userInfo) {
      currentUserInfo.value = userInfo
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
  }
}

// 页面加载
onLoad((options: any) => {
  isLogging.value = true
  if (options.receiverUserID) {
    receiverUserID.value = options.receiverUserID
  }
  getCurrentUserInfo()
})
</script>

<style lang="scss" scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chat-main {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 32rpx;
}

.message-box {
  box-sizing: border-box;
  flex: 1;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx 24rpx 0 0;

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;

    .loading-text {
      margin-left: 16rpx;
      font-size: 24rpx;
      color: #999999;
    }
  }

  .message-item {
    display: flex;
    margin-bottom: 32rpx;

    .message-avatar {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      margin-right: 16rpx;
      overflow: hidden;
      background: #f0f0f0;
      border-radius: 50%;

      .avatar-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .avatar-text {
        font-size: 24rpx;
        font-weight: 500;
        color: #666666;
      }
    }

    .message-content {
      display: flex;
      flex: 1;
      flex-direction: column;

      .message-info {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .message-sender {
          margin-right: 16rpx;
          font-size: 24rpx;
          color: #666666;
        }

        .message-time {
          font-size: 20rpx;
          color: #999999;
        }
      }

      .message-text {
        position: relative;
        display: flex;
        align-items: flex-end;
        max-width: 80%;
        padding: 16rpx 20rpx;
        font-size: 28rpx;
        line-height: 1.4;
        color: #333333;
        word-wrap: break-word;
        background: #f5f5f5;
        border-radius: 16rpx;

        .message-status {
          display: flex;
          align-items: center;
          margin-left: 8rpx;

          .status-icon {
            color: #999999;

            &.sent {
              color: #52c41a;
            }

            &.failed {
              color: #ff4d4f;
              cursor: pointer;
            }
          }
        }
      }
    }

    &.message-self {
      flex-direction: row-reverse;

      .message-content {
        align-items: flex-end;

        .message-text {
          color: #ffffff;
          background: linear-gradient(135deg, #667eea, #764ba2);
        }
      }

      .message-avatar {
        margin-right: 0;
        margin-left: 16rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);

        .avatar-text {
          color: #ffffff;
        }
      }
    }
  }

  .empty-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
}

.input-container {
  display: flex;
  align-items: center;
  height: 62px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 24rpx 24rpx;

  .message-input {
    flex: 1;
    height: 72rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
    background: #f8f9fa;
    border: none;
    border-radius: 36rpx;

    &::placeholder {
      color: #999999;
    }

    &:disabled {
      opacity: 0.6;
    }
  }

  .send-btn {
    height: 72rpx;
    padding: 0 32rpx;
    font-size: 28rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 36rpx;

    &:disabled {
      opacity: 0.5;
    }
  }
}

.logout-container {
  margin-top: 24rpx;
  text-align: center;
}
</style>

<style>
.uni-scroll-view-content {
  height: auto !important;
}
</style>
