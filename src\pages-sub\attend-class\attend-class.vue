<route lang="json5">
{
  layout: 'common',
  style: {
    navigationBarTitleText: '课堂',
  },
}
</route>

<template>
  <view class="bg-[#F5F5F5]">
    <!-- 课程信息卡片 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">课程信息</view>
      <view v-if="currentClass">
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">老师</view>
          <view class="text-[28rpx] font-medium">
            {{ currentClass.teacherName }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">课程类型</view>
          <view class="text-[28rpx] font-medium">
            {{ dict?.teacher_course_duration?.[currentClass.courseDuration]?.text || '未知类型' }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">上课时间</view>
          <view class="text-[28rpx] font-medium">
            {{ currentClass.startTime }} - {{ currentClass.endTime }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">腾讯会议号</view>
          <view class="text-[28rpx] font-medium">
            {{ currentClass.tencentMeetingNo || '暂无会议号' }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">课程状态</view>
          <view class="text-[28rpx] font-medium">
            {{ dict?.class_progress[currentClass.progress]?.text || '未知状态' }}
          </view>
        </view>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无课程信息</view>
    </view>

    <!-- 操作按钮 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]" v-if="false">
      <view class="text-[36rpx] font-bold mb-[24rpx]">课堂操作</view>
      <view class="flex justify-between">
        <wd-button type="primary" custom-class="w-[300rpx]!" @click="handleStartClass">
          开始上课
        </wd-button>
        <wd-button type="error" custom-class="w-[300rpx]!" @click="handleEndClass">
          结束上课
        </wd-button>
      </view>
    </view>

    <!-- 会议信息 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">会议信息</view>
      <view v-if="currentClass && currentClass.tencentMeetingNo">
        <view class="flex items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666] mr-[16rpx]">会议号:</view>
          <view class="text-[28rpx] font-medium">{{ currentClass.tencentMeetingNo }}</view>
          <wd-button
            size="small"
            type="primary"
            plain
            custom-class="ml-[16rpx]!"
            @click="copyMeetingNo"
          >
            复制
          </wd-button>
        </view>
        <wd-button type="primary" block @click="joinMeeting">加入会议</wd-button>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">
        老师尚未设置会议号，请耐心等待
      </view>
    </view>

    <!-- 课程资料 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">课程资料</view>
      <view v-if="currentClass && currentClass.coursewareId">
        <wd-button type="primary" plain block @click="viewCourseware">查看课程资料</wd-button>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无课程资料</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { showToast, navigateBack, navigateToCoursewareDetail } from '@/utils'
import {
  attendclassStartClassPlanUsingPost,
  attendclassEndClassPlanUsingPost,
  attendclassGetUsingPost,
} from '@/service/student/appStudentAttendClassPlanManagement'
import { useDictStore } from '@/store'
import { AttendClassPlanDetialRespVO } from '@/service/student'

// 页面参数
const classId = ref<number | null>(null)
const currentClass = ref<AttendClassPlanDetialRespVO>({ progress: 0 })
const dict = ref<any>(null)
// 复制会议号
const copyMeetingNo = () => {
  if (currentClass.value?.tencentMeetingNo) {
    uni.setClipboardData({
      data: currentClass.value.tencentMeetingNo,
      success: () => {},
    })
  }
}

// 加入会议
const joinMeeting = () => {
  showToast('正在打开腾讯会议...')
  // 这里可以添加打开腾讯会议的逻辑
}

// 查看课程资料
const viewCourseware = async () => {
  if (!currentClass.value?.coursewareId) {
    showToast('无法获取课程资料ID')
    return
  }

  const coursewareId = currentClass.value.coursewareId
  showToast('正在加载课程资料...')

  try {
    // 获取课程详情，确保有最新的课程资料ID
    const detailRes = await attendclassGetUsingPost({
      body: {
        id: currentClass.value.id,
      },
    })

    console.log('获取课程详情API响应:', detailRes)

    if (detailRes.code === 200 && detailRes.data?.item) {
      // 显示课程资料
      showToast('课程资料加载成功')
      // 导航到课程资料详情页面（自动根据角色选择）
      navigateToCoursewareDetail(coursewareId)
    } else {
      showToast(detailRes.message || '获取课程资料失败')
    }
  } catch (error) {
    console.error('获取课程资料失败:', error)
    showToast('获取课程资料失败，请稍后重试')
  }
}

// 开始上课
const handleStartClass = async () => {
  if (!currentClass.value?.id) {
    showToast('无法获取课程ID')
    return
  }

  try {
    const res = await attendclassStartClassPlanUsingPost({
      body: {
        id: currentClass.value.id,
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('上课开始成功')
      fetchClassInfo()
    } else {
      showToast(res.message || '开始上课失败')
    }
  } catch (error) {
    console.error('开始上课失败:', error)
    showToast('开始上课失败，请稍后重试')
  }
}

// 结束上课
const handleEndClass = async () => {
  if (!currentClass.value?.id) {
    showToast('无法获取课程ID')
    return
  }

  try {
    const res = await attendclassEndClassPlanUsingPost({
      body: {
        id: currentClass.value.id,
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('下课成功')
      // 延迟返回首页
      fetchClassInfo()
    } else {
      showToast(res.message || '结束上课失败')
    }
  } catch (error) {
    console.error('结束上课失败:', error)
    showToast('结束上课失败，请稍后重试')
  }
}

// 获取课程信息
const fetchClassInfo = async () => {
  try {
    // 获取字典数据
    const dictStore = useDictStore()
    dict.value = dictStore.dictData
    console.log(dict.value)

    if (classId.value) {
      // 如果有传入ID，则直接获取课程详情
      const detailRes = await attendclassGetUsingPost({
        body: {
          id: classId.value,
        },
      })

      console.log('课程详情API响应:', detailRes)

      if (detailRes.code === 200 && detailRes.data?.item) {
        // 构造符合ClassInfo类型的对象
        currentClass.value = detailRes.data.item
      } else {
        showToast('获取课程详情失败')
      }
    }
  } catch (error) {
    console.error('获取课程信息失败:', error)
    showToast('获取课程信息失败')
  }
}

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    classId.value = Number(options.id)
  }
})

onMounted(() => {
  fetchClassInfo()
})
</script>
