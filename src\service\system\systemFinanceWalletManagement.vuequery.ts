/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemFinanceWalletManagement';
import * as API from './types';

/** 【财务】财务钱包明细 POST /finance/wallet/details */
export function useFinanceWalletDetailsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultFinanceWalletInfoDetailsRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.financeWalletDetailsUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultFinanceWalletInfoDetailsRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【财务】财务钱包信息 POST /finance/wallet/info */
export function useFinanceWalletInfoUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultFinanceWalletInfoRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.financeWalletInfoUsingPost,
    onSuccess(data: API.ResultFinanceWalletInfoRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
