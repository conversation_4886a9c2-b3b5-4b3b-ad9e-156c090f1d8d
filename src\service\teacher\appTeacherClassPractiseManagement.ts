/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【老师】生成课堂试卷 POST /app/teacher/classpractis/generateRandomPractice */
export async function classpractisGenerateRandomPracticeUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>(
    '/app/teacher/classpractis/generateRandomPractice',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】随机生成用于课后练习的单词集合 POST /app/teacher/classpractis/generateRandomPracticeAfterClass */
export async function classpractisGenerateRandomPracticeAfterClassUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListString>(
    '/app/teacher/classpractis/generateRandomPracticeAfterClass',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】课堂试卷题目分页查询 POST /app/teacher/classpractis/practisDetailPage */
export async function classpractisPractisDetailPageUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseDetailPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStudentPractiseDetailBaseRespVO>(
    '/app/teacher/classpractis/practisDetailPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】课堂试卷分页查询 POST /app/teacher/classpractis/practisPage */
export async function classpractisPractisPageUsingPost({
  body,
  options,
}: {
  body: API.StudentPractisePageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStudentPractiseBaseRespVO>(
    '/app/teacher/classpractis/practisPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
