/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentEvaluateWordManagement';
import * as API from './types';

/** 【学生】根据id获取评测单词详情 POST /app/student/evaluateword/get */
export function useEvaluatewordGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStudentEvaluateWordGetRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.evaluatewordGetUsingPost,
    onSuccess(data: API.ResultStudentEvaluateWordGetRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】随机获取评测单词列表 POST /app/student/evaluateword/getRandomEvaluateWords */
export function useEvaluatewordGetRandomEvaluateWordsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStudentEvaluateWordGetRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.evaluatewordGetRandomEvaluateWordsUsingPost,
    onSuccess(data: API.ResultStudentEvaluateWordGetRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
