/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './iMceshi';
import * as API from './types';

/** 拉取消息记录 GET /test/history */
export function testHistoryUsingGetQueryOptions(options: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testHistoryUsingGetParams;
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.testHistoryUsingGet(queryKey[1] as typeof options);
    },
    queryKey: ['testHistoryUsingGet', options],
  });
}

/** 获取用户信息 GET /test/info */
export function testInfoUsingGetQueryOptions(options: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testInfoUsingGetParams;
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.testInfoUsingGet(queryKey[1] as typeof options);
    },
    queryKey: ['testInfoUsingGet', options],
  });
}

/** 注册 POST /test/register */
export function useTestRegisterUsingPostMutation(options?: {
  onSuccess?: (value?: string) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.testRegisterUsingPost,
    onSuccess(data: string) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 发送消息 POST /test/send */
export function useTestSendUsingPostMutation(options?: {
  onSuccess?: (value?: string) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.testSendUsingPost,
    onSuccess(data: string) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 测试AI GET /test/testGetChatResponse */
export function testTestGetChatResponseUsingGetQueryOptions(options: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.testTestGetChatResponseUsingGetParams;
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.testTestGetChatResponseUsingGet(
        queryKey[1] as typeof options
      );
    },
    queryKey: ['testTestGetChatResponseUsingGet', options],
  });
}
