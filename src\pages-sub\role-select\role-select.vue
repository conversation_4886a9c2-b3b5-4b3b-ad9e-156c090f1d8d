<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="min-h-screen bg-gradient-to-b from-blue-100 to-blue-50">
    <!-- 主内容区 -->
    <view class="flex flex-col min-h-screen pt-240rpx px-48rpx pb-48rpx">
      <!-- 标题 -->
      <view class="text-36rpx font-600 text-gray-800 text-center mb-120rpx">请选择您当前身份</view>

      <!-- 角色选择区域 -->
      <view class="flex-1 flex flex-col gap-48rpx mb-120rpx">
        <!-- 我是老师选项 -->
        <view
          class="bg-white rounded-24rpx p-32rpx flex items-center shadow-lg border-3 transition-all duration-300 cursor-pointer active:scale-98"
          :class="{
            'border-blue-500 bg-blue-50 -translate-y-8rpx shadow-blue-200':
              selectedRole === RoleEmu.Teacher,
            'border-transparent bg-white': selectedRole !== RoleEmu.Teacher,
          }"
          @click="selectRole(RoleEmu.Teacher)"
        >
          <view class="w-120rpx h-120rpx mr-32rpx flex items-center justify-center relative">
            <!-- 老师插画 -->
            <image src="../../assets/images/default_teacher.png" class="w-100rpx h-100rpx"></image>
          </view>
          <view
            class="text-32rpx font-500 flex-1"
            :class="selectedRole === RoleEmu.Teacher ? 'text-blue-500' : 'text-gray-800'"
          >
            我是老师
          </view>
        </view>

        <!-- 我是学生选项 -->
        <view
          class="bg-white rounded-24rpx p-32rpx flex items-center shadow-lg border-3 transition-all duration-300 cursor-pointer active:scale-98"
          :class="{
            'border-blue-500 bg-blue-50 -translate-y-8rpx shadow-blue-200':
              selectedRole === RoleEmu.Student,
            'border-transparent bg-white': selectedRole !== RoleEmu.Student,
          }"
          @click="selectRole(RoleEmu.Student)"
        >
          <view class="w-120rpx h-120rpx mr-32rpx flex items-center justify-center relative">
            <!-- 学生插画 -->
            <image src="../../assets/images/default_student.png" class="w-100rpx h-100rpx"></image>
          </view>
          <view
            class="text-32rpx font-500 flex-1"
            :class="selectedRole === RoleEmu.Student ? 'text-blue-500' : 'text-gray-800'"
          >
            我是学生
          </view>
        </view>
      </view>

      <!-- 立即体验按钮 -->
      <view class="pb-48rpx">
        <view
          class="w-full h-96rpx rounded-24rpx flex items-center justify-center text-32rpx font-600 text-white cursor-pointer transition-all duration-300"
          :class="
            selectedRole
              ? 'bg-blue-500 shadow-lg shadow-blue-500/30 active:scale-98'
              : 'bg-gray-400 cursor-not-allowed'
          "
          @click="handleExperience"
        >
          立即体验
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { navigateToSub } from '@/utils'
import { RoleEmu, useRoleStore } from '@/store'
const { setRole, getRole } = useRoleStore()
const to = ref('')
// 选中的角色
const selectedRole = ref<RoleEmu>(getRole())

// 选择角色
const selectRole = (role: RoleEmu) => {
  selectedRole.value = role
}
onLoad((option) => {
  if (option.to) {
    to.value = option.to
  }
})
// 立即体验
const handleExperience = () => {
  if (!selectedRole.value) {
    uni.showToast({
      title: '请选择身份',
      icon: 'none',
    })
    return
  }
  setRole(selectedRole.value)

  if (to.value === 'login') {
    navigateToSub(`/login/login?role=${selectedRole.value}`)
  } else {
    navigateToSub(`/register/register?role=${selectedRole.value}`)
  }
}
</script>
