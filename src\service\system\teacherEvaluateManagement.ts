/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】查询评价详情 POST /pc/teacher/evaluate/get */
export async function pcTeacherEvaluateGetUsingPost({
  body,
  options,
}: {
  body: API.TeacherEvaluateGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherEvaluateBaseRespVO>(
    '/pc/teacher/evaluate/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询评价列表 POST /pc/teacher/evaluate/page */
export async function pcTeacherEvaluatePageUsingPost({
  body,
  options,
}: {
  body: API.TeacherEvaluatePageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultTeacherEvaluateBaseRespVO>(
    '/pc/teacher/evaluate/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
