<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '学习单词',
  },
}
</route>

<template>
  <sxt-container @scrolltolower="onScrolltolower">
    <!-- 单词列表 -->
    <view class="p-32rpx">
      <view class="space-y-24rpx">
        <view
          v-for="(word, index) in wordList"
          @click="clickWord(word)"
          @dblclick="refishLearnProgress(word)"
          :key="index"
          class="word-container flex items-center justify-between py-24rpx px-16rpx bg-white rounded-16rpx shadow-sm border border-gray-100"
        >
          <!-- 左侧序号和单词 -->
          <view class="flex items-center flex-1">
            <!-- 序号 -->
            <view class="w-32rpx text-24rpx text-gray-500 font-500 mr-24rpx">{{ index + 1 }}.</view>

            <!-- 单词内容 -->
            <view class="flex-1">
              <view class="text-32rpx font-600 text-gray-800 mb-4rpx">
                {{ word.word }}
              </view>
            </view>

            <view
              class="w-48rpx flex-1 gap-2 h-48rpx rounded-full border-2 flex items-center justify-center cursor-pointer transition-all duration-300 ml-24rpx"
              @click="toggleWord(index)"
            >
              <!-- <wd-icon
              v-if="word.isCompletedLearn === '1'"
              name="check-rectangle"
              color=" #80d4ff"
              size="24px"
            ></wd-icon> -->
              <view>
                <wd-icon
                  @click="playSound(word)"
                  color=" #80d4ff"
                  name="sound"
                  size="22px"
                ></wd-icon>
              </view>
              <wd-button v-if="word.isCompletedLearn === '1'" type="text">已学习</wd-button>
              <!-- <wd-icon v-else name="rectangle" color=" #d9d9d9" size="24px"></wd-icon> -->
            </view>
          </view>

          <!-- 右侧勾选按钮 -->
          <!-- <view
            class="w-48rpx h-48rpx rounded-full border-2 flex items-center justify-center cursor-pointer transition-all duration-300 ml-24rpx"
            @click="toggleWord(index)"
          >
            <wd-icon
              v-if="word.isCompletedLearn === '1'"
              name="check-rectangle"
              color=" #80d4ff"
              size="24px"
            ></wd-icon>
            <wd-button v-if="word.isCompletedLearn === '1'" type="text">已学习</wd-button>
            <wd-icon v-else name="rectangle" color=" #d9d9d9" size="24px"></wd-icon>
          </view> -->
        </view>
      </view>
      <view v-if="loading" class="text-center text-24rpx text-#666 py-32rpx">加载中...</view>
      <view v-if="noMore && wordList.length > 0" class="text-center text-24rpx text-#666 py-32rpx">
        没有更多数据了
      </view>
      <view
        v-if="wordList.length === 0 && !loading"
        class="text-center text-24rpx text-#666 py-64rpx"
      >
        暂无老师数据
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-32rpx border-t border-gray-200 shadow-lg">
      <!-- 操作按钮 -->
      <view class="flex gap-24rpx">
        <wd-button type="text" size="large" custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx">
          {{ '已学习' + learnedWordsCount + '/' + wordList.length }}
        </wd-button>
        <wd-button
          type="text"
          size="large"
          @click="goBackChooseWords"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx"
        >
          {{ '返回筛选' }}
        </wd-button>
        <wd-button
          v-if="isGetAIpractise === true"
          type="text"
          size="large"
          @click="handleToPractice"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx"
        >
          {{ '前往答题' }}
        </wd-button>
        <wd-button
          v-if="isGetAIpractise === undefined"
          type="text"
          size="large"
          @click="gennerateClassPractise"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx"
        >
          {{ '课堂练习' }}
        </wd-button>
        <wd-loading v-if="isGetAIpractise === false" />

        <wd-button
          type="text"
          size="large"
          @click="handleEndClass"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx bg-blue-500"
        >
          {{ '结束上课' }}
        </wd-button>
      </view>
    </view>

    <!-- 底部安全间距 -->
    <view class="h-200rpx"></view>
    <wd-message-box selector="classPractise">
      <view class="p-32rpx">
        <view class="space-y-24rpx">
          <view
            v-for="(word, index) in wordList"
            :key="index"
            class="flex items-center justify-between py-24rpx px-16rpx bg-white rounded-16rpx shadow-sm border border-gray-100"
          >
            <!-- 左侧序号和单词 -->
            <view class="flex items-center flex-1">
              <!-- 序号 -->
              <view class="w-32rpx text-24rpx text-gray-500 font-500 mr-24rpx">
                {{ index + 1 }}.
              </view>

              <!-- 单词内容 -->
              <view class="flex-1">
                <view class="text-32rpx font-600 text-gray-800 mb-4rpx">
                  {{ word.word }}
                </view>
              </view>
            </view>

            <!-- 右侧勾选按钮 -->
            <view
              class="w-48rpx h-48rpx rounded-full border-2 flex items-center justify-center cursor-pointer transition-all duration-300 ml-24rpx"
              @click="toggleWord(index)"
            >
              <wd-icon
                v-if="word.isNeedPractise === '1'"
                name="check-rectangle"
                color=" #80d4ff"
                size="24px"
              ></wd-icon>
              <wd-icon v-else name="rectangle" color=" #d9d9d9" size="24px"></wd-icon>
            </view>
          </view>
        </view>
        <view v-if="loading" class="text-center text-24rpx text-#666 py-32rpx">加载中...</view>
        <view
          v-if="noMore && wordList.length > 0"
          class="text-center text-24rpx text-#666 py-32rpx"
        >
          没有更多数据了
        </view>
        <view
          v-if="wordList.length === 0 && !loading"
          class="text-center text-24rpx text-#666 py-64rpx"
        >
          暂无单词数据
        </view>
      </view>
    </wd-message-box>
  </sxt-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast, navigateBack, navigateToSub } from '@/utils'
import {
  attendclassSelectWordsLearnUsingPost,
  attendclassUpdateWordProssUsingPost,
  attendclassWordNeedLearnPageUsingPost,
  attendclassEndClassPlanUsingPost,
} from '@/service/teacher'
import { useDictStore } from '@/store'
import { useMessage, useToast } from 'wot-design-uni'
import { createUsingPost, queryBySessionIdUsingPost } from '@/service/ai'
const toast = useToast()
const message = useMessage('classPractise')
const totalCount = ref(90)
const wordList = ref([])
// 加载状态
const loading = ref(false)
// 没有更多数据
const noMore = ref(false)
const classId = ref()
const coursewareId = ref()
const searchForm = ref({
  pageSize: 10,
  pageNo: 1,
  attendClassPlanId: classId.value,
})
// 需要课堂练习的单词集合
const currentClickWordId = ref()
const dictStore = useDictStore()
const isGetAIpractise = ref(undefined)
const learnedWordsCount = computed(() => {
  return wordList.value.filter((word) => word.isCompletedLearn === '1').length
})
const PractiseSessionId = ref('')
// 单词分页查询
const searchWords = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await attendclassWordNeedLearnPageUsingPost({
      body: {
        ...searchForm.value,
      },
    })
    const needLearnWords = res.data?.items?.map((item) => {
      return {
        ...item,
        isNeedPractise: '0',
      }
    })
    if (isLoadMore) {
      wordList.value = [...wordList.value, ...needLearnWords]
    } else {
      wordList.value = needLearnWords
    }

    // 更新总记录数
    totalCount.value = (res.data as any).total || needLearnWords.length

    noMore.value = needLearnWords.length < searchForm.value.pageSize
    if (isLoadMore) {
      searchForm.value.pageNo++
    }
  } catch (error) {
    console.error('搜索老师失败', error)
    uni.showToast({
      title: '搜索失败，请稍后重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}
// 标记当前点击的单词（高亮）
const clickWord = (word) => {
  currentClickWordId.value = word.id
}
// 双击更新学习进度，
const refishLearnProgress = async (word) => {
  console.log('word,执行了', word)
  try {
    const res = await attendclassUpdateWordProssUsingPost({
      body: {
        wordId: word.id,
        wordContent: word.word,
        isCorrect: 1,
        attendClassPlanId: Number(classId.value),
        coursewareId: Number(coursewareId.value),
      },
    })
    if (res.code === 200) {
      refishWordList()
    }
  } catch (error) {
    console.error('更新学习进度失败', error)
  }
}
defineOptions({
  name: 'ChooseWordsPage',
})

// 计算选中的单词数量
const selectedCount = computed(() => {
  return wordList.value.filter((word) => word.isNeedLearn === 1).length
})

// 返回继续筛选单词
const goBackChooseWords = () => {
  navigateBack()
}
// 切换单词选中状态
const toggleWord = (index: number) => {
  if (wordList.value[index].isNeedPractise === '1') {
    wordList.value[index].isNeedPractise = '0'
  } else {
    wordList.value[index].isNeedPractise = '1'
  }
}
// 查询试卷生成action
const fetchPractiseDetailBySessionId = async (sessionId) => {
  try {
    const res = await queryBySessionIdUsingPost({ body: { sessionId } })
    if (res.data.item.examId !== null) {
      isGetAIpractise.value = true
    }
  } catch (err) {
    console.log('获取试卷详情失败', err)
  }
}
const handleToPractice = () => {
  navigateToSub(
    `/ai-practise-page/ai-practise-page?sessionId=${PractiseSessionId.value}&type=${'classPractise'}`,
  )
}
// 先查询试卷是否生成信息，生成则跳转，否则留在该页面
// 获取AI学习试卷
const getAIPractiseBank = async (wordList) => {
  try {
    console.log(" uni.getStorageSync('userInfo')", uni.getStorageSync('userInfo'))
    const res = await createUsingPost({
      body: {
        studentId: uni.getStorageSync('studentId'),
        teacherId: uni.getStorageSync('userInfo')?.userId,
        words: wordList,
        attendId: Number(classId.value),
      },
    })
    if (res.code === 200) {
      const sessionId = res?.data?.item?.sessionId
      toast.success('试卷正在生成中，请稍后...')
      console.log('sessionId', sessionId)
      isGetAIpractise.value = false
      let interval = null
      // 由于时间关系，15秒后再开始请求，减少请求次数，15秒钟之后每隔2秒钟执行一次，根据状态
      setTimeout(() => {
        interval = setInterval(() => {
          console.log('dafdasa')
          if (isGetAIpractise.value) {
            console.log('isGetAIpractise.value', isGetAIpractise.value)
            clearInterval(interval)
            PractiseSessionId.value = sessionId
            return
          }
          fetchPractiseDetailBySessionId(sessionId)
        }, 2000)
      }, 20000)
      // 做安全处理，如果一分钟都还没请求到数据，则清除定时器
      setTimeout(() => {
        console.log('一分钟都没请求到数据')
        clearInterval(interval)
        isGetAIpractise.value = undefined
      }, 60000)
    }
  } catch (err) {
    console.log('err', err)
  }
}
// 结束课程
const handleEndClass = async () => {
  try {
    const res = await attendclassEndClassPlanUsingPost({
      body: {
        id: Number(classId.value),
      },
    })
    if (res.code === 200) {
      showToast('下课成功')
      navigateToSub(`/practise-result/practise-result?attendClassPlanId=${classId.value}`)
    }
  } catch (error) {
    console.log('error', error)
  }
}
// 课堂练习单词选择
const gennerateClassPractise = () => {
  message
    .confirm({
      title: '课堂练习',
    })
    .then(async () => {
      const pracitseWords = wordList.value
        .filter((word) => word.isNeedPractise === '1')
        .map((item) => item.word)
      // 调用AI学习接口，生成课堂试卷
      getAIPractiseBank(pracitseWords.join(','))
    })
    .catch((err) => {
      console.log('err', err)
    })
}
const playSound = (word) => {
  if (word.mp3) {
    const audio = new Audio(word.mp3)
    audio.play().catch((err) => {
      console.log('播放音频失败', err)
    })
  }
  console.log('播放音频', word.mp3)
}
// 返回上一页
const handleBack = () => {
  navigateBack()
}

// 开始上课
// const handleStartClass = async () => {
//   // 先根据选择结果，过滤需要和不需要的单词
//   if (wordList.value.length > 0) {
//     const needLearnWordIds = []
//     const noNeedLearnWordIds = []
//     wordList.value.forEach((word) => {
//       if (word.isNeedLearn === 1) {
//         needLearnWordIds.push(word.id)
//       } else {
//         noNeedLearnWordIds.push(word.id)
//       }
//     })
//     console.log('needLearnWordIds', needLearnWordIds, noNeedLearnWordIds)
//     const res = await attendclassSelectWordsLearnUsingPost({
//       body: {
//         attendClassPlanId: Number(classId.value),
//         needLearnWordIds,
//         notNeedLearnWordIds: noNeedLearnWordIds,
//       },
//     })
//     if (res.code === 200) {
//       showToast('选择成功进入学习页面')
//     }
//   }

//   showToast(`已选择${selectedCount.value}个单词，开始上课`)
//   // 这里可以跳转到上课页面
//   // navigateToSub('/class/class', { words: selectedWords })
// }

// 获取单词列表

const getInit = async () => {
  await searchWords(true)
}
onLoad((option) => {
  if (option.classId) {
    console.log('option.classId', option.classId)
    classId.value = option.classId
    searchForm.value.attendClassPlanId = classId.value
  }
  if (option.coursewareId) {
    coursewareId.value = option.coursewareId
  }
})
const onScrolltolower = () => {
  console.log('是否触发了上拉加载更多')
  if (!noMore.value) {
    searchWords(true)
  }
}
const refishWordList = async () => {
  const refishSearchForm = {
    pageSize: 0,
    pageNo: 0,
    attendClassPlanId: classId.value,
  }
  const pageNo = searchForm.value.pageNo
  const pageSize = searchForm.value.pageSize * pageNo
  refishSearchForm.pageNo = 1
  refishSearchForm.pageSize = pageSize
  const res = await attendclassWordNeedLearnPageUsingPost({
    body: {
      ...refishSearchForm,
    },
  })
  if (res.code === 200 && res.data.items.length > 0) {
    wordList.value = res.data.items
  }
}
onMounted(() => {
  getInit()
  console.log('dictStore.dictData', dictStore.dictData)
  // 监听上拉触底事件
})
</script>
<style lang="css" scoped>
.word-container:hover {
  border: 1px solid #b7b3b3;
}
.isActive {
  background-color: #b7b1b1;
  opacity: 0.8;
  border: 2px solid #b7b3b3;
}
:deep(.wd-message-box .wd-message-box__container) {
  width: 600rpx;
}

:deep(.wd-message-box .wd-message-box__flex) {
  display: flex;
  justify-content: center;
}
</style>
