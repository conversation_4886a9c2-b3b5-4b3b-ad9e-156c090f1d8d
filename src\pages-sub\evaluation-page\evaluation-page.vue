<route lang="json5">
{
  layout: 'normal',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="w">
    <view class="mt-16 flex justify-between flex-col items-center">
      <view class="text-xl text-[#272830]">词汇量测评</view>
      <h2 class="text-[16px] mt-5 mb-3 text-[#8C8C8C]">花几分钟测试一下，定位您的词汇水平</h2>
      <image src="/static/img/common/ceping.svg" class="mx-auto w-[290px] h-[290px]" />
    </view>
    <wd-button
      size="large"
      block
      :round="false"
      type="primary"
      custom-class="!mx-14"
      @click="handleExperience"
    >
      开始
    </wd-button>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { navigateToSub } from '@/utils'
import { RoleEmu, useRoleStore } from '@/store'
const handleExperience = () => {}
</script>
