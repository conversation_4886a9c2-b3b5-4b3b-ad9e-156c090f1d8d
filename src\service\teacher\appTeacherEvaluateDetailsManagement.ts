/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【老师】为指定学生创建评测 POST /app/teacher/evaluatedetails/create */
export async function evaluatedetailsCreateUsingPost({
  body,
  options,
}: {
  body: API.StudentEvaluateDetialCreateByTeacherReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/app/teacher/evaluatedetails/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
