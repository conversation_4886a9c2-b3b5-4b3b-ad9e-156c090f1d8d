/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】新增题库 POST /pc/questionbank/create */
export async function pcQuestionbankCreateUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseQuestionBankCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/questionbank/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】下载题库导入模板 POST /pc/questionbank/downTemplate */
export async function pcQuestionbankDownTemplateUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<string>('/pc/questionbank/downTemplate', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【运营】获取题库详情 POST /pc/questionbank/get */
export async function pcQuestionbankGetUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseQuestionBankGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStudentPractiseQuestionBankBaseRespVO>(
    '/pc/questionbank/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】批量导入题库 POST /pc/questionbank/import */
export async function pcQuestionbankOpenApiImportUsingPost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pcQuestionbankOpenApiImportUsingPostParams;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/questionbank/import', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 【运营】修改题库 POST /pc/questionbank/modify */
export async function pcQuestionbankModifyUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseQuestionBankModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/questionbank/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】分页查询题库 POST /pc/questionbank/page */
export async function pcQuestionbankPageUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseQuestionBankPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStudentPractiseQuestionBankBaseRespVO>(
    '/pc/questionbank/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】上架/下架题库 POST /pc/questionbank/updateStatus */
export async function pcQuestionbankUpdateStatusUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseQuestionBankGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/questionbank/updateStatus', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
