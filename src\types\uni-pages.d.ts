/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/mine/mine" |
       "/pages/self-study/self-study" |
       "/pages-sub/ai-practise-page/ai-practise-page" |
       "/pages-sub/attend-class/attend-class" |
       "/pages-sub/book-class/book-class" |
       "/pages-sub/breakfast/breakfast" |
       "/pages-sub/chat/chat" |
       "/pages-sub/choose-words-page/choose-words-page" |
       "/pages-sub/complaint/complaint" |
       "/pages-sub/courseware-detail/courseware-detail" |
       "/pages-sub/courseware-forget-detail/courseware-forget-detail" |
       "/pages-sub/evaluate/evaluate" |
       "/pages-sub/evaluation-page/evaluation-page" |
       "/pages-sub/login/login" |
       "/pages-sub/message-list/message-list" |
       "/pages-sub/more-teacher/more-teacher" |
       "/pages-sub/my-complaints/my-complaints" |
       "/pages-sub/practise-result/practise-result" |
       "/pages-sub/recharge/recharge" |
       "/pages-sub/register/register" |
       "/pages-sub/role-select/role-select" |
       "/pages-sub/start-answer/start-answer" |
       "/pages-sub/start-learn-page/start-learn-page" |
       "/pages-sub/student-courseware-detail/student-courseware-detail" |
       "/pages-sub/teacher-class/teacher-class" |
       "/pages-sub/teacher-courseware-detail/teacher-courseware-detail" |
       "/pages-sub/teacher-detail/teacher-detail" |
       "/pages-sub/training-result/training-result";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
