<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
// import { unique } from './service/index/main'

onLaunch(() => {
  // const uniqueToken = uni.getStorageSync('uniqueToken')
  // if (!uniqueToken) {
  //   unique().then((res) => {
  //     uni.setStorageSync('uniqueToken', res.token)
  //   })
  // }
})
onShow(() => {
  // console.log('App Show')
})
onHide(() => {
  // console.log('App Hide')
})
</script>

<style lang="scss">
image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
view {
  box-sizing: border-box;
}
// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.btn-bg {
  background: linear-gradient(90deg, #3c5bf6 0%, #7b9df1 100%) !important;
  box-shadow: 0rpx 16rpx 24rpx 0rpx rgba(0, 8, 170, 0.16) !important;
}
.container {
  width: 100%;
  min-height: 100%;
  overflow: hidden;
  background-color: #f8f8f8;
  background-repeat: no-repeat; /* 不重复 */
  background-position: top; /* 居中对齐 */
  background-size: 100% auto; /* 覆盖整个页面 */
}
#app {
  max-width: 750px;
  margin: 0 auto;
}

.uni-scroll-view-content {
  height: auto !important;
}
.wd-picker__placeholder {
  font-size: 28rpx;
}
.form {
  .wd-picker.is-large {
    position: relative;
    .wd-picker__cell {
      padding: var(--wot-input-cell-padding-large, 12px);
    }
    &::after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1px;
      content: '';
      background: var(--wot-input-border-color, #dadada);
      transition: background-color 0.2s ease-in-out;
      transform: scaleY(0.5);
    }
  }
}
</style>
