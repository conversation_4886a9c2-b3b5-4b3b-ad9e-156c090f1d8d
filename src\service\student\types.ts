/* eslint-disable */
// @ts-ignore

export type AttendClassPlanAntiforgetStatisticsRespVO = {
  /** 上课计划id */
  id?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师id */
  teacherId?: number;
  /** 训练名称、课件资料名称 */
  coursewareName?: string;
  /** 复习抗遗忘单词总数 */
  wordCount?: number;
  /** 复习抗遗忘单词正确数 */
  correctCount?: number;
  /** 复习抗遗忘单词错误数 */
  errorCount?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
};

export type AttendClassPlanDetialListRespVO = {
  /** 上课日期 */
  classDate?: string;
  /** 上课计划详情列表 */
  classPlanDetialList?: AttendClassPlanDetialRespVO[];
};

export type AttendClassPlanDetialRespVO = {
  /** 上课计划id */
  id?: number;
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 课程名称 */
  coursewareName?: string;
  /** 老师名称 */
  teacherName?: string;
  /** 学生名称 */
  studentName?: string;
  /** 上课日期 */
  classDate?: string;
  startTime?: LocalTime;
  endTime?: LocalTime;
  /** 实际开始时间 */
  actualStartTime?: string;
  /** 实际结束时间 */
  actualEndTime?: string;
  /** 上课状态：0-未开始，1-进行中，2-已结束，3-已取消 */
  progress?: string;
  /** 腾讯会议号1 */
  tencentMeetingNo?: string;
  /** 腾讯会议号2 */
  tencentMeetingNoTwo?: string;
  /** 资料id */
  coursewareId?: number;
  /** 最新单词id */
  latestWordId?: number;
  /** 评价状态 0 未评价 1 已评价 */
  evaluateStatus?: string;
  /** 老师id */
  teacherId?: number;
  /** 是否要做抗遗忘： 0 不做 1 做 */
  isToDoAntiForget?: string;
  /** 是否已经做了抗遗忘： 0 未做 1 已做 */
  isDoneAntiForget?: string;
};

export type AttendClassPlanGetReqVO = {
  /** 上课计划id */
  id: number;
};

export type AttendClassPlanStartOrEndReqVO = {
  /** 上课计划id */
  id: number;
};

export type AttendClassPlanStatisticsRespVO = {
  /** 上课计划id */
  id?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师id */
  teacherId?: number;
  /** 学新单词总数 */
  newWordsCount?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 是否要做抗遗忘： 0 不做 1 做 */
  isToDoAntiForget?: string;
  /** 是否已经做了抗遗忘： 0 未做 1 已做 */
  isDoneAntiForget?: string;
};

export type ClassPlanVO = {
  trainingTime?: LocalTime;
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 预约状态 待确认：0，预约成功：1 */
  reservationStatus?: string;
};

export type CompletedClassPlanPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
};

export type ConfigBaseByStuAppRespVO = {
  /** 系统价格配置 */
  sysPriceConfigDOs?: SysPriceConfigDO[];
  /** 充值次数选项1 */
  option1?: number;
  /** 充值次数选项2 */
  option2?: number;
  /** 充值次数选项3 */
  option3?: number;
  /** 充值次数选项4 */
  option4?: number;
  /** 充值次数选项5 */
  option5?: number;
  /** 充值次数选项6 */
  option6?: number;
  /** 充值次数选项7 */
  option7?: number;
};

export type CourseReservationDetialRespVO = {
  /** id */
  id?: number;
  /** 课程时长类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 预约次数 */
  reservationCount?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生id */
  studentId?: number;
  /** 老师id */
  teacherId?: number;
  /** 课件资料名称 */
  coursewareName?: string;
  /** 学生姓名账号 */
  studentNameAccount?: string;
  /** 老师姓名账号 */
  teacherNameAccount?: string;
  /** 约课状态 预约待确认：0，预约成功：1,预约失败：2，取消待确认：3，取消成功：4 */
  status?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 间隔时间 0-每天 1-每周 */
  intervalTime?: string;
  /** 首次日期 */
  firstDate?: string;
  /** 资料id */
  articleTitleId?: number;
  trainingTime?: LocalTime;
  /** 支付方式 0-线上  1-线下 */
  payWay?: string;
  /** 价格 */
  price?: number;
};

export type CoursewareByAdminPageRespVO = {
  /** 课件资料ID */
  id?: number;
  /** 资料名称 */
  name?: string;
  /** 课件资料年级 */
  courseGrade?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建人 */
  creator?: string;
  /** 更新人 */
  updater?: string;
};

export type CoursewareDetialGetReqVO = {
  /** 资料id */
  coursewareId: number;
};

export type CoursewareWordPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 上课计划id */
  classPlanId?: number;
  /** 单词查询类型：0.抗遗忘复习 1.学习新单词 */
  wordQueryType?: string;
};

export type CoursewareWordPageRespVO = {
  /** 单词id */
  id?: number;
  /** 单词 */
  word?: string;
  /** 中文释义 */
  chinese?: string;
  /** 音频地址 */
  mp3?: string;
  /** 是否需要学习：0 不需要 ，1 需要 */
  isNeedLearn?: string;
  /** 是否已学习：0 否 ，1 是 */
  isCompletedLearn?: string;
  /** 是否已完成抗遗忘学习：0 否 ，1 是 */
  isCompletedAntiforget?: string;
};

export type DataWrapperAttendClassPlanAntiforgetStatisticsRespVO = {
  item?: AttendClassPlanAntiforgetStatisticsRespVO;
  items?: AttendClassPlanAntiforgetStatisticsRespVO[];
};

export type DataWrapperAttendClassPlanDetialRespVO = {
  item?: AttendClassPlanDetialRespVO;
  items?: AttendClassPlanDetialRespVO[];
};

export type DataWrapperAttendClassPlanStatisticsRespVO = {
  item?: AttendClassPlanStatisticsRespVO;
  items?: AttendClassPlanStatisticsRespVO[];
};

export type DataWrapperBoolean = {
  item?: boolean;
  items?: boolean[];
};

export type DataWrapperConfigBaseByStuAppRespVO = {
  item?: ConfigBaseByStuAppRespVO;
  items?: ConfigBaseByStuAppRespVO[];
};

export type DataWrapperCourseReservationDetialRespVO = {
  item?: CourseReservationDetialRespVO;
  items?: CourseReservationDetialRespVO[];
};

export type DataWrapperCoursewareByAdminPageRespVO = {
  item?: CoursewareByAdminPageRespVO;
  items?: CoursewareByAdminPageRespVO[];
};

export type DataWrapperListAttendClassPlanDetialListRespVO = {
  item?: AttendClassPlanDetialListRespVO[];
  items?: AttendClassPlanDetialListRespVO[][];
};

export type DataWrapperListAttendClassPlanDetialRespVO = {
  item?: AttendClassPlanDetialRespVO[];
  items?: AttendClassPlanDetialRespVO[][];
};

export type DataWrapperLong = {
  item?: number;
  items?: number[];
};

export type DataWrapperStudentEvaluateWordGetRespVO = {
  item?: StudentEvaluateWordGetRespVO;
  items?: StudentEvaluateWordGetRespVO[];
};

export type DataWrapperStudentPractiseStatisticsRespVO = {
  item?: StudentPractiseStatisticsRespVO;
  items?: StudentPractiseStatisticsRespVO[];
};

export type DataWrapperStudentVipConfigBaseRespVO = {
  item?: StudentVipConfigBaseRespVO;
  items?: StudentVipConfigBaseRespVO[];
};

export type DataWrapperStuUserGetRespVO = {
  item?: StuUserGetRespVO;
  items?: StuUserGetRespVO[];
};

export type DataWrapperStuWalletInfoRespVo = {
  item?: StuWalletInfoRespVo;
  items?: StuWalletInfoRespVo[];
};

export type DataWrapperTeacherAndStudentRespVO = {
  item?: TeacherAndStudentRespVO;
  items?: TeacherAndStudentRespVO[];
};

export type DataWrapperTeacherComplaintBaseRespVO = {
  item?: TeacherComplaintBaseRespVO;
  items?: TeacherComplaintBaseRespVO[];
};

export type DataWrapperTeacherDetialRespVO = {
  item?: TeacherDetialRespVO;
  items?: TeacherDetialRespVO[];
};

export type DataWrapperTeacherEvaluateBaseRespVO = {
  item?: TeacherEvaluateBaseRespVO;
  items?: TeacherEvaluateBaseRespVO[];
};

export type DataWrapperWordStatisticsRespVO = {
  item?: WordStatisticsRespVO;
  items?: WordStatisticsRespVO[];
};

export type DataWrapperWxPayInfoVo = {
  item?: WxPayInfoVo;
  items?: WxPayInfoVo[];
};

export type LocalTime = {
  hour?: number;
  minute?: number;
  second?: number;
  nano?: number;
};

export type PageDto = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
};

export type PageResultCourseReservationDetialRespVO = {
  /** 数据列表 */
  items?: CourseReservationDetialRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultCoursewareWordPageRespVO = {
  /** 数据列表 */
  items?: CoursewareWordPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultResponsePageResultCourseReservationDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCourseReservationDetialRespVO;
};

export type PageResultResponsePageResultCoursewareWordPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCoursewareWordPageRespVO;
};

export type PageResultResponsePageResultStudentPractiseBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStudentPractiseBaseRespVO;
};

export type PageResultResponsePageResultStudentPractiseDetailBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStudentPractiseDetailBaseRespVO;
};

export type PageResultResponsePageResultStuSearchTeacherPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStuSearchTeacherPageRespVO;
};

export type PageResultResponsePageResultStuWalletDetailsRespVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStuWalletDetailsRespVo;
};

export type PageResultResponsePageResultTeacherComplaintBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultTeacherComplaintBaseRespVO;
};

export type PageResultStudentPractiseBaseRespVO = {
  /** 数据列表 */
  items?: StudentPractiseBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStudentPractiseDetailBaseRespVO = {
  /** 数据列表 */
  items?: StudentPractiseDetailBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStuSearchTeacherPageRespVO = {
  /** 数据列表 */
  items?: StuSearchTeacherPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStuWalletDetailsRespVo = {
  /** 数据列表 */
  items?: StuWalletDetailsRespVo[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultTeacherComplaintBaseRespVO = {
  /** 数据列表 */
  items?: TeacherComplaintBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type RechargeReqVo = {
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration: string;
  /** 课时节数 */
  classNumber: number;
};

export type ReservationGetReqVO = {
  /** 约课记录id */
  id: number;
};

export type ResultAttendClassPlanAntiforgetStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanAntiforgetStatisticsRespVO;
};

export type ResultAttendClassPlanDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanDetialRespVO;
};

export type ResultAttendClassPlanStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanStatisticsRespVO;
};

export type ResultBoolean = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperBoolean;
};

export type ResultConfigBaseByStuAppRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperConfigBaseByStuAppRespVO;
};

export type ResultCourseReservationDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCourseReservationDetialRespVO;
};

export type ResultCoursewareByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCoursewareByAdminPageRespVO;
};

export type ResultListAttendClassPlanDetialListRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListAttendClassPlanDetialListRespVO;
};

export type ResultListAttendClassPlanDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListAttendClassPlanDetialRespVO;
};

export type ResultLong = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperLong;
};

export type ResultStudentEvaluateWordGetRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStudentEvaluateWordGetRespVO;
};

export type ResultStudentPractiseStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStudentPractiseStatisticsRespVO;
};

export type ResultStudentVipConfigBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStudentVipConfigBaseRespVO;
};

export type ResultStuUserGetRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStuUserGetRespVO;
};

export type ResultStuWalletInfoRespVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperStuWalletInfoRespVo;
};

export type ResultTeacherAndStudentRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherAndStudentRespVO;
};

export type ResultTeacherComplaintBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherComplaintBaseRespVO;
};

export type ResultTeacherDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherDetialRespVO;
};

export type ResultTeacherEvaluateBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherEvaluateBaseRespVO;
};

export type ResultWordStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperWordStatisticsRespVO;
};

export type ResultWxPayInfoVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperWxPayInfoVo;
};

export type StudentCourseReservationCreateV2ReqVO = {
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration: string;
  /** 老师id */
  teacherId: number;
  /** 上课时间 */
  trainingTime: string;
  /** 训练日期集合 */
  trainingDateList: string[];
};

export type StudentCourseReservationPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  courseDuration?: string;
  /** 预约状态：0-待确认 1-成功 2-失败 3-取消待确认 4-已取消 */
  status?: string;
  /** 进度：0-未开始 1-进行中 2-已结束 */
  progress?: string;
  /** 间隔时间 0-每天 1-每周 */
  intervalTime?: string;
  /** 首次日期 */
  firstDate?: string;
  trainingTime?: LocalTime;
};

export type StudentEvaluateWordGetReqVO = {
  /** 评测单词id  */
  id?: number;
};

export type StudentEvaluateWordGetRespVO = {
  /** 评测单词id */
  id?: number;
  /** 等级：1 -初级, 2 -中级, 3 -高级, 4 -雅思  */
  level?: string;
  /** 单词内容 */
  word?: string;
  /** 备选答案1 */
  option1?: string;
  /** 备选答案2 */
  option2?: string;
  /** 备选答案3 */
  option3?: string;
  /** 备选答案4 */
  option4?: string;
  /** 备选答案5 */
  option5?: string;
  /** 第几个备选答案是正确答案：比如为1，表示 备选答案1是正确答案 */
  answer?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
};

export type StudentPractiseBaseRespVO = {
  /** 课堂试卷id */
  id?: number;
  /** 课程计划id */
  attendClassPlanId?: number;
  /** 名称 */
  name?: string;
  /** 题目数量 */
  num?: number;
  /** 创建时间 */
  createTime?: string;
};

export type StudentPractiseCreateReqVO = {
  /** 试卷名称 */
  name?: string;
  /** 版本 */
  version: string;
  /** 年级 */
  grade: string;
  /** 单元 */
  unit: string;
  /** 课程id */
  attendClassPlanId?: number;
  /** 题库数量，不传默认为：5 */
  num?: number;
};

export type StudentPractiseDetailBaseRespVO = {
  /** 课堂试卷题目id */
  id?: number;
  /** 所属版本 */
  version?: string;
  /** 所属年级 */
  grade?: string;
  /** 所属单元 */
  unit?: string;
  /** 题库内容 */
  content?: string;
  /** 备选答案1 */
  option1?: string;
  /** 备选答案2 */
  option2?: string;
  /** 备选答案3 */
  option3?: string;
  /** 备选答案4 */
  option4?: string;
  /** 备选答案5 */
  option5?: string;
  /** 第几个备选答案是正确答案：1 第一个是正确答案 */
  answer?: number;
  /** 学生的答案 */
  studentAnswer?: number;
  /** 是否正确：0-错误，1-正确 */
  isCorrect?: number;
};

export type StudentPractiseDetailDoingReqVO = {
  /** 课堂试卷题目id */
  id: number;
  /** 学生的答案 */
  studentAnswer: number;
};

export type StudentPractiseDetailPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 课堂试卷id */
  practiseId: number;
};

export type StudentPractisePageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 课程id */
  attendClassPlanId?: number;
};

export type StudentPractiseStatisticsReqVO = {
  /** 课堂试卷id */
  practiseId?: number;
};

export type StudentPractiseStatisticsRespVO = {
  /** 课堂试卷id */
  practiseId?: number;
  /** 题目数量 */
  num?: number;
  /** 答对题数 */
  correctNum?: number;
};

export type StudentVipConfigBaseRespVO = {
  /** id */
  id?: number;
  /** vip权益内容 */
  content?: string;
  /** 价格 */
  price?: number;
  /** 状态:0-下架，1-上架 */
  status?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者 */
  creator?: string;
  /** 更新者 */
  updater?: string;
};

export type StuSearchTeacherPageReqVO = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
  /** 评分 */
  score?: number;
  /** 学校名称 */
  schoolName?: string;
  /** 教师等级 */
  userLevel?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 学历 */
  education?: string;
};

export type StuSearchTeacherPageRespVO = {
  /** 老师id */
  teacherId?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师账号 */
  teacherAccount?: string;
  /** 学校名称 */
  schoolName?: string;
  /** 专业名称 */
  majorName?: string;
  /** 评分 */
  score?: number;
  /** 教师等级 */
  userLevel?: string;
  /** 审核状态 */
  auditStatus?: string;
  /** 学历 */
  education?: string;
  /** 30分钟课时金额 */
  thirtyIncomePrice?: number;
  /** 60分钟课时金额 */
  sixtyIncomePrice?: number;
  /** 体验课课时金额 */
  experienceIncomePrice?: number;
};

export type StuUserGetRespVO = {
  /** 学生ID */
  id?: number;
  /** 账户 */
  account?: string;
  /** 推广机构 */
  promotionOrg?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 家长姓名 */
  parentsName?: string;
  /** 家长电话 */
  parentsPhone?: string;
  /** 学校 */
  school?: string;
  /** 年级 */
  grade?: string;
  /** 是否参加体验课 0 未参加  1 已参加 */
  attendExperienceClass?: string;
  /** 水平等级：1-初级, 2-中级, 3-高级, 4-雅思  */
  evaluateLevel?: string;
  /** 是否vip: 0 非vip,1 vip */
  isVip?: string;
  /** vip到期时间 */
  vipExpireTime?: string;
  /** vip权益内容 */
  vipContent?: string;
  /** 出生年月 */
  birthday?: string;
  /** 所学习的教材版本 */
  textbookVersion?: string;
  createTime?: string;
  updateTime?: string;
};

export type StuWalletDetailsRespVo = {
  /** 创建时间 */
  time?: string;
  /** 内容 */
  content?: string;
  /** 时长 */
  duration?: number;
};

export type StuWalletInfoRespVo = {
  /** 60分钟的总课时数量 */
  sixtyMinutesTotalNumber?: number;
  /** 30分钟的总课时数量 */
  thirtyMinutesTotalNumber?: number;
  /** 60分钟的剩余未上课数量 */
  sixtyMinutesSurplusNumber?: number;
  /** 30分钟的剩余未上课数量 */
  thirtyMinutesSurplusNumber?: number;
  /** 60分钟的可用数量 */
  sixtyMinutesAvailableNumber?: number;
  /** 30分钟的可用数量 */
  thirtyMinutesAvailableNumber?: number;
};

export type SysPriceConfigDO = {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  id?: number;
  /** 课时名称 */
  durationName?: string;
  /** 课时类型：0-30分钟正课，1-60分钟正课，2-体验课 */
  durationType?: number;
  /** 课时价格 */
  price?: number;
  /** 课时折扣（8折显示为80） */
  discount?: number;
  /** 课时折扣价格 */
  discountPrice?: number;
  /** 备注 */
  remark?: string;
  /** 价格配置状态:0-下架，1-上架 */
  status?: string;
};

export type TeacherAndStudentRespVO = {
  /** 创建生成的账号 */
  account?: string;
  /** 创建生成的密码 */
  password?: string;
};

export type TeacherComplaintBaseRespVO = {
  /** 投诉id */
  id?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 投诉内容 */
  content?: string;
  /** 投诉状态：0-未处理，1-已处理 */
  status?: string;
  /** 处理方案 */
  handleScheme?: string;
  /** 投诉时间 */
  createTime?: string;
};

export type TeacherComplaintCreateReqVO = {
  /** 上课计划id */
  attendClassPlanId: number;
  /** 投诉内容 */
  content: string;
};

export type TeacherComplaintGetReqVO = {
  /** 投诉id */
  id: number;
};

export type TeacherComplaintPageByStuReqVO = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
};

export type TeacherDetialReqVO = {
  /** 老师id */
  id?: number;
};

export type TeacherDetialRespVO = {
  /** 老师id */
  id?: number;
  /** 老师姓名 */
  name?: string;
  /** 老师账号 */
  account?: string;
  /** 所在学校 */
  school?: string;
  /** 专业 */
  major?: string;
  /** 个人简介 */
  introduction?: string;
  /** 累计已带学生数 */
  studentCount?: number;
  /** 累计已授单词数 */
  wordCount?: number;
  /** 累计评分 */
  score?: number;
  /** 当天上课计划列表 */
  classPlans?: ClassPlanVO[];
};

export type TeacherEvaluateBaseRespVO = {
  /** 评价id */
  id?: number;
  /** 上课计划id */
  attendClassPlanId?: number;
  /** 学生id */
  studentId?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师id */
  teacherId?: number;
  /** 老师姓名 */
  teacherName?: string;
  /** 发音得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  pronunciationScore?: number;
  /** 态度得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  attitudeScore?: number;
  /** 感染力得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  appealScore?: number;
  /** 责任心得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  responsibilityScore?: number;
  /** 创建时间 */
  createTime?: string;
  /** 创建人 */
  creator?: string;
};

export type TeacherEvaluateCreateReqVO = {
  /** 上课计划id */
  attendClassPlanId: number;
  /** 老师id */
  teacherId: number;
  /** 发音得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  pronunciationScore: number;
  /** 态度得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  attitudeScore: number;
  /** 感染力得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  appealScore: number;
  /** 责任心得分: 1-1分，2-2分，3-3分，4-4分，5-5分 */
  responsibilityScore: number;
};

export type TeacherEvaluateGetReqVO = {
  /** 评价记录id */
  id: number;
};

export type UserLoginReqVO = {
  /** 账户 */
  account: string;
  /** 密码 */
  password: string;
};

export type UserLoginVo = {
  /** 用户编号 */
  userId?: number;
  /** 访问令牌 */
  accessToken?: string;
  /** 腾讯im的userSig */
  userSig?: string;
};

export type UserRegisterReqVO = {
  /** 推广码 */
  promotionCode?: string;
  /** 家长电话 */
  parentsPhone: string;
};

export type WordDetailsUpdateProcessReqVO = {
  /** 评测记录详情id */
  evaluateDetailsId: number;
  /** 评测单词id  */
  wordId: number;
  /** 学生答案 ，如果传空 就是不知道 */
  studentAnswer?: number;
};

export type WordStatisticsReqVO = {
  /** 评测记录详情id */
  id: number;
};

export type WordStatisticsRespVO = {
  /** 评测记录详情id */
  id?: number;
  /** 学生年龄 */
  age?: number;
  /** 学生性别 */
  sex?: string;
  /** 学生姓名 */
  name?: string;
  /** 年级 */
  grade?: string;
  /** 学生评测等级:1-初级, 2-中级, 3-高级, 4-雅思  */
  level?: string;
  /** 学生词汇量 */
  studentWordCount?: number;
  /** 初级词汇量 */
  level1WordCount?: number;
  /** 中级词汇量 */
  level2WordCount?: number;
  /** 高级词汇量 */
  level3WordCount?: number;
  /** 雅思词汇量 */
  level4WordCount?: number;
};

export type WxPayInfoVo = {
  /** 公众账号ID */
  appId?: string;
  /** 商户号 */
  mchId?: string;
  /** 订单详情扩展字符串 */
  packages?: string;
  /** 预支付交易会话ID */
  prepayId?: string;
  /** 随机字符串 */
  nonceStr?: string;
  /** 时间戳 */
  timeStamp?: string;
  /** 签名 */
  paySign?: string;
  /** 加密方式 */
  signType?: string;
};
