/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】获取上课抗遗忘复习统计详情 POST /pc/teacher/reservation/classPlanAntiforgetStatistics */
export async function pcTeacherReservationClassPlanAntiforgetStatisticsUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanAntiforgetStatisticsRespVO>(
    '/pc/teacher/reservation/classPlanAntiforgetStatistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询课程计划 POST /pc/teacher/reservation/classPlanPage */
export async function pcTeacherReservationClassPlanPageUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultAttendClassPlanDetialPageRespVO>(
    '/pc/teacher/reservation/classPlanPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】获取上课统计详情 POST /pc/teacher/reservation/classPlanStatistics */
export async function pcTeacherReservationClassPlanStatisticsUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanStatisticsRespVO>(
    '/pc/teacher/reservation/classPlanStatistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】去约课  POST /pc/teacher/reservation/create */
export async function pcTeacherReservationCreateUsingPost({
  body,
  options,
}: {
  body: API.CourseReservationCreateV2ReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/teacher/reservation/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】获取指定id的约课记录明细信息 POST /pc/teacher/reservation/get */
export async function pcTeacherReservationGetUsingPost({
  body,
  options,
}: {
  body: API.ReservationGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCourseReservationDetialRespVO>(
    '/pc/teacher/reservation/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询约课记录信息 POST /pc/teacher/reservation/page */
export async function pcTeacherReservationPageUsingPost({
  body,
  options,
}: {
  body: API.CourseReservationPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCourseReservationDetialRespVO>(
    '/pc/teacher/reservation/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
