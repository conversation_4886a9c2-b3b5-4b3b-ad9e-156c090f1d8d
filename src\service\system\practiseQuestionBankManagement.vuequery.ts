/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './practiseQuestionBankManagement';
import * as API from './types';

/** 【运营】新增题库 POST /pc/questionbank/create */
export function usePcQuestionbankCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】下载题库导入模板 POST /pc/questionbank/downTemplate */
export function usePcQuestionbankDownTemplateUsingPostMutation(options?: {
  onSuccess?: (value?: string) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankDownTemplateUsingPost,
    onSuccess(data: string) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】获取题库详情 POST /pc/questionbank/get */
export function usePcQuestionbankGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStudentPractiseQuestionBankBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankGetUsingPost,
    onSuccess(data: API.ResultStudentPractiseQuestionBankBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】批量导入题库 POST /pc/questionbank/import */
export function usePcQuestionbankOpenApiImportUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankOpenApiImportUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】修改题库 POST /pc/questionbank/modify */
export function usePcQuestionbankModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询题库 POST /pc/questionbank/page */
export function usePcQuestionbankPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStudentPractiseQuestionBankBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultStudentPractiseQuestionBankBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】上架/下架题库 POST /pc/questionbank/updateStatus */
export function usePcQuestionbankUpdateStatusUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcQuestionbankUpdateStatusUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
