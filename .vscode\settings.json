{
  "editor.tabSize": 2,
  "editor.fontSize": 13,
  "git.autofetch": true,
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "change-case.default": ["camelCase", "param-case", "snake_case", "PascalCase", "CONST_CASE"],
  "prettier.printWidth": 60,
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "explorer.confirmDelete": false,
  "diffEditor.ignoreTrimWhitespace": false,
  "git.suggestSmartCommit": false,
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "files.autoSave": "onFocusChange",
  "fileheader.customMade": {
    //此为头部注释
    "Description": "",
    "Version": "2.0",
    "Autor": "<PERSON><PERSON>",
    "Date": "Do not edit",
    "LastEditors": "<PERSON>",
    "LastEditTime": "Do not edit"
  },
  "fileheader.cursorMode": {
    // 函数注释
    "description": "",
    "param": "params",
    "return": ""
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "git.confirmSync": false,
  "bracketPairColorizer.depreciation-notice": false,
  "settingsSync.ignoredExtensions": [],
  "settingsSync.ignoredSettings": [],
  "easysass.formats": [
    {
      "format": "expanded",
      "extension": ".css"
    },
    {
      "format": "compressed",
      "extension": ".min.css"
    }
  ],
  "window.zoomLevel": 0.5, // 窗口的缩放
  "security.workspace.trust.untrustedFiles": "open",
  "search.exclude": {
    "**/node_modules": true //全局查询时忽略这个文件夹
  },
  "liveSassCompile.settings.autoprefix": [],
  "liveSassCompile.settings.savePath": "~",
  "code-runner.executorMapByGlob": {
    "pom.xml": "cd $dir && mvn clean package"
  },
  "editor.codeActionsOnSave": {}
}
