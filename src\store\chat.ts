// stores/chat.ts
import { defineStore } from 'pinia'
import TIM, { ChatSDK } from 'tim-js-sdk'

export const useChatStore = defineStore('chat', {
  state: () => ({
    isReady: false,
    chat: null as any,
    callback: (chat) => {},
  }),

  actions: {
    async getChatIns() {
      return new Promise((resolve, reject) => {
        if (this.isReady) {
          resolve(this.chat)
        } else {
          this.chat.on(TIM.EVENT.SDK_READY, () => {
            this.isReady = true
            resolve(this.chat)
          })
        }
      })
    },
    async login(userID: string, userSig: string) {
      if (!this.chat) {
        this.chat = TIM.create({ SDKAppID: import.meta.env.VITE_SDKAPPID })
      }
      if (this.isReady) {
        await this.chat.logout()
      }
      await this.chat.login({ userID, userSig })
    },
  },
})
