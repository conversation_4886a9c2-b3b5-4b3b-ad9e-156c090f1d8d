<route lang="json5">
{
  layout: 'common',
  style: {
    navigationBarTitleText: '课程资料详情',
  },
}
</route>

<template>
  <view class="bg-[#F5F5F5]">
    <!-- 课程资料基本信息 -->
    <view class="bg-white p-[32rpx] pt-0 mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">课程资料信息</view>
      <view v-if="coursewareInfo">
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">资料ID</view>
          <view class="text-[28rpx] font-medium">{{ coursewareInfo.id }}</view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">资料名称</view>
          <view class="text-[28rpx] font-medium">{{ coursewareInfo.name || '未命名资料' }}</view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">创建时间</view>
          <view class="text-[28rpx] font-medium">{{ coursewareInfo.createTime || '--' }}</view>
        </view>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">
        正在加载课程资料信息...
      </view>
    </view>

    <!-- 单词列表 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">单词列表</view>
      <view v-if="wordList && wordList.length > 0">
        <view
          v-for="(word, index) in wordList"
          :key="index"
          class="p-[16rpx] border-b border-[#eee] last:border-b-0"
        >
          <view class="flex justify-between items-center mb-[8rpx]">
            <view class="flex items-center">
              <view class="text-[32rpx] font-medium">{{ word.word }}</view>
              <wd-icon
                name="sound"
                size="36rpx"
                color="#8000FF"
                @click="playWordSound(word)"
                class="ml-[8rpx] cursor-pointer mt-1"
              ></wd-icon>
            </view>
            <!-- 单词进度更新操作区域 -->
            <view class="mt-[16rpx]">
              <view class="flex items-center mb-[8rpx]">
                <wd-radio-group
                  v-model="word.isCorrect"
                  shape="dot"
                  @change="handleUpdateWordProgress(word)"
                >
                  <wd-radio :value="1"></wd-radio>
                </wd-radio-group>
              </view>
            </view>
          </view>
          <view class="text-[28rpx] text-[#666]">{{ word.chinese }}</view>
        </view>
      </view>
      <view v-else-if="loading" class="text-center py-[40rpx] text-[28rpx] text-[#999]">
        正在加载单词列表...
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无单词数据</view>
    </view>

    <!-- 分页控制 -->
    <view
      v-if="wordList && wordList.length > 0"
      class="bg-white p-[32rpx] mb-[20rpx] flex justify-between"
    >
      <wd-button
        size="small"
        type="primary"
        plain
        :disabled="currentPage <= 1"
        @click="handlePrevPage"
      >
        上一页
      </wd-button>
      <view class="text-[28rpx] flex items-center">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
      </view>
      <wd-button
        size="small"
        type="primary"
        plain
        :disabled="currentPage >= totalPages"
        @click="handleNextPage"
      >
        下一页
      </wd-button>
    </view>

    <!-- 课堂操作 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[24rpx]">课堂操作</view>
      <view class="flex justify-between">
        <wd-button
          v-if="currentClass.progress !== '2'"
          type="primary"
          custom-class="w-[300rpx]!"
          @click="handleStartClass"
        >
          {{ currentClass?.progress === '0' ? '开始上课' : '继续上课' }}
        </wd-button>
        <wd-button
          v-if="currentClass.progress === '2'"
          type="primary"
          custom-class="w-[300rpx]!"
          @click="handleTopractise"
        >
          课后练习
        </wd-button>
        <wd-button
          type="error"
          :disabled="currentClass.progress === '2'"
          custom-class="w-[300rpx]!"
          @click="handleEndClass"
        >
          {{ currentClass.progress !== '2' ? '结束上课' : '已结束' }}
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'
import { navigateTo, showToast } from '@/utils'
import {
  attendclassGetCoursewareDetialUsingPost,
  attendclassUpdateWordProssUsingPost,
  attendclassEndClassPlanUsingPost,
  attendclassStartClassPlanUsingPost,
  attendclassGetUsingPost,
  attendclassWordAllPageUsingPost,
} from '@/service/teacher/appTeacherAttendClassPlanManagement'
import { AttendClassPlanDetialRespVO } from '@/service/teacher'
import { getItemClass } from 'wot-design-uni/components/wd-calendar-view/utils'

defineOptions({
  name: 'TeacherCoursewareDetail',
})

// 页面参数
const coursewareId = ref<number | null>(null)
const coursewareInfo = ref<any>(null)
const wordList = ref<any[]>([])
const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = ref(1)

// 当前课程ID
const attendClassPlanId = ref<number | null>(null)
const currentClass = ref<AttendClassPlanDetialRespVO | null>()

// 获取单词列表
const fetchWordList = async () => {
  if (!coursewareId.value) {
    showToast('无法获取课程资料ID')
    return
  }
  loading.value = true
  // 获取课程资料详情
  const resCourseware = await attendclassGetCoursewareDetialUsingPost({
    body: {
      coursewareId: coursewareId.value,
    },
  })

  coursewareInfo.value = {
    ...resCourseware.data.item,
    createTime: dayjs(resCourseware.data.item.createTime).format('YYYY-MM-DD HH:mm:ss'),
  }

  // 获取单词列表
  const res = await attendclassWordAllPageUsingPost({
    body: {
      classPlanId: attendClassPlanId.value,
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      wordQueryType: '1',
    },
  })

  if (res.code === 200) {
    if (res.data) {
      // 为每个单词添加isCorrect属性
      wordList.value = res.data.items?.map((item: any) => ({
        ...item,
        isCorrect: null, // 默认未选择
        operationResult: null, // 操作结果，初始为null
      }))
      // 计算总页数
      if (res.data.counts) {
        totalPages.value = Math.ceil(res.data.counts / pageSize.value)
      }
    }
  } else {
    showToast(res.message)
  }
  loading.value = false
}

// 上一页
const handlePrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchWordList()
  }
}

// 下一页
const handleNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchWordList()
  }
}

// 播放单词声音
const playWordSound = (item: { mp3: any; word: any }) => {
  const { mp3, word } = item
  if (!mp3) {
    showToast('该单词暂无发音')
    return
  }
  try {
    const audio = new Audio(mp3)
    audio.play()
  } catch (error) {
    console.error('播放音频失败:', error)
    showToast('播放音频失败')
  }
}

// 更新单词进度
const handleUpdateWordProgress = async (word: any) => {
  if (!coursewareId.value || !word.id) {
    showToast('无法获取必要的单词信息')
    return
  }

  if (!attendClassPlanId.value) {
    showToast('无法获取课程计划ID')
    return
  }

  const res = await attendclassUpdateWordProssUsingPost({
    body: {
      attendClassPlanId: attendClassPlanId.value,
      coursewareId: coursewareId.value,
      wordId: word.id,
      wordContent: word.word,
      isCorrect: 1,
    },
  })

  if (res.code === 200) {
    showToast('单词进度更新成功')
  } else {
    showToast(res.message)
  }
}

// 开始上课
const handleStartClass = async () => {
  if (!currentClass.value?.id) {
    showToast('无法获取课程ID')
    return
  }
  const res = await attendclassStartClassPlanUsingPost({
    body: {
      id: currentClass.value.id,
    },
  })

  if (res.code === 200) {
    showToast('上课开始成功')
  } else {
    showToast(res.message)
  }
}
const handleTopractise = () => {
  console.log('跳转生成试卷页面')
  navigateTo(`/self-study/self-study?classId=${currentClass.value.id}`)
}
// 结束上课
const handleEndClass = async () => {
  if (!currentClass.value?.id) {
    showToast('无法获取课程ID')
    return
  }
  const res = await attendclassEndClassPlanUsingPost({
    body: {
      id: currentClass.value.id,
    },
  })

  if (res.code === 200) {
    showToast('下课成功')
  } else {
    showToast(res.message)
  }
}

// 获取课程信息
const fetchClassInfo = async () => {
  if (attendClassPlanId.value) {
    const detailRes = await attendclassGetUsingPost({
      body: {
        id: attendClassPlanId.value,
      },
    })

    if (detailRes.code === 200 && detailRes.data?.item) {
      currentClass.value = detailRes.data.item
    } else {
      showToast(detailRes.message)
    }
  }
}

// 页面加载
onLoad((options: any) => {
  if (options.coursewareId) {
    coursewareId.value = Number(options.coursewareId)
  }
  // 如果有课程ID参数
  if (options.classId) {
    attendClassPlanId.value = Number(options.classId)
  }
  fetchClassInfo()
  fetchWordList()
})
</script>
