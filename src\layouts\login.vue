<template>
  <wd-config-provider :themeVars="theme">
    <scroll-view :scroll-y="true" class="h-100vh relative">
      <view class="bg-gradient-primary w-full h-[100px] !absolute !top-0"></view>
      <view class="h-full" :style="{ marginTop: safeAreaInsets?.top + 'px' }">
        <wd-navbar :bordered="false" custom-class="!bg-transparent" :left-arrow="!isTab" fixed>
          <template #left>
            <view
              class="bg-[#064DEA1A] text-[#064DEA] rounded-[22px] px-3 h-[36px] flex items-center gap-1"
              @click="switchIdentity"
            >
              <wd-icon name="translate-bold" custom-class="text-[16px]"></wd-icon>
              切换身份
            </view>
          </template>
        </wd-navbar>
        <slot></slot>
      </view>
    </scroll-view>
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { themeVars } from '@/constant'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { getSystemInfoSync } from '@/utils/system'
import { navigateToSub } from '@/utils'
const theme: ConfigProviderThemeVars = { ...themeVars }
const { title, getCurrentPage } = useNavigation()
const { isTab = false, style } = getCurrentPage()
const { safeAreaInsets } = getSystemInfoSync()

onLoad(() => {})
const switchIdentity = () => {
  navigateToSub('/role-select/role-select?to=login')
}
</script>
