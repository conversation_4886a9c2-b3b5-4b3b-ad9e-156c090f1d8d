/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【系统管理、机构】给角色分配菜单 POST /pc/system/role/allocMenu */
export async function pcSystemRoleAllocMenuUsingPost({
  body,
  options,
}: {
  body: API.SystemRoleAllocMenuReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultInteger>('/pc/system/role/allocMenu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、运营、财务、机构、老师、学生】获取角色相关菜单 POST /pc/system/role/getMenuList */
export async function pcSystemRoleGetMenuListUsingPost({
  body,
  options,
}: {
  body: API.SystemRoleGetMenuListReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListString>('/pc/system/role/getMenuList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、机构】获取角色列表 POST /pc/system/role/page */
export async function pcSystemRolePageUsingPost({
  body,
  options,
}: {
  body: API.SystemRolePageRespVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSystemRoleDO>(
    '/pc/system/role/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
