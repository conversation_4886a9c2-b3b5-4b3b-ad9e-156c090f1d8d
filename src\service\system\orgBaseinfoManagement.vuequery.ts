/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './orgBaseinfoManagement';
import * as API from './types';

/** 【运营】添加推广机构信息 POST /pc/orgBaseinfo/create */
export function usePcOrgBaseinfoCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultString) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgBaseinfoCreateUsingPost,
    onSuccess(data: API.ResultString) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【推广机构】获取当前登录推广机构详情 POST /pc/orgBaseinfo/current */
export function usePcOrgBaseinfoCurrentUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSysOrgDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgBaseinfoCurrentUsingPost,
    onSuccess(data: API.ResultSysOrgDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】获取推广机构详情 POST /pc/orgBaseinfo/get */
export function usePcOrgBaseinfoGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSysOrgDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgBaseinfoGetUsingPost,
    onSuccess(data: API.ResultSysOrgDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】修改推广机构详情 POST /pc/orgBaseinfo/modify */
export function usePcOrgBaseinfoModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgBaseinfoModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询推广机构 POST /pc/orgBaseinfo/page */
export function usePcOrgBaseinfoPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultSysOrgDetialRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgBaseinfoPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSysOrgDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
