/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './teacherIncomeConfigManagement';
import * as API from './types';

/** 【运营】上架老师抽佣配置信息 POST /pc/teacher/incomeconfig/active */
export function usePcTeacherIncomeconfigActiveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherIncomeconfigActiveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】新增老师抽佣配置 POST /pc/teacher/incomeconfig/create */
export function usePcTeacherIncomeconfigCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherIncomeconfigCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】查询老师抽佣配置详情 POST /pc/teacher/incomeconfig/get */
export function usePcTeacherIncomeconfigGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherIncomeConfigBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherIncomeconfigGetUsingPost,
    onSuccess(data: API.ResultTeacherIncomeConfigBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询老师抽佣配置信息 POST /pc/teacher/incomeconfig/page */
export function usePcTeacherIncomeconfigPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultTeacherIncomeConfigBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherIncomeconfigPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultTeacherIncomeConfigBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
