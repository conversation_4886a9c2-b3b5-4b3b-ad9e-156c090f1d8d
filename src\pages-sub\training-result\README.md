# 学生训练结果页面

## 功能描述

这是一个展示学生上课情况和训练结果的页面，包含以下信息：

- **基本信息**：用户姓名、陪练姓名、训练名称
- **学习统计**：学新单词总数、学新正确数、学新错误数、正确率
- **时间信息**：开始时间、结束时间、学习时长
- **操作按钮**：去评价、去投诉

## 页面路径

```
/pages-sub/training-result/training-result
```

## 使用方法

### 1. 页面跳转

```javascript
import { navigateToSub } from '@/utils'

// 跳转到训练结果页面，需要传递课程计划ID
const goToTrainingResult = (attendClassPlanId) => {
  navigateToSub(`/training-result/training-result?attendClassPlanId=${attendClassPlanId}`)
}
```

### 2. 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| attendClassPlanId | number | 是 | 课程计划ID，用于获取训练统计数据 |

### 3. API接口

页面使用以下API获取数据：

- **获取训练统计**: `attendclassStatisticsUsingPost`
  - 接口路径: `/app/student/attendclass/statistics`
  - 请求参数: `{ id: attendClassPlanId }`
  - 返回数据: `AttendClassPlanStatisticsRespVO`

### 4. 数据结构

```typescript
interface AttendClassPlanStatisticsRespVO {
  id?: number                    // 上课计划id
  studentName?: string           // 学生姓名
  teacherName?: string           // 老师姓名
  coursewareName?: string        // 训练名称、课件资料名称
  newWordsCount?: number         // 学新单词总数
  newWordsCorrectCount?: number  // 学新单词正确数
  newWordsErrorCount?: number    // 学新单词错误数
  startTime?: string             // 开始时间
  endTime?: string               // 结束时间
}
```

## 功能特性

### 1. 数据展示
- 美观的卡片式布局
- 渐变色背景和统计卡片
- 实时计算正确率和学习时长
- 响应式设计，适配不同屏幕

### 2. 交互功能
- 加载状态显示
- 错误处理和提示
- 示例数据后备方案

### 3. 操作按钮
- **去评价**: 跳转到评价页面
- **去投诉**: 跳转到投诉页面

## 示例数据

当API调用失败时，页面会显示示例数据：

```javascript
{
  studentName: '张三',
  teacherName: '李老师',
  coursewareName: '新版初中考纲单词（升序版）（含音标）',
  newWordsCount: 25,
  newWordsCorrectCount: 25,
  newWordsErrorCount: 0,
  startTime: '2025-04-15 21:53:03',
  endTime: '2025-04-18 22:33:09',
}
```

## 样式特点

- 使用渐变色背景 (#667eea 到 #764ba2)
- 卡片式设计，圆角和阴影效果
- 统计数据使用不同颜色区分：
  - 总数：蓝紫色渐变
  - 正确：绿色渐变  
  - 错误：红色渐变
- 正确率进度条动画效果
- 响应式按钮设计

## 注意事项

1. 确保传递正确的 `attendClassPlanId` 参数
2. 页面会自动处理API调用失败的情况
3. 评价和投诉功能需要相应的页面支持
4. 时间格式为 'YYYY-MM-DD HH:mm:ss'
