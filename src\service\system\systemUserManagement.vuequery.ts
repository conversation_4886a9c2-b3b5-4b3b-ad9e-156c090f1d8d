/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemUserManagement';
import * as API from './types';

/** 【系统管理、机构】添加系统用户信息 POST /pc/system/user/create */
export function usePcSystemUserCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemUserCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、运营、财务、机构、老师、学生】获取当前登录用户信息 POST /pc/system/user/current */
export function usePcSystemUserCurrentUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSystemUserBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemUserCurrentUsingPost,
    onSuccess(data: API.ResultSystemUserBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、机构】获取指定用户信息 POST /pc/system/user/get */
export function usePcSystemUserGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultSystemUserBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemUserGetUsingPost,
    onSuccess(data: API.ResultSystemUserBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、机构】修改指定用户信息 POST /pc/system/user/modify */
export function usePcSystemUserModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemUserModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、机构】获取用户列表 POST /pc/system/user/page */
export function usePcSystemUserPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultSystemUserBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemUserPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSystemUserBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、机构】删除指定用户信息 POST /pc/system/user/remove */
export function usePcSystemUserRemoveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemUserRemoveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
