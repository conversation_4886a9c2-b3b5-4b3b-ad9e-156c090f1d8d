/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentEvaluateManagement';
import * as API from './types';

/** 【学生app】发起评价 POST /app/student/evaluate/create */
export function useEvaluateCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.evaluateCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生app】根据id查看评价详情 POST /app/student/evaluate/get */
export function useEvaluateGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherEvaluateBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.evaluateGetUsingPost,
    onSuccess(data: API.ResultTeacherEvaluateBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
