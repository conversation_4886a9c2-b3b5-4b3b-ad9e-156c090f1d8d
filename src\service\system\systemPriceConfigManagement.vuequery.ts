/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemPriceConfigManagement';
import * as API from './types';

/** 【运营】上架课程价格信息 POST /pc/system/priceConfig/active */
export function usePcSystemPriceConfigActiveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemPriceConfigActiveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营/学生】获取课程总价 POST /pc/system/priceConfig/calcTotalPrice */
export function usePcSystemPriceConfigCalcTotalPriceUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListInteger) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemPriceConfigCalcTotalPriceUsingPost,
    onSuccess(data: API.ResultListInteger) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】新增价格配置 POST /pc/system/priceConfig/create */
export function usePcSystemPriceConfigCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemPriceConfigCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营/学生】查询课时价格配置列表信息 POST /pc/system/priceConfig/list */
export function usePcSystemPriceConfigListUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListSysPriceConfigDO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemPriceConfigListUsingPost,
    onSuccess(data: API.ResultListSysPriceConfigDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询课时价格配置信息 POST /pc/system/priceConfig/page */
export function usePcSystemPriceConfigPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultSysPriceConfigDO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemPriceConfigPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSysPriceConfigDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
