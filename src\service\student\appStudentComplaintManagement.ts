/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生app】发起投诉 POST /app/student/complaint/create */
export async function complaintCreateUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/app/student/complaint/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生app】根据id查看投诉详情 POST /app/student/complaint/get */
export async function complaintGetUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherComplaintBaseRespVO>(
    '/app/student/complaint/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生app】分页查询投诉列表 POST /app/student/complaint/page */
export async function complaintPageUsingPost({
  body,
  options,
}: {
  body: API.TeacherComplaintPageByStuReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultTeacherComplaintBaseRespVO>(
    '/app/student/complaint/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
