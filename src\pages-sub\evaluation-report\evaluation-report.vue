<route lang="json5">
{
  style: {
    navigationBarTitleText: '测评报告',
    navigationBarBackgroundColor: '#4285f4',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <view class="min-h-screen bg-gradient-to-b from-blue-500 to-blue-600">
    <!-- 头部用户信息 -->
    <view class="px-48rpx pt-48rpx pb-32rpx">
      <view class="flex items-center justify-between text-white">
        <view>
          <view class="text-32rpx font-600 mb-8rpx">{{ userInfo.name }}，你的词汇量</view>
          <view class="text-24rpx opacity-80">{{ userInfo.gender }} · {{ userInfo.age }}岁 · {{ userInfo.grade }}</view>
        </view>
        <view class="flex flex-col items-center">
          <view class="w-120rpx h-120rpx rounded-full border-4 border-white flex items-center justify-center mb-16rpx">
            <text class="text-48rpx font-700 text-white">{{ vocabularyScore }}</text>
          </view>
          <view class="bg-yellow-500 px-24rpx py-8rpx rounded-full">
            <text class="text-20rpx font-600 text-white">{{ levelText }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="flex-1 px-48rpx space-y-32rpx">
      <!-- 语言水平分级表 -->
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="text-32rpx font-600 text-gray-800 mb-32rpx">语言水平分级表</view>
        
        <!-- 金字塔图表 -->
        <view class="pyramid-container">
          <view 
            v-for="(level, index) in pyramidLevels" 
            :key="index"
            class="pyramid-level"
            :class="level.class"
            :style="{ width: level.width }"
          >
            <view class="pyramid-label">{{ level.label }}</view>
            <view class="pyramid-value">{{ level.value }}</view>
          </view>
        </view>
      </view>

      <!-- 现实掌握词汇 -->
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="text-32rpx font-600 text-gray-800 mb-32rpx">现实掌握词汇</view>
        
        <view class="flex items-end justify-between h-200rpx">
          <!-- 高中英语水平 -->
          <view class="flex flex-col items-center">
            <view class="w-80rpx bg-green-500 rounded-t-8rpx mb-16rpx" :style="{ height: getBarHeight(realVocabulary.highSchool) }"></view>
            <view class="text-24rpx text-gray-600 text-center">高中英语水平</view>
            <view class="text-28rpx font-600 text-gray-800">{{ realVocabulary.highSchool }}</view>
          </view>
          
          <!-- 现实掌握词汇 -->
          <view class="flex flex-col items-center">
            <view class="w-80rpx bg-blue-500 rounded-t-8rpx mb-16rpx" :style="{ height: getBarHeight(realVocabulary.actual) }"></view>
            <view class="text-24rpx text-gray-600 text-center">现实掌握词汇</view>
            <view class="text-28rpx font-600 text-gray-800">{{ realVocabulary.actual }}</view>
          </view>
        </view>
      </view>

      <!-- 语言能力 -->
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="text-32rpx font-600 text-gray-800 mb-24rpx">语言能力</view>
        <view class="text-28rpx text-gray-700 leading-relaxed">
          {{ languageAbility }}
        </view>
      </view>

      <!-- 评测分析 -->
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="text-32rpx font-600 text-gray-800 mb-24rpx">评测分析</view>
        <view class="text-28rpx text-gray-700 leading-relaxed">
          {{ evaluationAnalysis }}
        </view>
      </view>

      <!-- 练习建议 -->
      <view class="bg-white rounded-24rpx p-32rpx mb-48rpx">
        <view class="text-32rpx font-600 text-gray-800 mb-24rpx">练习建议</view>
        <view class="text-28rpx text-gray-700 leading-relaxed">
          {{ practiceAdvice }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 用户信息
const userInfo = ref({
  name: '李木子',
  gender: '男',
  age: 10,
  grade: '五年级'
})

// 词汇量分数
const vocabularyScore = ref(500)

// 等级文本
const levelText = computed(() => {
  if (vocabularyScore.value >= 3000) return '优秀初级大学'
  if (vocabularyScore.value >= 1500) return '良好初级大学'
  if (vocabularyScore.value >= 500) return '儿童初级大学'
  return '基础水平'
})

// 金字塔数据
const pyramidLevels = ref([
  { label: '难词', value: 3885, width: '200rpx', class: 'pyramid-hard' },
  { label: '高级', value: 1760, width: '280rpx', class: 'pyramid-advanced' },
  { label: '中级', value: 650, width: '360rpx', class: 'pyramid-intermediate' },
  { label: '初级', value: 240, width: '440rpx', class: 'pyramid-basic' }
])

// 现实掌握词汇数据
const realVocabulary = ref({
  highSchool: 2345,
  actual: 500
})

// 计算柱状图高度
const getBarHeight = (value: number) => {
  const maxValue = Math.max(realVocabulary.value.highSchool, realVocabulary.value.actual)
  const ratio = value / maxValue
  return `${ratio * 160}rpx`
}

// 语言能力描述
const languageAbility = ref(
  '经过智能检测，你的词汇量相当于小学一、二年级水平，目前只能识别生活中少数简单的名词，英语学习处于刚刚起步阶段，还无法用英文进行交流/阅读/写作。'
)

// 评测分析
const evaluationAnalysis = ref(
  '你能跟随老师通过做游戏等活动的学习单词和发音，但要注意基础词汇的积累，需要开始不断学习和提高新的词汇，进一步提高和巩固英语学习能力。'
)

// 练习建议
const practiceAdvice = ref(
  '开始跟读小学英语词汇小学教材词汇，进一步巩固和提高词汇量。在掌握词汇后，可以开始小学阶段中级/高级英文读物，提升英语阅读的兴趣和习惯。'
)
</script>

<style lang="scss" scoped>
// 金字塔容器
.pyramid-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

// 金字塔层级
.pyramid-level {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  border-radius: 8rpx;
  color: white;
  font-weight: 600;
  
  &.pyramid-hard {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  }
  
  &.pyramid-advanced {
    background: linear-gradient(135deg, #ffa726, #ff9800);
  }
  
  &.pyramid-intermediate {
    background: linear-gradient(135deg, #66bb6a, #4caf50);
  }
  
  &.pyramid-basic {
    background: linear-gradient(135deg, #42a5f5, #2196f3);
  }
}

.pyramid-label {
  font-size: 28rpx;
}

.pyramid-value {
  font-size: 28rpx;
}

// 渐变背景
.bg-gradient-to-b {
  background: linear-gradient(to bottom, #4285f4, #3367d6);
}

// 文本行高
.leading-relaxed {
  line-height: 1.6;
}
</style>
