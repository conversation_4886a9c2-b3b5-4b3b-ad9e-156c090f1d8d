<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '练习',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="min-h-screen bg-#f5f5f5">
    <!-- 组卷信息卡片 -->
    <view class="mx-32rpx pt-32rpx mb-32rpx">
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="flex items-center mb-24rpx">
          <!-- 头像占位符 -->
          <view class="w-96rpx h-96rpx bg-#f0f0f0 rounded-full mr-24rpx overflow-hidden">
            <image
              src="/static/images/avatar_placeholder.png"
              class="w-full h-full"
              mode="aspectFill"
            />
          </view>

          <!-- 组卷中心信息 -->
          <view class="flex-1">
            <view class="text-32rpx font-600 text-#333 mb-8rpx">组卷中心</view>
            <view class="text-24rpx text-#666">智能组卷 • 自测练习</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 组卷表单 -->
    <view class="mx-32rpx mb-32rpx">
      <view class="bg-white rounded-24rpx p-32rpx">
        <wd-form ref="formRef" :model="form">
          <!-- 试卷名称 -->
          <view class="form-item">
            <view class="form-label">试卷名称</view>
            <wd-input
              class="practiseName"
              v-model="form.name"
              placeholder="请输入试卷名称"
              custom-class="form-picker"
            />
          </view>

          <!-- 教材版本 -->
          <view class="form-item">
            <view class="form-label">教材版本</view>
            <wd-picker
              v-model="form.version"
              :columns="dictStore.getDictByType('question_bank_version')"
              placeholder="请选择版本"
              custom-class="form-picker"
            />
          </view>

          <!-- 年级选择 -->
          <view class="form-item">
            <view class="form-label">年级</view>
            <wd-picker
              v-model="form.grade"
              :columns="dictStore.getDictByType('question_bank_grade')"
              placeholder="请选择年级"
              custom-class="form-picker"
            />
          </view>

          <!-- 单元选择 -->
          <view class="form-item">
            <view class="form-label">单元</view>
            <wd-picker
              v-model="form.unit"
              :columns="dictStore.getDictByType('question_bank_unit')"
              placeholder="请选择单元"
              custom-class="form-picker"
            />
          </view>

          <!-- 试题数量 -->
          <view class="form-item">
            <view class="form-label">试题数量</view>
            <wd-input-number
              v-model="form.num"
              :min="5"
              :max="100"
              :step="10"
              custom-class="form-picker"
            />
          </view>
        </wd-form>
      </view>
    </view>

    <!-- 开始答题按钮 -->
    <view class="flex justify-between gap-1 mx-32rpx mb-32rpx">
      <wd-button
        v-if="isGetAIpractise === true"
        custom-class="w-full! h-96rpx! rounded-24rpx! text-32rpx! font-600! bg-#3d5af5! border-#3d5af5!"
        type="primary"
        @click="startAnswer"
      >
        {{ '返回首页' }}
      </wd-button>
      <wd-loading v-if="isGetAIpractise === false" />
      <wd-button
        custom-class="w-full! h-96rpx! rounded-24rpx! text-32rpx! font-600! bg-#3d5af5! border-#3d5af5!"
        type="primary"
        @click="startAnswer"
      >
        {{ selectedRole === 'teacher' ? '布置课后练习' : '开始答题' }}
      </wd-button>
    </view>

    <!-- 底部导航栏 -->
    <sxt-tab-bar tab-index="study" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { RoleEmu, useDictStore, useRoleStore } from '@/store'
import { navigateToSub, showToast } from '@/utils'
import { useRouter } from 'vue-router'
import * as student from '@/service/student'
import { classpractisGenerateRandomPracticeAfterClassUsingPost } from '@/service/teacher'
import { createUsingPost, queryBySessionIdUsingPost } from '@/service/ai'
import { useToast } from 'wot-design-uni'
const { getRole } = useRoleStore()
const form = ref({
  name: '',
  version: '',
  grade: '',
  unit: '',
  num: 10,
  attendClassPlanId: 0,
})
const PractiseSessionId = ref('')
const toast = useToast()
// 引入Wot UI组件
const dictStore = useDictStore()
// 静态题目数据
const practiseId = ref(0)
// 课程id，用于老师生成课后练习
const classId = ref(0)
// 响应式状态
const router = useRouter()
const isGetAIpractise = ref(undefined)
// 检查是否可以开始答题（所有条件都已选择）
// const canStart = computed(() => {
//   return selectedVersion.value && selectedGrade.value && selectedUnit.value
// })

const selectedRole = ref(getRole())
// 开始答题按钮点击事件
const startAnswer = async () => {
  // 请求生成试卷，获取试卷Id
  if (classId.value) {
    form.value.attendClassPlanId = classId.value
  }
  if (selectedRole.value === RoleEmu.Teacher) {
    const res = await classpractisGenerateRandomPracticeAfterClassUsingPost({
      body: form.value,
    })
    if (res.code === 200) {
      // 还需要调用生成AI测试的接口
      if (res.data.items.length === 0) {
        showToast('生成失败')
        return
      } else {
        const wordsStr = res.data.items.join(',')
        getAIPractiseBank(wordsStr)
      }
    }
  } else {
    const res = await student.classpractisGenerateRandomPracticeUsingPost({
      body: form.value,
    })
    if (res.code === 200) {
      showToast('生成成功')
      practiseId.value = res.data.item
      navigateToSub(`/start-answer/start-answer?practiseId=${practiseId.value}`)
    }
  }
  console.log('from.value', form.value)
}
// 用户角色
const userRole = ref<string | null>(null)
// 获取AI试卷
const getAIPractiseBank = async (wordsStr) => {
  try {
    console.log(" uni.getStorageSync('userInfo')", uni.getStorageSync('userInfo'))
    const res = await createUsingPost({
      body: {
        studentId: uni.getStorageSync('studentId'),
        teacherId: uni.getStorageSync('userInfo')?.userId,
        words: wordsStr,
        attendId: Number(classId.value),
      },
    })
    if (res.code === 200) {
      const sessionId = res?.data?.item?.sessionId
      toast.success('试卷正在生成中，请稍后...')
      console.log('sessionId', sessionId)
      isGetAIpractise.value = false
      let interval = null
      // 由于时间关系，15秒后再开始请求，减少请求次数，15秒钟之后每隔2秒钟执行一次，根据状态
      setTimeout(() => {
        interval = setInterval(() => {
          console.log('dafdasa')
          if (isGetAIpractise.value) {
            console.log('isGetAIpractise.value', isGetAIpractise.value)
            clearInterval(interval)
            PractiseSessionId.value = sessionId
            return
          }
          fetchPractiseDetailBySessionId(sessionId)
        }, 2000)
      }, 20000)
      // 做安全处理，如果一分钟都还没请求到数据，则清除定时器
      setTimeout(() => {
        console.log('一分钟都没请求到数据')
        clearInterval(interval)
        isGetAIpractise.value = undefined
      }, 60000)
    }
  } catch (err) {
    console.log('err', err)
  }
}
// 根据会话Id请求试卷内容
const fetchPractiseDetailBySessionId = async (sessionId) => {
  try {
    const res = await queryBySessionIdUsingPost({ body: { sessionId } })
    if (res.data.item.examId !== null) {
      isGetAIpractise.value = true
    }
  } catch (err) {
    console.log('获取试卷详情失败', err)
  }
}
// 获取用户角色
const getUserRole = () => {
  try {
    userRole.value = getRole()
  } catch (e) {
    console.error('获取用户角色失败:', e)
    userRole.value = null
  }
}
onLoad((options) => {
  console.log('options', options)
  if (options.classId) {
    classId.value = options.classId
  }
})
// 页面加载时检查登录状态
onMounted(() => {
  const token = uni.getStorageSync('token')
  getUserRole()
  if (!token) {
    uni.showToast({ title: '请先登录', icon: 'none' })
    setTimeout(() => {
      router.push('/pages-sub/login/login')
    }, 1500)
  }
})
</script>

<style scoped lang="scss">
// 表单项样式 - 与约课页面保持一致
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

// 表单组件自定义样式 - 与约课页面保持一致
::v-deep .form-picker {
  flex: 1;
  text-align: right;

  .wd-input,
  .wd-select-picker,
  .wd-picker,
  .wd-input-number {
    border: none !important;
    border-bottom: none !important;
  }

  .wd-input__inner,
  .wd-select-picker__inner,
  .wd-picker__inner,
  .wd-input-number__input {
    text-align: right;
    border: none !important;
    border-bottom: none !important;
    background: transparent;
    font-size: 28rpx;
    color: #666;
  }

  .wd-input__placeholder,
  .wd-select-picker__placeholder,
  .wd-picker__placeholder {
    color: #999;
    font-size: 28rpx;
  }
  // :deep(.practiseName .uni-input-input) {
  //   border-bottom-width: 0rpx;
  // }
}
</style>
