<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>
<template>
  <sxt-container :isTab="true" showHeaderBg>
    <template #nav-left>
      <image class="w-[127px] h-[30px]" src="/static/images/logo.png" />
    </template>
    <template #nav-right>
      <view class="mr-2 flex items-center">
        <wd-badge modelValue="12" :top="10" custom-class="h-7 mb-4">
          <image class="w-6 h-6" src="/static/images/ic_top_message.png" />
        </wd-badge>
      </view>
    </template>
    <!-- 根据角色显示不同的内容 -->
    <student-page v-if="userRole === 'student'"></student-page>
    <teacher-page v-else-if="userRole === 'teacher'"></teacher-page>
    <view
      v-else
      class="bg-[#F5F5F5] px-[32rpx] pb-[32rpx] flex justify-center items-center h-[100vh]"
    >
      <view class="text-center">
        <wd-icon name="warn" size="80rpx" color="#FF6B6B"></wd-icon>
        <view class="text-[28rpx] mt-[16rpx]">未检测到角色信息，请先登录</view>
        <wd-button class="mt-[32rpx]" type="primary" @click="goToLogin">去登录</wd-button>
      </view>
    </view>
    <sxt-tab-bar tab-index="index" />
  </sxt-container>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { checkAuth } from '@/utils/check-auth'
import { navigateTo, navigateToSub } from '@/utils'
import StudentPage from './components/student-page.vue'
import TeacherPage from './components/teacher-page.vue'
import { useDictStore, useRoleStore } from '@/store'
defineOptions({
  name: 'Home',
})
const dictStore = useDictStore()
const { getRole } = useRoleStore()
// 用户角色
const userRole = ref<string | null>(null)

// 获取用户角色
const getUserRole = () => {
  try {
    userRole.value = getRole()
  } catch (e) {
    userRole.value = null
  }
}

// 跳转到登录页面
const goToLogin = () => {
  navigateToSub('/login/login')
}

onMounted(() => {
  console.log(`"此处运行了"`)
  getUserRole()
  dictStore.fetchDictData()
})
onShow(() => {
  getUserRole()
})
onLoad(() => {
  if (!checkAuth()) {
    // 未登录，跳转到登录页面
    goToLogin()
  } else {
    // 已登录，获取用户角色
    getUserRole()
  }
})
</script>

<style>
.main-title-color {
  color: #d14328;
}
</style>
