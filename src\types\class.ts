/**
 * 时间对象类型
 */
export interface TimeObject {
  hour?: number
  minute?: number
  second?: number
  nano?: number
}

/**
 * 课程进度类型
 */
export interface ClassProgress {
  totalWords?: number
  learnedWords?: number
  correctWords?: number
  percentage?: number
  [key: string]: any
}

/**
 * 课程类型
 */
export interface ClassInfo {
  id?: number
  courseDuration?: number
  studentName?: string
  startTime?: TimeObject
  endTime?: TimeObject
  tencentMeetingNo?: string
  coursewareId?: number
  latestWordId?: number
  progress?: ClassProgress
  [key: string]: any
}
