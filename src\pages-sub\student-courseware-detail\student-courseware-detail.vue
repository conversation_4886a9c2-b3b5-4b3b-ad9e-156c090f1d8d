<route lang="json5">
{
  layout: 'common',
  style: {
    navigationBarTitleText: '课程资料详情',
  },
}
</route>

<template>
  <view class="bg-[#F5F5F5]">
    <!-- 课程资料基本信息 -->
    <view class="bg-white p-[32rpx] pt-0 mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">课程资料信息</view>
      <view v-if="coursewareInfo">
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">资料ID</view>
          <view class="text-[28rpx] font-medium">{{ coursewareInfo.id }}</view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">资料名称</view>
          <view class="text-[28rpx] font-medium">{{ coursewareInfo.name || '未命名资料' }}</view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">创建时间</view>
          <view class="text-[28rpx] font-medium">{{ coursewareInfo.createTime || '--' }}</view>
        </view>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">
        正在加载课程资料信息...
      </view>
    </view>

    <!-- 单词列表 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">单词列表</view>
      <view v-if="wordList && wordList.length > 0">
        <view
          v-for="(word, index) in wordList"
          :key="index"
          class="p-[16rpx] border-b border-[#eee] last:border-b-0"
        >
          <view class="flex justify-between items-center mb-[8rpx]">
            <view class="flex items-center">
              <view class="text-[32rpx] font-medium">{{ word.word }}</view>
              <wd-icon
                name="sound"
                size="36rpx"
                color="#8000FF"
                @click="playWordSound(word)"
                class="ml-[8rpx] cursor-pointer mt-1"
              ></wd-icon>
            </view>
          </view>
          <view class="text-[28rpx] text-[#666]">{{ word.chinese }}</view>
        </view>
      </view>
      <view v-else-if="loading" class="text-center py-[40rpx] text-[28rpx] text-[#999]">
        正在加载单词列表...
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无单词数据</view>
    </view>

    <!-- 分页控制 -->
    <view
      v-if="wordList && wordList.length > 0"
      class="bg-white p-[32rpx] mb-[20rpx] flex justify-between"
    >
      <wd-button
        size="small"
        type="primary"
        plain
        :disabled="currentPage <= 1"
        @click="handlePrevPage"
      >
        上一页
      </wd-button>
      <view class="text-[28rpx] flex items-center">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
      </view>
      <wd-button
        size="small"
        type="primary"
        plain
        :disabled="currentPage >= totalPages"
        @click="handleNextPage"
      >
        下一页
      </wd-button>
    </view>
    <!-- 课堂操作 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[24rpx]">课堂操作</view>
      <view class="flex justify-center">
        <wd-button type="error" custom-class="w-[300rpx]!" @click="handleEndClass">
          结束上课
        </wd-button>
      </view>
    </view>
    <!-- 学习提示 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">学习提示</view>
      <view class="text-[28rpx] text-[#666] leading-[1.6]">
        <view class="mb-[8rpx]">• 点击单词旁边的声音图标可以听发音</view>
        <view class="mb-[8rpx]">• 认真学习每个单词的发音和含义</view>
        <view class="mb-[8rpx]">• 如有疑问请及时向老师提问</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'
import { navigateToSub, showToast } from '@/utils'
import {
  attendclassEndClassPlanUsingPost,
  attendclassGetCoursewareDetialUsingPost,
  attendclassWordPageUsingPost,
} from '@/service/student/appStudentAttendClassPlanManagement'

defineOptions({
  name: 'StudentCoursewareDetail',
})

// 页面参数
const classPlanId = ref<number | null>(null)
const coursewareInfo = ref<any>(null)
const wordList = ref<any[]>([])
const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = ref(1)
const classId = ref()

// 获取单词列表
const fetchWordList = async () => {
  if (!classPlanId.value) {
    showToast('无法获取课程资料ID')
    return
  }

  loading.value = true

  // 获取课程资料详情
  const resCourseware = await attendclassGetCoursewareDetialUsingPost({
    body: {
      coursewareId: classPlanId.value,
    },
  })

  coursewareInfo.value = {
    ...resCourseware.data.item,
    createTime: dayjs(resCourseware.data.item.createTime).format('YYYY-MM-DD HH:mm:ss'),
  }

  // 获取单词列表
  const res = await attendclassWordPageUsingPost({
    body: {
      classPlanId: classId.value,
      wordQueryType: '1',
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    },
  })

  if (res.code === 200) {
    if (res.data) {
      wordList.value = res.data.items
      // 计算总页数
      if (res.data.counts) {
        totalPages.value = Math.ceil(res.data.counts / pageSize.value)
      }
    }
  } else {
    showToast(res.message)
  }
  loading.value = false
}
// 结束上课
const handleEndClass = async () => {
  if (!classId.value) {
    showToast('无法获取课程ID')
    return
  }
  const res = await attendclassEndClassPlanUsingPost({
    body: {
      id: classId.value,
    },
  })

  if (res.code === 200) {
    showToast('下课成功')
    navigateToSub(`/training-result/training-result?attendClassPlanId=${classId.value}`)
  } else {
    showToast(res.message)
  }
}
// 上一页
const handlePrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchWordList()
  }
}

// 下一页
const handleNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchWordList()
  }
}

// 播放单词声音
const playWordSound = (item: { mp3: any; word: any }) => {
  const { mp3, word } = item
  if (!mp3) {
    showToast('该单词暂无发音')
    return
  }
  try {
    const audio = new Audio(mp3)
    audio.play()
  } catch (error) {
    console.error('播放音频失败:', error)
    showToast('播放音频失败')
  }
}

// 页面加载
onLoad((options: any) => {
  if (options.coursewareId) {
    classPlanId.value = Number(options.coursewareId)
  }
  if (options.classId) {
    classId.value = Number(options.classId)
  }
  fetchWordList()
})
</script>
