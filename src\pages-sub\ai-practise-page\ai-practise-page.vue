<route lang="json5">
{
  style: {
    navigationBarTitleText: '答题',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>
<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- 题目内容区域 -->
    <view class="flex-1 flex flex-col items-center justify-center px-48rpx">
      <!-- 题目标题 -->
      <view class="question text-45rpx font-600 text-#333 text-left mb-32rpx">
        {{ currentQuestion.questionText }}
      </view>

      <!-- 提示文字 -->
      <view class="text-24rpx text-#999 text-center mb-80rpx">备注：错误状态和正确状态</view>

      <!-- 选项区域 -->
      <view class="w-full space-y-24rpx">
        <view v-for="(item, index) in currentQuestion.options" :key="index">
          <!-- 休息选项 -->
          <view
            @click="selectOption(item.optionId)"
            :class="{ isCorrect: acitveOptionId === item.optionId }"
            class="option-item rest-option"
          >
            <text class="option-text">{{ item.optionContent || '--' }}</text>
          </view>

          <!-- 呼吸选项 -->
          <!-- <view
            class="option-item breath-option"
            :class="{
              isCorrect: isCorrectAnswer(2),
              isInCorrect: isInCorrectAnswer(2),
            }"
            @click="selectOption(2)"
          >
            <text class="option-text">{{ currentQuestion.option2 || '--' }}</text>
          </view> -->

          <!-- 胸部选项 -->
          <!-- <view
            class="option-item chest-option"
            :class="{
              isCorrect: isCorrectAnswer(3),
              isInCorrect: isInCorrectAnswer(3),
            }"
            @click="selectOption(3)"
          >
            <text class="option-text">{{ currentQuestion.option3 || '--' }}</text>
          </view> -->

          <!-- 早晨选项 -->
          <!-- <view
            class="option-item morning-option"
            :class="{
              isCorrect: isCorrectAnswer(4),
              isInCorrect: isInCorrectAnswer(4),
            }"
            @click="selectOption(4)"
          >
            <text class="option-text">{{ currentQuestion.option4 || '--' }}</text>
          </view> -->

          <!-- 品种选项 -->
          <!-- <view
            class="option-item variety-option"
            :class="{
              isCorrect: isCorrectAnswer(5),
              isInCorrect: isInCorrectAnswer(5),
            }"
            @click="selectOption(5)"
          >
            <text class="option-text">{{ currentQuestion.option5 || '--' }}</text>
          </view> -->

          <!-- 不认识选项 -->
        </view>
        <view
          @click="selectOption(-1)"
          :class="{ isCorrect: acitveOptionId === -1 }"
          class="option-item unknown-option"
        >
          <text class="option-text">不认识</text>
        </view>
      </view>
    </view>

    <!-- 底部统计区域 -->
    <view class="bg-white px-48rpx py-32rpx">
      <!-- 提示文字 -->
      <view class="text-24rpx text-#999 text-center mb-32rpx">停留页面超过10s，建议选择不认识</view>

      <!-- 进度和统计 -->
      <view class="flex items-center justify-between mb-32rpx">
        <view class="text-28rpx text-#333">
          {{ currentQuestionNum + 1 }}/{{ practiseDetail.length }}
        </view>

        <view class="flex items-center space-x-32rpx">
          <!-- 正确数量 -->
          <view class="flex items-center">
            <view class="w-32rpx h-32rpx bg-#4caf50 rounded-full mr-16rpx"></view>
            <text class="text-28rpx text-#333">{{ correctCount }}</text>
          </view>

          <!-- 错误数量 -->
          <view class="flex items-center">
            <view class="w-32rpx h-32rpx bg-#f44336 rounded-full mr-16rpx"></view>
            <text class="text-28rpx text-#333">{{ errorCount }}</text>
          </view>
        </view>
      </view>

      <!-- 导航按钮 -->
      <!-- <view class="flex gap-24rpx">
        <wd-button
          custom-class="nav-btn prev-btn"
          :disabled="currentQuestionNum === 0"
          @click="prevQuestion"
        >
          上一题
        </wd-button>

        <wd-button
          v-if="currentQuestionNum === practiseDetail.length - 1"
          custom-class="nav-btn submit-btn"
          type="primary"
          @click="handleSubmit"
        >
          提交试卷
        </wd-button>
        <wd-button v-else custom-class="nav-btn next-btn" type="primary" @click="nextQuestion">
          下一题
        </wd-button>
      </view> -->
    </view>

    <wd-message-box selector="checkResult">
      <view class="success-dialog-content">
        <!-- 成功图标 -->
        <view class="success-icon">
          <image
            class="w-120rpx h-120rpx"
            src="../../assets/images/book-class-success.png"
            mode="scaleToFill"
          />
        </view>

        <!-- 成功文字 -->
        <view class="success-title">您已完成测试</view>
        <view class="success-subtitle">
          {{ `一共${practiseDetail.length}道测试题，答对${correctCount}道测试题,加油哦` }}
        </view>
      </view>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
import {
  classpractisAnswerQuestionsUsingPost,
  classpractisPractisDetailPageUsingPost,
  StudentPractiseDetailBaseRespVO,
} from '@/service/student'
import { useMessage } from 'wot-design-uni'
import { ref, onMounted, computed } from 'vue'

import { navigateBack, navigateTo, navigateToSub } from '@/utils'
import { answerUsingPost, OptionVo, queryBySessionIdUsingPost, QuestionVo } from '@/service/ai'

// 防抖函数
const debounce = (func, delay) => {
  let timeoutId = null
  return (...args: any[]) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      func.apply(null, args)
    }, delay)
  }
}
const examId = ref(0)
const practiseId = ref(0)
const message = useMessage('checkResult')
const answerFrom = ref({
  // 试卷题目Id
  questionId: 0,
  // 学生答案的选项Id
  studentAnswer: 1,
  // 是否为正确答案
  isCorrectAnswered: 0,
})
const currentQuestionNum = ref(0)
const currentQuestion = ref<any>({})
const fetchPractiseFrom = ref({})
const practiseDetail = ref([])
const sessionId = ref('89366b624d1e41a399139f6111895027')
// 选项点击的实际处理函数
// const handleSelectOption = (value: number) => {
//   // currentQuestion.value.studentAnswer = value
//   // 完成对试题答案的提交

// }
const acitveOptionId = ref(0)
const handleSelectOption = (optionId) => {
  console.log('optionsId', optionId)
  acitveOptionId.value = optionId
  currentQuestion.value.studentAnswer = optionId
  setTimeout(() => {
    submitAnswer()
  }, 500)
}
// 带防抖的选项点击方法（300ms防抖延迟）
const selectOption = debounce(handleSelectOption, 300)

// 计算正确和错误数量
const correctCount = computed(() => {
  return practiseDetail.value.filter((item) => item.isCorrectAnswered === 1).length
})
const errorCount = computed(() => {
  console.log('practiseDetail.value', practiseDetail.value)
  return practiseDetail.value.filter((item) => item.isCorrectAnswered === 0).length
})
// 获取试卷详情
// const fetchPractiseDetail = async () => {
//   try {
//     fetchPractiseFrom.value = {
//       pageSize: 9999,
//       pageNo: 1,
//       practiseId: practiseId.value,
//     }
//     const res = await classpractisPractisDetailPageUsingPost({ body: fetchPractiseFrom.value })
//     if (res.code === 200) {
//       console.log('data', res.data)
//       practiseDetail.value = res.data.items
//       if (practiseDetail.value.length > 0) {
//         // 设置当前题目
//         currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
//       }
//     }
//   } catch (err) {
//     console.log('获取试卷err', err)
//   }
// }
// 上一题实现逻辑
// const prevQuestion = () => {
//   currentQuestionNum.value--
//   currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
// }
// 下一题逻辑实现

// 判断是否是正确的答案的逻辑
const isCorrectAnswer = () => {
  if (currentQuestion.value.studentAnswer === -1) {
    return -1
  } else {
    return currentQuestion.value.studentAnswer ===
      currentQuestion.value.options.find((item) => item.isCorrect === true).optionId
      ? 1
      : 0
  }
}
const submitAnswer = async () => {
  // 填充提交答案的相关数据
  // 最后一道题

  const searchForm = {
    questionId: currentQuestion.value.questionId,
    studentAnswer: currentQuestion.value.studentAnswer,
    isCorrectAnswered: isCorrectAnswer(),
  }
  answerFrom.value = searchForm

  // 统一执行提交逻辑
  const res = await answerUsingPost({ body: answerFrom.value })
  if (res.code === 200) {
    // 将当前成绩的答案更新到维护的试卷中
    practiseDetail.value[currentQuestionNum.value].studentAnswer = searchForm.studentAnswer
    practiseDetail.value[currentQuestionNum.value].isCorrectAnswered = searchForm.isCorrectAnswered
  }
  // 非最后一题,需要跳转下一题
  console.log('这里执行了1', currentQuestionNum.value)
  if (currentQuestionNum.value < practiseDetail.value.length - 1) {
    console.log('这里执行了2')
    currentQuestionNum.value++
    currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
  } else {
    console.log('这里执行了3')
    message
      .confirm({
        // msg: '请及时前往上课',
        title: '',
        confirmButtonText: '返回上课',
        cancelButtonText: '返回首页',
      })
      .then(() => {
        // 回到第一题
        navigateBack()
      })
      .catch(() => {
        navigateTo('/index/index')
      })
  }
}
// 最后一题提交逻辑
// const handleSubmit = async () => {
//   // 还需要对最后一题的答案进行提交
//   if (currentQuestionNum.value === practiseDetail.value.length - 1) {
//     // answerFrom.value.id = currentQuestion.value.id
//     // answerFrom.value.studentAnswer = currentQuestion.value.studentAnswer
//     const res = await classpractisAnswerQuestionsUsingPost({ body: answerFrom.value })
//     // 将当前试题的答案更新到维护的试卷中，方便后续对题目的查阅
//     if (res.code === 200) {
//       // 将当前成绩的答案更新到维护的试卷中
//       practiseDetail.value[currentQuestionNum.value].studentAnswer = 1
//       // currentQuestion.value.studentAnswer
//     }
//   }
//   message
//     .confirm({
//       // msg: '请及时前往上课',
//       title: '',
//       confirmButtonText: '重新测试',
//       cancelButtonText: '返回首页',
//     })
//     .then(() => {
//       // 回到第一题
//       navigateTo('/self-study/self-study')
//     })
//     .catch(() => {
//       navigateTo('/index/index')
//     })
// }
// 根据sessionId查询试卷详情
const fetchPractiseDetailBySessionId = async () => {
  try {
    const res = await queryBySessionIdUsingPost({ body: { sessionId: sessionId.value } })
    if (res.code === 200) {
      console.log('data', res.data)
      examId.value = res.data.item.examId
      practiseDetail.value = res?.data?.item?.questions.map((item) => {
        return {
          ...item,
          studentAnswer: 0,
          isCorrectAnswered: undefined,
        }
      })
      if (practiseDetail.value.length > 0) {
        // 设置当前题目
        currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
      }
    }
  } catch (err) {
    console.log('获取试卷详情失败', err)
  }
}
onLoad((options: any) => {
  if (options.type === 'classPractise') {
    sessionId.value = options.sessionId
  }
})
onMounted(() => {
  fetchPractiseDetailBySessionId()
})
// 返回判断正确答案的结果
// const isCorrectAnswer = (studentAnswer: number) => {
//   return (
//     currentQuestion.value.studentAnswer === studentAnswer &&
//     currentQuestion.value.answer === studentAnswer
//   )
// }
// const isInCorrectAnswer = (studentAnswer: number) => {
//   return (
//     currentQuestion.value.studentAnswer === studentAnswer &&
//     currentQuestion.value.answer !== studentAnswer
//   )
// }
// onMounted(() => fetchPractiseDetail())
</script>

<style lang="scss" scoped>
.question {
  text-align: left;
  hyphens: auto; /* 自动在单词断句处添加连字符 */
  overflow-wrap: break-word; /* 允许长单词在容器边界处换行 */
  word-break: break-word; /* 强制长单词换行（增强兼容性） */
}
:deep(.option-group) {
  background-color: rgb(245, 245, 245);
}
.isCorrect {
  background-color: #f6f6f6;
}
// 选项样式
.option-item {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  .option-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
  //  正确的颜色
  &.isCorrect {
    background: #e8f5e8;
    border: 2rpx solid #c8e6c9;
    transform: scale(0.98);
  }
  &.isInCorrect {
    background: #fce4ec;
    border: 2rpx solid #f8bbd9;
  }
  &.currentAnswer {
    background: #e8f5e8;
    border: 2rpx solid #c8e6c9;
  }
  &.unknownOptions {
    background: #9e9e9e;
    border-color: #757575;
  }
}

// 导航按钮样式
.nav-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.prev-btn {
  background: #f5f5f5;
  color: #666;
  border: none;

  &:disabled {
    background: #f0f0f0;
    color: #ccc;
  }
}

.next-btn,
.submit-btn {
  background: #3d5af5;
  border: none;
}
.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}
</style>
