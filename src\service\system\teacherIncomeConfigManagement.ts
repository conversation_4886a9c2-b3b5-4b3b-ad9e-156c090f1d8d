/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】上架老师抽佣配置信息 POST /pc/teacher/incomeconfig/active */
export async function pcTeacherIncomeconfigActiveUsingPost({
  body,
  options,
}: {
  body: API.TeacherIncomeConfigGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/teacher/incomeconfig/active', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】新增老师抽佣配置 POST /pc/teacher/incomeconfig/create */
export async function pcTeacherIncomeconfigCreateUsingPost({
  body,
  options,
}: {
  body: API.TeacherIncomeConfigCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/teacher/incomeconfig/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】查询老师抽佣配置详情 POST /pc/teacher/incomeconfig/get */
export async function pcTeacherIncomeconfigGetUsingPost({
  body,
  options,
}: {
  body: API.TeacherIncomeConfigGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherIncomeConfigBaseRespVO>(
    '/pc/teacher/incomeconfig/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询老师抽佣配置信息 POST /pc/teacher/incomeconfig/page */
export async function pcTeacherIncomeconfigPageUsingPost({
  body,
  options,
}: {
  body: API.TeacherIncomeConfigPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultTeacherIncomeConfigBaseRespVO>(
    '/pc/teacher/incomeconfig/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
