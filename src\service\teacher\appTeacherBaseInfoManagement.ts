/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【老师】获取当前登录的老师信息详情 返回值: default response POST /app/teacher/baseinfo/current */
export async function baseinfoCurrentUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.TeacherUserDO>('/app/teacher/baseinfo/current', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 老师使用账号密码登录 返回值: default response POST /app/teacher/baseinfo/login */
export async function baseinfoLoginUsingPost({
  body,
  options,
}: {
  body: API.UserLoginReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.UserLoginVo>('/app/teacher/baseinfo/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【老师】修改老师信息 POST /app/teacher/baseinfo/modify */
export async function baseinfoModifyUsingPost({
  body,
  options,
}: {
  body: API.AppUserModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/baseinfo/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 注册老师信息 POST /app/teacher/baseinfo/register */
export async function baseinfoRegisterUsingPost({
  body,
  options,
}: {
  body: API.UserRegisterReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherAndStudentRespVO>(
    '/app/teacher/baseinfo/register',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
