import { useDictStore } from '@/store'

/**
 * 获取字典文本
 * @param type 字典类型
 * @param value 字典值
 * @returns 字典文本
 */
export const useDict = (type: string, value: string | number): string => {
  const dictStore = useDictStore()
  return dictStore.getDictText(type, value)
}

/**
 * 获取字典项
 * @param type 字典类型
 * @param value 字典值
 * @returns 字典项
 */
export const useDictItem = (
  type: string,
  value: string | number,
): { text: string; [key: string]: any } => {
  const dictStore = useDictStore()
  return dictStore.getDictItem(type, value)
}

/**
 * 获取字典类型下的所有字典项
 * @param type 字典类型
 * @returns 字典类型下的所有字典项
 */
export const useDictByType = (
  type: string,
): { [key: string]: { text: string; [key: string]: any } } => {
  const dictStore = useDictStore()
  return dictStore.getDictByType(type)
}

/**
 * 获取字典类型下的所有字典项，转换为选项数组
 * @param type 字典类型
 * @returns 选项数组 [{ value: '1', label: '文本1' }, { value: '2', label: '文本2' }]
 */
export const useDictOptions = (type: string): Array<{ value: string; label: string }> => {
  const dictStore = useDictStore()
  const dict = dictStore.getDictByType(type)
  return Object.keys(dict).map((key) => ({
    value: key,
    label: dict[key].text,
  }))
}
