<template>
  <view class="">
    <!-- 用户信息卡片 -->
    <view class="mx-32rpx mt-24rpx mb-32rpx">
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="flex items-center">
          <!-- 用户头像 -->
          <view class="w-96rpx h-96rpx bg-#f0f0f0 rounded-full mr-24rpx overflow-hidden">
            <image
              v-if="userInfo?.avatar"
              :src="userInfo.avatar"
              class="w-full h-full"
              mode="aspectFill"
            />
            <view v-else class="w-full h-full flex items-center justify-center">
              <wd-icon name="user" size="48rpx" color="#999"></wd-icon>
            </view>
          </view>

          <!-- 用户信息 -->
          <view class="flex-1">
            <view class="text-32rpx font-600 text-#333 mb-8rpx">
              Hi，{{ userInfo?.studentName || '学生' }}
            </view>
            <view class="text-24rpx text-#999">今日共有课程{{ courseList?.length || 0 }}节！</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 我的预约 -->
    <view class="mx-32rpx mb-32rpx" v-if="courseList?.length">
      <view class="bg-white rounded-24rpx p-32rpx">
        <view class="text-28rpx font-600 text-#333 mb-24rpx">我的预约</view>

        <view v-for="item in courseList" :key="item.id" class="mb-24rpx last:mb-0">
          <view class="flex items-center justify-between">
            <view class="flex-1">
              <view class="text-24rpx text-#333 mb-8rpx">
                今天 {{ item.startTime }}-{{ item.endTime }} ({{
                  dict?.teacher_course_duration?.[item.courseDuration]?.text
                }})
              </view>
              <view class="flex items-center">
                <view class="w-48rpx h-48rpx bg-#f0f0f0 rounded-full mr-16rpx overflow-hidden">
                  <image
                    v-if="item.teacherAvatar"
                    :src="item.teacherAvatar"
                    class="w-full h-full"
                    mode="aspectFill"
                  />
                  <view v-else class="w-full h-full flex items-center justify-center">
                    <wd-icon name="user" size="24rpx" color="#999"></wd-icon>
                  </view>
                </view>
                <view class="text-24rpx text-#666">{{ item.teacherName }}</view>
                <!-- <view class="text-24rpx text-#3d5af5 ml-16rpx">ID:{{ item.id }}</view> -->
              </view>
            </view>

            <view class="ml-24rpx">
              <wd-button
                v-if="item.progress == '2'"
                size="small"
                type="success"
                @click="handleViewTrainingResult(item)"
              >
                训练结果
              </wd-button>
              <wd-button v-else size="small" type="primary" @click="handleGoToClass(item)">
                {{ item.progress == '0' ? '去上课' : '进行中' }}
              </wd-button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐老师 -->
    <view class="mx-32rpx mb-32rpx">
      <view class="flex items-center justify-between mb-24rpx">
        <view class="text-28rpx font-600 text-#333">推荐老师</view>
        <view class="text-24rpx text-#999" @click="handleViewMoreTeachers">更多</view>
      </view>

      <!-- 老师卡片列表 -->
      <view class="space-y-24rpx">
        <view
          v-for="teacher in teachers"
          :key="teacher.teacherId"
          class="bg-white rounded-24rpx p-32rpx"
          @click="handleViewTeacherDetail(teacher)"
        >
          <!-- 老师基本信息 -->
          <view @click="handleViewTeacherDetail(teacher)" class="flex items-center justify-between">
            <!-- 左侧：头像和信息 -->
            <view class="flex items-center flex-1">
              <view class="w-120rpx h-120rpx rounded-full bg-#f0f0f0 mr-24rpx overflow-hidden">
                <image
                  v-if="teacher.avatar"
                  :src="teacher.avatar"
                  class="w-full h-full object-cover"
                  mode="aspectFill"
                />
                <view v-else class="w-full h-full flex items-center justify-center">
                  <image
                    src="/static/images/default_teacher.png"
                    class="w-full h-full object-cover"
                    mode="aspectFill"
                  />
                </view>
              </view>
              <view class="flex-1">
                <view class="text-32rpx font-500 text-#333 mb-8rpx">{{ teacher.teacherName }}</view>
                <view class="text-24rpx text-#666 mb-4rpx">
                  {{ teacherLevelDict[teacher.userLevel].text + '级' }}
                  {{ teacher.education || '本科' }}
                </view>
              </view>
            </view>

            <!-- 右侧：聊天按钮 -->
            <view
              class="rounded-16rpx flex items-center justify-center"
              @click.stop="handleToChat(teacher)"
            >
              <image
                src="/static/images/ic_message.png"
                class="w-80rpx h-80rpx"
                mode="scaleToFill"
              />
              <wd-icon name="comment" size="32rpx" color="white" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 推广活动 -->
  <view class="mx-32rpx mb-32rpx">
    <view class="flex items-center justify-between mb-24rpx">
      <view class="text-28rpx font-600 text-#333">推广活动</view>
    </view>

    <view class="bg-white rounded-24rpx p-32rpx" @click="handleViewActivityDetails">
      <view class="flex items-center justify-between">
        <view class="flex-1">
          <view class="text-28rpx font-600 text-#333 mb-16rpx">每天20分钟，轻松备战四级</view>
          <view class="flex items-center">
            <view
              class="w-48rpx h-48rpx bg-#3d5af5 rounded-full flex items-center justify-center mr-16rpx"
            >
              <wd-icon name="star" size="24rpx" color="#fff"></wd-icon>
            </view>
            <view class="text-24rpx text-#666">1</view>

            <view
              class="w-48rpx h-48rpx bg-#f0f0f0 rounded-full flex items-center justify-center mx-16rpx"
            >
              <wd-icon name="star" size="24rpx" color="#999"></wd-icon>
            </view>
            <view class="text-24rpx text-#666">2</view>

            <view
              class="w-48rpx h-48rpx bg-#f0f0f0 rounded-full flex items-center justify-center mx-16rpx"
            >
              <wd-icon name="star" size="24rpx" color="#999"></wd-icon>
            </view>
            <view class="text-24rpx text-#666">3</view>

            <view
              class="w-48rpx h-48rpx bg-#f0f0f0 rounded-full flex items-center justify-center mx-16rpx"
            >
              <wd-icon name="star" size="24rpx" color="#999"></wd-icon>
            </view>
            <view class="text-24rpx text-#666">4</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { navigateToCoursewareDetail, navigateToSub } from '@/utils'
import {
  attendclassListTodayUsingPost,
  attendclassTeacherPageUsingPost,
  attendclassListSevenDayUsingPost,
} from '@/service/student/appStudentAttendClassPlanManagement'
import { baseinfoCurrentUsingPost } from '@/service/student/appStudentBaseInfoManagement'
import { useDictPage } from '@/hooks/useDictPage'
import { StuSearchTeacherPageRespVO } from '@/service/student'
import { navigateToChat } from '@/utils/chat'
import { useDictStore } from '@/store'
const dictStore = useDictStore()
defineOptions({
  name: 'Student',
})
const sevenDaysList = ref([])
const userInfo = ref()
// 模拟老师数据
const teachers = ref<StuSearchTeacherPageRespVO[]>([])

// 查看老师详情
const handleViewTeacherDetail = (teacher: StuSearchTeacherPageRespVO) => {
  if (teacher.teacherId) {
    navigateToSub(`/teacher-detail/teacher-detail?teacherId=${teacher.teacherId}`)
  } else {
    uni.showToast({
      title: '无法查看详情，缺少老师ID',
      icon: 'none',
    })
  }
}

const handleViewTrainingResult = (row) => {
  const { id } = row
  navigateToSub(`/training-result/training-result?attendClassPlanId=${id}`)
}

// 处理查看更多老师
const handleViewMoreTeachers = () => {
  navigateToSub('/more-teacher/more-teacher')
  uni.showToast({
    title: '查看更多老师',
    icon: 'none',
  })
}
const teacherLevelDict = dictStore.getDictByType('teacher_level')
// 处理去上课按钮点击
const handleGoToClass = (row) => {
  const { coursewareId, id: classId, isToDoAntiForget, isDoneAntiForget } = row
  console.log(row)
  navigateToCoursewareDetail(coursewareId, isToDoAntiForget, isDoneAntiForget, classId)
}
// 找老师沟通
const handleToChat = (teacher) => {
  console.log('teacher', teacher)
  if (!teacher) return
  console.log(teacher)
  navigateToChat(teacher.teacherId.toString(), teacher.teacherName)
}

// 处理查看活动详情
const handleViewActivityDetails = () => {
  uni.showToast({
    title: '查看活动详情',
    icon: 'none',
  })
}
const courseList = ref()
const dict = ref()
const getInit = async () => {
  const user = await baseinfoCurrentUsingPost({})
  userInfo.value = user.data.item
  const sevenDays = await attendclassListSevenDayUsingPost({})
  sevenDaysList.value = sevenDays.data.items
  const res = await attendclassListTodayUsingPost({})
  courseList.value = res.data.items
  dict.value = await useDictPage()
  const teacherRes = await attendclassTeacherPageUsingPost({
    body: {
      pageNo: 1,
      pageSize: 3,
      score: 0,
      schoolName: '',
      userLevel: '',
    },
  })
  teachers.value = teacherRes.data.items
}

onMounted(() => {
  getInit()
})
</script>

<style>
.main-title-color {
  color: #d14328;
}
</style>
