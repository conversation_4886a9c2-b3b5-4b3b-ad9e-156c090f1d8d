/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './teacherComplaintManagement';
import * as API from './types';

/** 【运营】查询投诉详情 POST /pc/teacher/complaint/get */
export function usePcTeacherComplaintGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherComplaintBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherComplaintGetUsingPost,
    onSuccess(data: API.ResultTeacherComplaintBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】处理指定投诉 POST /pc/teacher/complaint/handle */
export function usePcTeacherComplaintHandleUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherComplaintHandleUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询投诉列表 POST /pc/teacher/complaint/page */
export function usePcTeacherComplaintPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultTeacherComplaintBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherComplaintPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultTeacherComplaintBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
