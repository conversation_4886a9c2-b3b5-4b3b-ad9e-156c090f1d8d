<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '个人中心',
    navigationStyle: 'custom',
  },
  needLogin: true,
  isTab: true,
}
</route>
<template>
  <view>
    <view v-if="selectedRole === 'student'">
      <student-mine />
    </view>
    <view v-else>
      <teacher-mine />
    </view>
    <sxt-tab-bar tab-index="mine" />
  </view>
</template>
<script setup lang="ts">
import { useRoleStore } from '@/store'
import studentMine from './components/student-mine.vue'
import teacherMine from './components/teacher-mine.vue'
import { ref } from 'vue'
const { getRole } = useRoleStore()
const selectedRole = ref(getRole())
</script>
