/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生app】发起评价 POST /app/student/evaluate/create */
export async function evaluateCreateUsingPost({
  body,
  options,
}: {
  body: API.TeacherEvaluateCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/app/student/evaluate/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生app】根据id查看评价详情 POST /app/student/evaluate/get */
export async function evaluateGetUsingPost({
  body,
  options,
}: {
  body: API.TeacherEvaluateGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherEvaluateBaseRespVO>(
    '/app/student/evaluate/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
