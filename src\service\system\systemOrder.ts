/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 充值明细 POST /pc/system/order/details */
export async function pcSystemOrderDetailsUsingPost({
  body,
  options,
}: {
  body: API.OrderDetailsDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultOrderDetailsVo>(
    '/pc/system/order/details',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】PC端充值 POST /pc/system/order/recharge */
export async function pcSystemOrderRechargeUsingPost({
  body,
  options,
}: {
  body: API.RechargePcReqVo;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/order/recharge', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
