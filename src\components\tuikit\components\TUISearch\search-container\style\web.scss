.container-bottom {
  margin: 0 10px;
  position: relative;
}

.container-conversation {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tui-search-container {
  &-bottom {
    position: absolute;
    min-width: 400px;
    width: fit-content;
    height: 453px;
    border-radius: 4px;
    background-color: #fff;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: rgba(0,0,0,0.16) 0 3px 6px, rgba(0,0,0,0.23) 0 3px 6px;
    left: 0;
    top: -5px;
  }

  &-aside {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .tui-search-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 10px;

    &-title {
      font-size: 14px;
      font-weight: 500;
      line-height: 19.6px;
      font-family: "PingFang SC", sans-serif;
    }

    &-close {
      cursor: pointer;
    }
  }

  .tui-search-tabs {
    width: 100%;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0,0,0,0.1);

    &-item {
      padding: 10px 0;
      margin: 0 10px;
      font-size: 14px;
      color: #666;
      user-select: none;
      cursor: pointer;

      &-selected {
        color: #007aff;
        border-bottom: 2px solid #007aff;
      }
    }
  }

  .tui-search-time {
    width: 100%;
    height: 43px;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;

    &-item {
      height: calc(100% - 20px);
      padding: 5px;
      margin: 5px;
      border-radius: 5px;
      font-size: 12px;
      color: #666;
      user-select: none;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;

      &-picker {
        display: flex;
      }

      &-close,
      &-all {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &-selected {
        background-color: rgba(0,122,255,0.2);
      }
    }
  }
}
