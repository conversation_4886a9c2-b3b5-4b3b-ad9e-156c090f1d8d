/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 学生答题 POST /ai/exam/answer */
export async function answerUsingPost({
  body,
  options,
}: {
  body: API.AnswerDto;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/ai/exam/answer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 生成试卷返回会话id POST /ai/exam/create */
export async function createUsingPost({
  body,
  options,
}: {
  body: API.ExamDto;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCreateExamVo>('/ai/exam/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据会话id查询试卷内容 POST /ai/exam/queryBySessionId */
export async function queryBySessionIdUsingPost({
  body,
  options,
}: {
  body: API.SessionIdDto;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultExamVo>('/ai/exam/queryBySessionId', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 学生查询自己的试卷 POST /ai/exam/queryByStudentId */
export async function queryByStudentIdUsingPost({
  body,
  options,
}: {
  body: API.PageDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultAnswerExamVo>(
    '/ai/exam/queryByStudentId',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 老师查询试卷 POST /ai/exam/queryByTeacherId */
export async function queryByTeacherIdUsingPost({
  body,
  options,
}: {
  body: API.QueryByTeacherIdDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultAnswerExamVo>(
    '/ai/exam/queryByTeacherId',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
