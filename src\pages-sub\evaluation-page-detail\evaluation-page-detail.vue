<route lang="json5">
{
  layout: 'normal',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: ' ',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- 主内容区域 -->
    <view class="flex-1 flex flex-col items-center justify-center px-48rpx">
      <!-- 标题 -->
      <view class="text-64rpx font-600 text-#333 text-center mb-32rpx">breakfast</view>

      <!-- 提示文字 -->
      <view class="text-24rpx text-#999 text-center mb-80rpx">备注：错误状态和正确状态</view>

      <!-- 选项区域 -->
      <view class="w-full space-y-24rpx">
        <!-- 休息选项 -->
        <view
          class="option-item rest-option"
          :class="{
            selected: selectedOption === 1,
            correct: showResult && correctAnswer === 1,
            incorrect: showResult && selectedOption === 1 && correctAnswer !== 1,
          }"
          @click="selectOption(1)"
        >
          <text class="option-text">休息</text>
        </view>

        <!-- 呼吸选项 -->
        <view
          class="option-item breath-option"
          :class="{
            selected: selectedOption === 2,
            correct: showResult && correctAnswer === 2,
            incorrect: showResult && selectedOption === 2 && correctAnswer !== 2,
          }"
          @click="selectOption(2)"
        >
          <text class="option-text">呼吸</text>
        </view>

        <!-- 胸部选项 -->
        <view
          class="option-item chest-option"
          :class="{
            selected: selectedOption === 3,
            correct: showResult && correctAnswer === 3,
            incorrect: showResult && selectedOption === 3 && correctAnswer !== 3,
          }"
          @click="selectOption(3)"
        >
          <text class="option-text">胸部</text>
        </view>

        <!-- 早晨选项 -->
        <view
          class="option-item morning-option"
          :class="{
            selected: selectedOption === 4,
            correct: showResult && correctAnswer === 4,
            incorrect: showResult && selectedOption === 4 && correctAnswer !== 4,
          }"
          @click="selectOption(4)"
        >
          <text class="option-text">早晨</text>
        </view>

        <!-- 品种选项 -->
        <view
          class="option-item variety-option"
          :class="{
            selected: selectedOption === 5,
            correct: showResult && correctAnswer === 5,
            incorrect: showResult && selectedOption === 5 && correctAnswer !== 5,
          }"
          @click="selectOption(5)"
        >
          <text class="option-text">品种</text>
        </view>

        <!-- 不认识选项 -->
        <view
          class="option-item unknown-option"
          :class="{
            selected: selectedOption === 6,
            correct: showResult && correctAnswer === 6,
            incorrect: showResult && selectedOption === 6 && correctAnswer !== 6,
          }"
          @click="selectOption(6)"
        >
          <text class="option-text">不认识</text>
        </view>
      </view>
    </view>

    <!-- 底部提示和进度 -->
    <view class="fixed bottom-0 w-full">
      <!-- 倒计时提示 -->
      <view class="text-center text-24rpx text-#999 mb-32rpx">停留页面超过10s，建议选择不认识</view>

      <!-- 进度和统计 -->
      <view class="flex items-center justify-around bg-[#F5F6FA] h-11">
        <!-- 左侧进度 -->
        <view class="text-28rpx text-#666">{{ currentQuestion }}/{{ totalQuestions }}</view>

        <!-- 右侧统计 -->
        <!-- 正确数量 -->
        <view class="flex items-center">
          <view
            class="w-32rpx h-32rpx rounded-full bg-#4caf50 mr-16rpx flex items-center justify-center"
          >
            <text class="text-16rpx text-white">✓</text>
          </view>
          <text class="text-28rpx text-#4caf50 font-600">{{ correctCount }}</text>
        </view>

        <!-- 错误数量 -->
        <view class="flex items-center">
          <view
            class="w-32rpx h-32rpx rounded-full bg-#f44336 mr-16rpx flex items-center justify-center"
          >
            <text class="text-16rpx text-white">✗</text>
          </view>
          <text class="text-[36rpx] text-#f44336 font-600">{{ incorrectCount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const selectedOption = ref<number | null>(null)
const showResult = ref(false)
const correctAnswer = ref(2) // 假设正确答案是"呼吸"
const currentQuestion = ref(23)
const totalQuestions = ref(34)
const correctCount = ref(20)
const incorrectCount = ref(3)

// 定时器相关
const timer = ref<NodeJS.Timeout | null>(null)
const timeSpent = ref(0)

// 选择选项
const selectOption = (option: number) => {
  if (showResult.value) return

  selectedOption.value = option
  showResult.value = true

  // 清除定时器
  if (timer.value) {
    clearInterval(timer.value)
  }

  // 模拟答题结果处理
  setTimeout(() => {
    // 这里可以添加跳转到下一题的逻辑
    console.log('选择了选项:', option)
  }, 1500)
}

// 页面加载时启动定时器
onMounted(() => {
  timer.value = setInterval(() => {
    timeSpent.value += 1
    // 10秒后自动提示选择"不认识"
    if (timeSpent.value >= 10 && !selectedOption.value) {
      // 可以在这里添加提示逻辑
    }
  }, 1000)
})

// 页面卸载时清除定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<style lang="scss" scoped>
// 选项样式
.option-item {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;

  .option-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  // 选中状态
  &.selected {
    background: #e3f2fd;
    border: 2rpx solid #2196f3;
    transform: scale(0.98);
  }

  // 正确答案样式
  &.correct {
    background: #e8f5e8;
    border: 2rpx solid #4caf50;

    .option-text {
      color: #2e7d32;
    }
  }

  // 错误答案样式
  &.incorrect {
    background: #fce4ec;
    border: 2rpx solid #f44336;

    .option-text {
      color: #c62828;
    }
  }
}

// 特定选项的背景色
.rest-option {
  background: #fce4ec;
  border-color: #f8bbd9;

  &:not(.selected):not(.correct):not(.incorrect) {
    background: #fce4ec;
    border-color: #f8bbd9;
  }
}

.breath-option {
  background: #e8f5e8;
  border-color: #c8e6c9;

  &:not(.selected):not(.correct):not(.incorrect) {
    background: #e8f5e8;
    border-color: #c8e6c9;
  }
}

.chest-option,
.morning-option,
.variety-option {
  background: #f3f4f6;
  border-color: #d1d5db;

  &:not(.selected):not(.correct):not(.incorrect) {
    background: #f3f4f6;
    border-color: #d1d5db;
  }
}

.unknown-option {
  background: #f5f5f5;
  border-color: #e0e0e0;

  &:not(.selected):not(.correct):not(.incorrect) {
    background: #f5f5f5;
    border-color: #e0e0e0;
  }
}
</style>
