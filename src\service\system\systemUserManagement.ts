/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【系统管理、机构】添加系统用户信息 POST /pc/system/user/create */
export async function pcSystemUserCreateUsingPost({
  body,
  options,
}: {
  body: API.SystemUserAddReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/system/user/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、运营、财务、机构、老师、学生】获取当前登录用户信息 POST /pc/system/user/current */
export async function pcSystemUserCurrentUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSystemUserBaseRespVO>('/pc/system/user/current', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【系统管理、机构】获取指定用户信息 POST /pc/system/user/get */
export async function pcSystemUserGetUsingPost({
  body,
  options,
}: {
  body: API.SystemUserGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSystemUserBaseRespVO>('/pc/system/user/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、机构】修改指定用户信息 POST /pc/system/user/modify */
export async function pcSystemUserModifyUsingPost({
  body,
  options,
}: {
  body: API.SystemUserModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/user/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、机构】获取用户列表 POST /pc/system/user/page */
export async function pcSystemUserPageUsingPost({
  body,
  options,
}: {
  body: API.SystemUserPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSystemUserBaseRespVO>(
    '/pc/system/user/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【系统管理、机构】删除指定用户信息 POST /pc/system/user/remove */
export async function pcSystemUserRemoveUsingPost({
  body,
  options,
}: {
  body: API.SystemUserRemoveReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/user/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
