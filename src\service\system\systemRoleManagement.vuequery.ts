/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemRoleManagement';
import * as API from './types';

/** 【系统管理、机构】给角色分配菜单 POST /pc/system/role/allocMenu */
export function usePcSystemRoleAllocMenuUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultInteger) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemRoleAllocMenuUsingPost,
    onSuccess(data: API.ResultInteger) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、运营、财务、机构、老师、学生】获取角色相关菜单 POST /pc/system/role/getMenuList */
export function usePcSystemRoleGetMenuListUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListString) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemRoleGetMenuListUsingPost,
    onSuccess(data: API.ResultListString) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【系统管理、机构】获取角色列表 POST /pc/system/role/page */
export function usePcSystemRolePageUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponsePageResultSystemRoleDO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemRolePageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultSystemRoleDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
