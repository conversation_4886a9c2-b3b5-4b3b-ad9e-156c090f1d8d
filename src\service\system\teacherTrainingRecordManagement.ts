/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】对指定老师进行培训审核操作 POST /pc/teacher/training/create */
export async function pcTeacherTrainingCreateUsingPost({
  body,
  options,
}: {
  body: API.TrainingRecordCreatReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/teacher/training/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】获取指定id的培训审核记录明细信息 POST /pc/teacher/training/get */
export async function pcTeacherTrainingGetUsingPost({
  body,
  options,
}: {
  body: API.TrainingRecordGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTrainingRecordDetialRespVO>(
    '/pc/teacher/training/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询培训审核记录信息 POST /pc/teacher/training/page */
export async function pcTeacherTrainingPageUsingPost({
  body,
  options,
}: {
  body: API.TrainingRecordPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultTrainingRecordDetialRespVO>(
    '/pc/teacher/training/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
