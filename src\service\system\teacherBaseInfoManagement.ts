/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】添加老师信息 POST /pc/teacher/baseinfo/create */
export async function pcTeacherBaseinfoCreateUsingPost({
  body,
  options,
}: {
  body: API.UserCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherAndStudentRespVO>(
    '/pc/teacher/baseinfo/create',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】获取指定id的老师信息详情 POST /pc/teacher/baseinfo/get */
export async function pcTeacherBaseinfoGetUsingPost({
  body,
  options,
}: {
  body: API.UserGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherUserDO>('/pc/teacher/baseinfo/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】修改指定老师信息 POST /pc/teacher/baseinfo/modify */
export async function pcTeacherBaseinfoModifyUsingPost({
  body,
  options,
}: {
  body: API.UserModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/teacher/baseinfo/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】根据指定条件获取老师分页列表 返回值: default response POST /pc/teacher/baseinfo/page */
export async function pcTeacherBaseinfoPageUsingPost({
  body,
  options,
}: {
  body: API.UserPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.TeacherUserDO>('/pc/teacher/baseinfo/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
