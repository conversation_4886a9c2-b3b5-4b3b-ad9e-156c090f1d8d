/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appTeacherBaseInfoManagement';
import * as API from './types';

/** 【老师】获取当前登录的老师信息详情 返回值: default response POST /app/teacher/baseinfo/current */
export function useBaseinfoCurrentUsingPostMutation(options?: {
  onSuccess?: (value?: API.TeacherUserDO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoCurrentUsingPost,
    onSuccess(data: API.TeacherUserDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 老师使用账号密码登录 返回值: default response POST /app/teacher/baseinfo/login */
export function useBaseinfoLoginUsingPostMutation(options?: {
  onSuccess?: (value?: API.UserLoginVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoLoginUsingPost,
    onSuccess(data: API.UserLoginVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】修改老师信息 POST /app/teacher/baseinfo/modify */
export function useBaseinfoModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 注册老师信息 POST /app/teacher/baseinfo/register */
export function useBaseinfoRegisterUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherAndStudentRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.baseinfoRegisterUsingPost,
    onSuccess(data: API.ResultTeacherAndStudentRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
