import { defineStore } from 'pinia'
import { ref } from 'vue'
import { pcSystemDictPageUsingPost } from '@/service/system'

// 定义枚举数据的类型
export interface DictItem {
  text: string
  [key: string]: any
}

export interface DictType {
  [key: string]: {
    [key: string]: DictItem
  }
}

export const useDictStore = defineStore(
  'dict',
  () => {
    // 存储所有字典数据
    const dictData = ref<DictType>({})

    // 加载状态
    const loading = ref(false)

    // 是否已初始化
    const initialized = ref(false)

    // 获取字典数据
    const fetchDictData = async () => {
      // 如果已经初始化过，则直接返回
      if (initialized.value && Object.values(dictData.value).length !== 0) {
        return dictData.value
      }
      loading.value = true
      try {
        const res = await pcSystemDictPageUsingPost({
          body: {
            pageNo: 1,
            pageSize: 99999,
            type: '',
          },
        })

        if (res.code === 200 && res.data?.items) {
          // 处理字典数据
          const obj = res.data.items.reduce((pre: any, item: any) => {
            pre[item.type] = item.datas.reduce((pr: any, ite: any) => {
              pr[ite.value + ''] = { text: ite.label, ...ite }
              return pr
            }, {})
            return pre
          }, {})
          dictData.value = obj
          localStorage.setItem('dict', obj)
          initialized.value = true
        }
        return dictData.value
      } catch (error) {
        return {}
      } finally {
        loading.value = false
      }
    }

    // 获取指定类型的字典数据
    const getDictByType = (type: string) => {
      return Object.values(dictData.value[type] || {})
    }

    // 获取指定类型和值的字典项
    const getDictItem = (type: string, value: string | number) => {
      const dict = getDictByType(type)
      return dict[value + ''] || { text: '' }
    }

    // 获取指定类型和值的字典文本
    const getDictText = (type: string, value: string | number) => {
      const item = getDictItem(type, value)
      return item.text || ''
    }

    // 重置字典数据
    const resetDict = () => {
      dictData.value = {}
      initialized.value = false
    }

    return {
      dictData,
      loading,
      initialized,
      fetchDictData,
      getDictByType,
      getDictItem,
      getDictText,
      resetDict,
    }
  },
  {
    persist: true, // 持久化存储
  },
)
