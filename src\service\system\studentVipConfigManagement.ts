/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】上架学生vip权益配置信息 POST /pc/student/vipconfig/active */
export async function pcStudentVipconfigActiveUsingPost({
  body,
  options,
}: {
  body: API.StudentVipConfigGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/student/vipconfig/active', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】新增学生vip权益配置 POST /pc/student/vipconfig/create */
export async function pcStudentVipconfigCreateUsingPost({
  body,
  options,
}: {
  body: API.StudentVipConfigCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/student/vipconfig/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】查询学生vip权益配置详情 POST /pc/student/vipconfig/get */
export async function pcStudentVipconfigGetUsingPost({
  body,
  options,
}: {
  body: API.StudentVipConfigGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStudentVipConfigBaseRespVO>(
    '/pc/student/vipconfig/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询学生vip权益配置信息 POST /pc/student/vipconfig/page */
export async function pcStudentVipconfigPageUsingPost({
  body,
  options,
}: {
  body: API.StudentVipConfigPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStudentVipConfigBaseRespVO>(
    '/pc/student/vipconfig/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
