/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './tengxunCoSshang<PERSON>anwen<PERSON>an';
import * as API from './types';

/** 文件上传 POST /pc/cos/upload */
export function usePcCosUploadUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponseString) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCosUploadUsingPost,
    onSuccess(data: API.PageResultResponseString) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
