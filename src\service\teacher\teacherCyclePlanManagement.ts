/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【老师】创建周期性课程计划记录信息 POST /app/teacher/cycleplan/create */
export async function cycleplanCreateUsingPost({
  body,
  options,
}: {
  body: API.CourseCyclePlanCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/cycleplan/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【老师】获取当前老师的周期性课程计划记录明细信息 POST /app/teacher/cycleplan/current */
export async function cycleplanCurrentUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCourseCyclePlanDetialRespVO>(
    '/app/teacher/cycleplan/current',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【老师】更新周期性课程计划记录信息 POST /app/teacher/cycleplan/modify */
export async function cycleplanModifyUsingPost({
  body,
  options,
}: {
  body: API.CourseCyclePlanUpdateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/cycleplan/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【老师】删除指定id的周期性课程计划记录明细信息 POST /app/teacher/cycleplan/remove */
export async function cycleplanRemoveUsingPost({
  body,
  options,
}: {
  body: API.CyclePlanRemoveReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/cycleplan/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
