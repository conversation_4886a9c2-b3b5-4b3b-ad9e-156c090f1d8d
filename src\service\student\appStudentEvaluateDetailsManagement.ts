/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】创建评测记录 POST /app/student/evaluatedetails/create */
export async function evaluatedetailsCreateUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/app/student/evaluatedetails/create', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【学生】更新评测进度 POST /app/student/evaluatedetails/updateProgress */
export async function evaluatedetailsUpdateProgressUsingPost({
  body,
  options,
}: {
  body: API.WordDetailsUpdateProcessReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>(
    '/app/student/evaluatedetails/updateProgress',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
