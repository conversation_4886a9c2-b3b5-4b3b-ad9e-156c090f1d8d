<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '课前抗遗忘',
  },
}
</route>

<template>
  <sxt-container @scrolltolower="onScrolltolower">
    <!-- 单词列表 -->
    <view class="p-32rpx">
      <view class="space-y-24rpx">
        <view
          v-for="(word, index) in wordList"
          :key="index"
          :class="{ isActive: currentClickWordId === word.id }"
          class="word-container flex items-center justify-between py-24rpx px-16rpx bg-white rounded-16rpx shadow-sm border border-gray-100"
        >
          <!-- 左侧序号和单词 -->
          <view class="flex items-center flex-1">
            <!-- 序号 -->
            <view class="w-32rpx text-24rpx text-gray-500 font-500 mr-24rpx">{{ index + 1 }}.</view>

            <!-- 单词内容 -->
            <view class="flex-1">
              <view class="text-32rpx font-600 text-gray-800 mb-4rpx">
                {{ word.word }}
              </view>
            </view>
            <view class="flex-2">
              <wd-icon @click="playSound(word)" color=" #80d4ff" name="sound" size="22px"></wd-icon>
            </view>
            <view
              class="w-48rpx flex-1 h-48rpx rounded-full border-2 gap-5 flex items-center justify-center cursor-pointer transition-all duration-300 ml-24rpx"
            >
              <wd-icon
                v-if="word.answer === 1"
                name="check-outline"
                color="#80d4ff"
                size="22px"
              ></wd-icon>
              <wd-icon
                v-if="word.answer === -1"
                @click="markTrue(word, index)"
                name="check-outline"
                size="22px"
              ></wd-icon>
              <wd-icon
                v-if="word.answer === 0"
                name="close-outline"
                color=" #ff0000"
                size="22px"
              ></wd-icon>
              <wd-icon
                v-if="word.answer === -1"
                @click="markFalse(word, index)"
                name="close-outline"
                size="22px"
              ></wd-icon>
            </view>
          </view>

          <!-- 右侧勾选按钮 -->
        </view>
      </view>
      <view v-if="loading" class="text-center text-24rpx text-#666 py-32rpx">加载中...</view>
      <view v-if="noMore && wordList.length > 0" class="text-center text-24rpx text-#666 py-32rpx">
        没有更多数据了
      </view>
      <view
        v-if="wordList.length === 0 && !loading"
        class="text-center text-24rpx text-#666 py-64rpx"
      >
        暂无老师数据
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-32rpx border-t border-gray-200 shadow-lg">
      <!-- 操作按钮 -->
      <view class="flex gap-24rpx">
        <wd-button type="text" size="large" custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx">
          {{ '已复习' + finishCount + '/' + wordList.length }}
        </wd-button>
        <wd-button
          type="text"
          size="large"
          @click="handleEndClass"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx bg-blue-500"
        >
          {{ '结束抗遗忘' }}
        </wd-button>
      </view>
    </view>

    <!-- 底部安全间距 -->
    <view class="h-200rpx"></view>
  </sxt-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast, navigateBack, navigateToSub } from '@/utils'
import {
  attendclassWordAntiforgetPageUsingPost,
  attendclassUpdateAntiforgetWordProssUsingPost,
  attendclassCompleteAntiforgetWordUsingPost,
} from '@/service/teacher'
import { useDictStore } from '@/store'
import { useMessage, useToast } from 'wot-design-uni'
import { createUsingPost, queryBySessionIdUsingPost } from '@/service/ai'
const toast = useToast()
const message = useMessage('classPractise')
const totalCount = ref(90)
const wordList = ref([])
// 加载状态
const loading = ref(false)
// 没有更多数据
const noMore = ref(false)
const classId = ref()
const coursewareId = ref()
const searchForm = ref({
  pageSize: 15,
  pageNo: 1,
  attendClassPlanId: classId.value,
})
// 需要课堂练习的单词集合
const currentClickWordId = ref()
const dictStore = useDictStore()
const isGetAIpractise = ref(undefined)
const learnedWordsCount = computed(() => {
  return wordList.value.filter((word) => word.isCompletedLearn === '1').length
})
const PractiseSessionId = ref('')
// 单词分页查询
const searchWords = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await attendclassWordAntiforgetPageUsingPost({
      body: {
        ...searchForm.value,
      },
    })
    const needLearnWords = res.data?.items?.map((item) => {
      return {
        ...item,
        answer: -1,
      }
    })
    if (isLoadMore) {
      wordList.value = [...wordList.value, ...needLearnWords]
    } else {
      wordList.value = needLearnWords
    }

    // 更新总记录数
    totalCount.value = (res.data as any).total || needLearnWords.length

    noMore.value = needLearnWords.length < searchForm.value.pageSize
    if (isLoadMore) {
      searchForm.value.pageNo++
    }
  } catch (error) {
    console.error('搜索老师失败', error)
    uni.showToast({
      title: '搜索失败，请稍后重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}
// 调用更新抗遗忘的接口
const refishForgetProgress = async (form, index, answer) => {
  try {
    const res = await attendclassUpdateAntiforgetWordProssUsingPost({ body: form })
    if (res.code === 200) {
      console.log('学习成功')
      wordList.value[index].isCompletedAntiforget = '1'
      wordList.value[index].answer = answer
    }
  } catch (err) {
    console.log(err)
  }
}
// 更新单词进度为错误
const markFalse = (word, index) => {
  const from = {
    attendClassPlanId: classId.value,
    coursewareId: coursewareId.value,
    wordId: word.id,
    wordContent: word.word,
    isCorrect: 0,
  }
  refishForgetProgress(from, index, 0)
}
// 更新单词进度为正确
const markTrue = (word, index) => {
  const from = {
    attendClassPlanId: classId.value,
    coursewareId: coursewareId.value,
    wordId: word.id,
    wordContent: word.word,
    isCorrect: 1,
  }
  refishForgetProgress(from, index, 1)
}
// 标记当前点击的单词（高亮）
defineOptions({
  name: 'ChooseWordsPage',
})

// 计算选中的单词数量
const finishCount = computed(() => {
  return wordList.value.filter((word) => word.isCompletedAntiforget === '1').length
})

// 结束课程
const handleEndClass = async () => {
  try {
    const res = await attendclassCompleteAntiforgetWordUsingPost({
      body: {
        id: Number(classId.value),
      },
    })
    if (res.code === 200) {
      showToast('抗遗忘成功')
      navigateToSub(
        `/practise-result/practise-result?attendClassPlanId=${classId.value}&coursewareId=${coursewareId.value}&type=antiforget`,
      )
    }
  } catch (error) {
    console.log('error', error)
  }
}

const playSound = (word) => {
  if (word.mp3) {
    const audio = new Audio(word.mp3)
    audio.play().catch((err) => {
      console.log('播放音频失败', err)
    })
  }
  console.log('播放音频', word.mp3)
}
// 返回上一页
const handleBack = () => {
  navigateBack()
}

const getInit = async () => {
  await searchWords(true)
}
onLoad((option) => {
  if (option.classId) {
    console.log('option.classId', option.classId)
    classId.value = option.classId
    searchForm.value.attendClassPlanId = classId.value
  }
  if (option.coursewareId) {
    coursewareId.value = option.coursewareId
  }
})
const onScrolltolower = () => {
  console.log('是否触发了上拉加载更多')
  if (!noMore.value) {
    searchWords(true)
  }
}

onMounted(() => {
  getInit()
  console.log('dictStore.dictData', dictStore.dictData)
  // 监听上拉触底事件
})
</script>
<style lang="css" scoped>
.word-container:hover {
  border: 2px solid #b8b1b1;
}
.isActive {
  background-color: #b7b1b1;
  opacity: 0.8;
  border: 2px solid #b7b3b3;
}
:deep(.wd-message-box .wd-message-box__container) {
  width: 600rpx;
}

:deep(.wd-message-box .wd-message-box__flex) {
  display: flex;
  justify-content: center;
}
</style>
