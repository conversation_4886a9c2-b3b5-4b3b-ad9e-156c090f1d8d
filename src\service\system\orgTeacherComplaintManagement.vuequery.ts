/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './orgTeacherComplaintManagement';
import * as API from './types';

/** 【机构】查询投诉详情 POST /pc/orgteacher/complaint/get */
export function usePcOrgteacherComplaintGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherComplaintBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgteacherComplaintGetUsingPost,
    onSuccess(data: API.ResultTeacherComplaintBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【机构】处理投诉 POST /pc/orgteacher/complaint/handle */
export function usePcOrgteacherComplaintHandleUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgteacherComplaintHandleUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【机构】分页查询投诉列表 POST /pc/orgteacher/complaint/page */
export function usePcOrgteacherComplaintPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultTeacherComplaintBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgteacherComplaintPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultTeacherComplaintBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
