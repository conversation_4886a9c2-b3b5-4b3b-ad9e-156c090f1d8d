/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】添加基础课件资料信息 POST /pc/courseware/create */
export async function pcCoursewareCreateUsingPost({
  body,
  options,
}: {
  body: API.CoursewareCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/courseware/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】下载课件基础资料模板 POST /pc/courseware/downTemplate */
export async function pcCoursewareDownTemplateUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<string>('/pc/courseware/downTemplate', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【运营】查询基础课件资料详情信息 POST /pc/courseware/get */
export async function pcCoursewareGetUsingPost({
  body,
  options,
}: {
  body: API.CoursewareGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCoursewareByAdminPageRespVO>('/pc/courseware/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】批量导入课件基础资料 POST /pc/courseware/import */
export async function pcCoursewareOpenApiImportUsingPost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pcCoursewareOpenApiImportUsingPostParams;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/import', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 【运营】修改基础课件资料信息 POST /pc/courseware/modify */
export async function pcCoursewareModifyUsingPost({
  body,
  options,
}: {
  body: API.CoursewareModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】分页查询课程资料信息 POST /pc/courseware/page */
export async function pcCoursewarePageUsingPost({
  body,
  options,
}: {
  body: API.CoursewareByAdminPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareByAdminPageRespVO>(
    '/pc/courseware/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】删除基础课件资料信息 POST /pc/courseware/remove */
export async function pcCoursewareRemoveUsingPost({
  body,
  options,
}: {
  body: API.CoursewareGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
