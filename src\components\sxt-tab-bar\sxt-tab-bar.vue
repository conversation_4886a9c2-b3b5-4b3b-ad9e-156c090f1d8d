<template>
  <wd-tabbar fixed :model-value="tabbar" bordered safeAreaInsetBottom placeholder @change="change">
    <wd-tabbar-item
      v-for="tab in tabList"
      :key="tab.pagePath"
      :title="tab.text"
      custom-class="pt-[12rpx] !text-[24rpx]"
    >
      <template #icon>
        <wd-img
          height="40rpx"
          width="40rpx"
          :src="tabbar === tab.value ? tab.selectedIconPath : tab.iconPath"
        ></wd-img>
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<script lang="ts" setup>
import { RoleEmu, useRoleStore } from '@/store'
import { navigateTo, navigateToSub, switchTab } from '@/utils'

const { role, getRole } = useRoleStore()
const props = defineProps({
  tabIndex: {
    type: String,
    required: true,
  },
})
const tabbar = ref(props.tabIndex)
const arr = [
  {
    iconPath: '/static/tabbar/sy.png',
    selectedIconPath: '/static/tabbar/syh.png',
    pagePath: '/index/index',
    value: 'index',
    text: '首页',
  },

  {
    iconPath: '/static/tabbar/wd.png',
    selectedIconPath: '/static/tabbar/wdh.png',
    pagePath: '/mine/mine',
    value: 'mine',
    text: '我的',
  },
]
const tabList = ref(arr)

const change = ({ value }) => {
  const node = tabList.value[value]
  console.log(value, tabList.value)
  navigateTo(node?.pagePath)
}
onShow(() => {
  if (RoleEmu.Student === getRole()) {
    tabList.value = [...arr].splice(1, 0, {
      iconPath: '/static/tabbar/zx.png',
      selectedIconPath: '/static/tabbar/zxh.png',
      pagePath: '/self-study/self-study',
      value: 'study',
      text: '自学',
    })
  }
})
</script>
