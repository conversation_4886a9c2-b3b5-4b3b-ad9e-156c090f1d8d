<route lang="json5">
{
  layout: 'login',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>

<template>
  <view class="container bg-[#F5F6FA] flex flex-col h-screen">
    <!-- Logo和标题区域 -->
    <view class="flex-1 flex flex-col items-center justify-center px-48rpx">
      <!-- Logo -->
      <image
        class="w-160rpx h-160rpx mb-140rpx"
        src="../../assets/images/logo_login.png"
        mode="scaleToFill"
      />

      <!-- 登录表单 -->
      <wd-form ref="form" :model="model" :errorType="'toast'" class="w-full">
        <view class="space-y-32rpx mb-48rpx">
          <!-- 手机号输入框 -->
          <view class="relative">
            <wd-input
              v-model="model.account"
              prop="account"
              placeholder="请输入手机号"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写手机号' }]"
            />
          </view>

          <!-- 密码输入框 -->
          <view class="relative mb-48rpx">
            <wd-input
              v-model="model.password"
              prop="password"
              placeholder="请输入密码"
              show-password
              custom-input-class="h-96rpx! pl-32rpx! pr-80rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写密码' }]"
            />
          </view>
          <!-- <view class="relative">
            <wd-radio-group class="role-select-group" v-model="model.radioVal" shape="button">
              <wd-radio :value="1">我是老师</wd-radio>
              <wd-radio :value="2">我是学生</wd-radio>
            </wd-radio-group>
          </view> -->
        </view>
      </wd-form>

      <!-- 操作区域 -->
      <view class="w-full space-y-32rpx">
        <!-- 忘记密码链接 -->
        <view class="flex justify-between items-center text-28rpx">
          <view class="flex items-center">
            <text class="text-#999">还没有账户？</text>
            <text class="text-#3d5af5 ml-8rpx" @click="goToRoleSelect">立即注册</text>
          </view>
          <text class="text-#3d5af5">忘记密码？</text>
        </view>

        <!-- 登录按钮 -->
        <wd-button
          custom-class="w-full! h-96rpx! rounded-12rpx! text-32rpx! font-500! bg-#3d5af5! border-#3d5af5!"
          type="primary"
          @click="handSubmit"
        >
          登录
        </wd-button>

        <!-- 用户协议 -->
        <view class="flex items-center justify-center text-center text-#999 leading-relaxed">
          登录即表示同意《用户协议》和《隐私政策》
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import * as student from '@/service/student'
import * as teacher from '@/service/teacher'
import { RoleEmu, useRoleStore } from '@/store'
import { showToast, reLaunch, navigateToSub } from '@/utils'
const { getRole } = useRoleStore()
const selectedRole = ref<RoleEmu>(getRole())
const form = ref()
const model = ref({
  account: '***********', // '***********' , // *********** 老师 账号：TP000546 密码：IMFSbWRCDT
  password: '4WdDjRrgPc', // '4WdDjRrgPc', // IkgTJnbi94
})
const handSubmit = async () => {
  const res = await form.value.validate()
  if (!res.valid) return

  try {
    const { ...otherData } = model.value
    const fn =
      selectedRole.value === RoleEmu.Teacher
        ? teacher.baseinfoLoginUsingPost
        : student.baseinfoLoginUsingPost
    // 先尝试学生登录
    let resLogin: any
    try {
      resLogin = await fn({ body: otherData })
    } catch (studentError) {
      // 学生登录失败，尝试老师登录
      showToast('登录失败，请检查账号密码')
      return
    }

    if (resLogin.code === 200) {
      showToast('登录成功')
      uni.setStorageSync('token', resLogin.data.item.accessToken)
      uni.setStorageSync('userSig', resLogin.data.item.userSig)
      uni.setStorageSync('userID', model.value.account)
      uni.setStorageSync('userInfo', resLogin.data.item)
      reLaunch('/index/index')
    } else {
      showToast(resLogin.message || '登录失败，请检查账号密码')
    }
  } catch (error) {
    console.error('登录异常:', error)
    showToast('登录失败，请稍后重试')
  }
}
const goToRoleSelect = () => {
  navigateToSub('/role-select/role-select?to=register')
}
</script>
<style lang="css" scoped>
.container {
  min-height: calc(100vh - 44px);
}
.role-select-group {
  padding-block: 20rpx;
  background: transparent;
}
</style>
