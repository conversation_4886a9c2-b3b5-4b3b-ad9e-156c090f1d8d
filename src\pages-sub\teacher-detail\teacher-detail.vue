<route lang="json5">
{
  style: {
    navigationBarTitleText: '老师详情',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="container min-h-screen bg-#f8f8f8">
    <wd-loading v-if="isLoading" />

    <view v-if="!isLoading && teacherInfo" class="px-32rpx pt-32rpx">
      <!-- 老师信息卡片 -->
      <view class="bg-white rounded-16rpx p-32rpx mb-24rpx">
        <view class="flex items-start">
          <!-- 头像 -->
          <view class="w-120rpx h-120rpx rounded-full overflow-hidden mr-24rpx flex-shrink-0">
            <image
              :src="teacherInfo.avatar || '/static/images/avatar_placeholder.png'"
              class="w-full h-full object-cover"
              mode="aspectFill"
            />
          </view>

          <!-- 信息区域 -->
          <view class="flex-1">
            <view class="text-32rpx font-600 text-#333 mb-8rpx">
              {{ teacherInfo.name || '李红梅' }}
            </view>
            <view class="text-24rpx text-#666 mb-8rpx">
              {{ teacherInfo.school || '电子科技大学' }} {{ teacherInfo.major || '硕士' }}
            </view>

            <!-- 星级评分 -->
            <view class="flex items-center">
              <view class="flex mr-16rpx">
                <text
                  v-for="i in 5"
                  :key="i"
                  class="text-24rpx mr-4rpx"
                  :class="i <= (teacherInfo.rating || 5) ? 'text-#ffa500' : 'text-#ddd'"
                >
                  ★
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 老师介绍 - 在同一个卡片内 -->
        <view class="mt-24rpx pt-24rpx border-t border-#f0f0f0">
          <view class="text-24rpx text-#666 leading-relaxed">
            {{
              teacherInfo.introduction ||
              '经验丰富的英语老师，擅长帮助学生提高英语水平，已帮助1000+学生成功背诵单词。教学方法独特，能够根据学生的特点制定个性化的学习方案，深受学生喜爱。'
            }}
          </view>
        </view>
      </view>

      <!-- 已有上课计划 -->
      <view class="bg-white rounded-16rpx p-32rpx mb-24rpx">
        <view class="text-28rpx font-500 text-#333 mb-24rpx">已有上课计划</view>

        <view v-if="teacherInfo.classPlans?.length > 0" class="space-y-24rpx">
          <view
            v-for="classPlan in teacherInfo.classPlans"
            :key="classPlan"
            class="flex items-center justify-between py-16rpx border-b border-#f0f0f0 last:border-b-0"
          >
            <view class="flex items-center">
              <view class="text-32rpx font-500 text-#333 mr-24rpx w-120rpx">
                {{ classPlan.trainingTime }}
              </view>
              <view class="text-24rpx text-#666">{{ getClassDuration(classPlan) }}</view>
            </view>
            <view
              class="px-16rpx py-8rpx rounded-8rpx text-20rpx"
              :class="
                classPlan.reservationStatus === '0'
                  ? 'bg-#e8f5e8 text-#4caf50'
                  : 'bg-#fff3e0 text-#ff9800'
              "
            >
              {{ classPlan.reservationStatus === '0' ? '待确认' : '已预约' }}
            </view>
          </view>
        </view>

        <view v-else class="text-center py-64rpx text-24rpx text-#999">今天没有课程安排</view>
      </view>
    </view>

    <view v-if="!isLoading && !teacherInfo" class="text-center pt-200rpx text-24rpx text-#999">
      无法加载老师信息
    </view>

    <!-- 底部操作栏 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-#f0f0f0 px-32rpx py-24rpx">
      <view class="flex gap-24rpx">
        <image
          @click="communicateWithTeacher"
          src="../../assets/images/ic_message.png"
          class="w-80rpx h-80rpx"
          mode="scaleToFill"
        />
        <wd-button
          custom-class="flex-1 h-88rpx! rounded-44rpx! text-28rpx! font-400! bg-#4285f4! border-#4285f4!"
          type="primary"
          @click="goToBookClass"
        >
          去约课
        </wd-button>
        <wd-button
          custom-class="flex-1 h-88rpx! rounded-44rpx! text-28rpx! font-400! bg-#ff6b35! border-#ff6b35!"
          type="primary"
          @click="goToTrialClass"
        >
          上体验课
        </wd-button>
      </view>
    </view>

    <!-- 体验课预约弹窗 -->
    <wd-popup v-model="showTrialClassModal" position="bottom" :safe-area-inset-bottom="true">
      <view class="bg-white rounded-t-32rpx p-32rpx">
        <!-- 老师信息 -->
        <view class="flex items-center mb-32rpx">
          <view class="w-80rpx h-80rpx rounded-full overflow-hidden mr-24rpx">
            <image
              :src="teacherInfo?.avatar || '/static/images/avatar_placeholder.png'"
              class="w-full h-full object-cover"
              mode="aspectFill"
            />
          </view>
          <view class="flex-1">
            <view class="text-28rpx font-500 text-#333">{{ teacherInfo?.name || '--' }}</view>
            <view class="text-24rpx text-#666">{{ teacherInfo?.school || '--' }}</view>
          </view>
        </view>

        <!-- 价格信息 -->
        <view class="text-center mb-32rpx">
          <view class="text-48rpx font-600 text-#ff4444">{{ TrialClassPrice }}</view>
          <view class="text-24rpx text-#999">(体验价)</view>
        </view>

        <!-- 表单信息 -->
        <view class="space-y-24rpx mb-48rpx">
          <!-- 体验日期 -->
          <view class="flex items-center justify-between py-24rpx border-b border-#f0f0f0">
            <text class="text-28rpx text-#333">体验日期</text>
            <wd-calendar
              v-model="trialForm.date"
              type="date"
              :columns="dateColumns"
              placeholder="请选择"
              custom-class="text-#999"
            />
          </view>

          <!-- 上课时间 -->
          <view class="flex items-center justify-between py-24rpx border-b border-#f0f0f0">
            <text class="text-28rpx text-#333">上课时间</text>
            <wd-datetime-picker
              v-model="trialForm.time"
              placeholder="请选择"
              custom-class="text-#999"
              default-value="09:00"
              type="time"
            />
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="flex gap-24rpx">
          <wd-button
            custom-class="flex-1 h-88rpx! rounded-44rpx! text-28rpx! font-400!"
            @click="showTrialClassModal = false"
          >
            取消
          </wd-button>
          <wd-button
            custom-class="flex-1 h-88rpx! rounded-44rpx! text-28rpx! font-400! bg-#ff6b35! border-#ff6b35!"
            type="primary"
            @click="confirmTrialClass"
          >
            确定
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { attendclassTeacherDetailUsingPost } from '@/service/student/appStudentAttendClassPlanManagement'
import { navigateToChat } from '@/utils/chat'
import { pcSystemPriceConfigListUsingPost } from '@/service/system'
import { reservationCreateUsingPost } from '@/service/student'
import dayjs from 'dayjs'

defineOptions({
  name: 'TeacherDetail',
})

const isLoading = ref(true)
const teacherId = ref<number | null>(null)
const teacherInfo = ref<any>(null)
const TrialClassPrice = ref(0)
// 体验课弹窗相关
const showTrialClassModal = ref(false)
const trialForm = ref({
  date: 0,
  time: '09:00',
})

// 日期选择器数据
const getClassDuration = (plan: any) => {
  if (plan.courseDuration === '2') return '试听课'
  const duration = plan.courseDuration === '0' ? '30分钟' : '60分钟'
  return `${duration}正课`
}
const goToTrialClass = async () => {
  const priceConfig = await getPriceConfigofTrialClass()
  console.log(priceConfig)
  if (!priceConfig) return
  TrialClassPrice.value = priceConfig?.discountPrice
  showTrialClassModal.value = true
}
// 查询体验课的课程价格
const getPriceConfigofTrialClass = async () => {
  const res = await pcSystemPriceConfigListUsingPost({
    body: {
      durationType: 2,
    },
  })
  console.log(res)
  if (res.code === 200 && res.data?.items?.length) {
    return res.data.items[0]
  }
}
// 模拟页面加载时从 options 获取 teacherId
onLoad((options: any) => {
  if (options.teacherId) {
    teacherId.value = parseInt(options.teacherId, 10)
    fetchTeacherDetail()
  } else {
    console.error('缺少 teacherId')
    isLoading.value = false
  }
})

// 获取老师详情
const fetchTeacherDetail = async () => {
  if (!teacherId.value) return
  isLoading.value = true
  try {
    const res = await attendclassTeacherDetailUsingPost({
      body: { id: teacherId.value },
    })
    if (res.code === 200 && res.data) {
      teacherInfo.value = res.data.item
    } else {
      console.error('获取老师详情失败:', res.message)
    }
  } catch (error) {
    console.error('获取老师详情异常:', error)
  } finally {
    isLoading.value = false
  }
}

// 找老师沟通
const communicateWithTeacher = () => {
  if (!teacherInfo.value) return
  console.log(teacherInfo.value)
  navigateToChat(teacherInfo.value.account.toString(), teacherInfo.value.name)
}

// 去约课
const goToBookClass = () => {
  if (!teacherInfo.value) return
  const params = new URLSearchParams({
    teacherId: teacherInfo.value.id,
    name: encodeURIComponent(teacherInfo.value.name),
    school: encodeURIComponent(teacherInfo.value.school),
    major: encodeURIComponent(teacherInfo.value.major),
    // avatar: encodeURIComponent(teacherInfo.value.avatar), // 如果有头像信息
  }).toString()
  uni.navigateTo({
    url: `/pages-sub/book-class/book-class?${params}`,
  })
}

// 确认体验课预约
const confirmTrialClass = async () => {
  if (!trialForm.value.date || !trialForm.value.time) {
    uni.showToast({
      title: '请选择体验日期和上课时间',
      icon: 'none',
    })
    return
  }

  // TODO: 调用预约体验课API
  console.log(trialForm.value)
  // 预约课参数
  const params = {
    teacherId: teacherInfo.value?.id,
    courseDuration: 2,
    trainingDateList: [dayjs(trialForm.value.date).format('YYYY-MM-DD')],
    trainingTime: trialForm.value.time,
    // 体验课不传次数和周期
  }
  const res = await reservationCreateUsingPost({ body: params })
  if (res.code === 200) {
    uni.showToast({
      title: '预约成功',
      icon: 'success',
    })
  }
  showTrialClassModal.value = false
  // 重置表单
  trialForm.value = {
    date: 0,
    time: '09:00',
  }
}
</script>
<style lang="css" scoped>
.container {
  min-height: calc(100vh-44px);
}
</style>
