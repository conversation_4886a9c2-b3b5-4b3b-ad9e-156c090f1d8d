<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '练习结果',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <sxt-container>
    <view class="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-32rpx">
      <!-- 结果卡片 -->
      <view class="w-full mb-200rpx max-w-600rpx bg-gray-200 rounded-16rpx p-48rpx mb-80rpx">
        <view class="flex items-center justify-between mb-32rpx">
          <view class="text-32rpx font-600 text-gray-800">学生姓名</view>
          <view class="text-28rpx text-gray-600">
            {{ practiseResult?.studentName || '--' }}
          </view>
        </view>
        <!-- 抗遗忘状态 -->
        <view v-if="type !== 'antiforget'" class="flex items-center justify-between mb-32rpx">
          <view class="text-32rpx font-600 text-gray-800">抗遗忘</view>
          <view class="text-28rpx text-gray-600">
            {{ practiseResult?.isDoneAntiForget === '1' ? '已做' : '未做' }}
          </view>
        </view>

        <!-- 新学习单词结构 -->
        <view v-if="type !== 'antiforget'" class="flex items-center justify-between mb-32rpx">
          <view class="text-32rpx font-600 text-gray-800">单词</view>
          <view class="text-28rpx text-gray-600">
            {{ practiseResult?.newWordsCount ?? '--' }}个
          </view>
        </view>
        <!-- 抗遗忘结构 -->
        <view v-else class="flex items-center justify-between mb-32rpx">
          <view class="text-32rpx font-600 text-gray-800">抗遗忘单词</view>
          <view class="text-28rpx text-gray-600">{{ practiseResult?.wordCount ?? '--' }}个</view>
        </view>
        <view v-if="type === 'antiforget'" class="flex items-center justify-between mb-32rpx">
          <view class="text-32rpx font-600 text-gray-800">正确单词</view>
          <view class="text-28rpx text-gray-600">{{ practiseResult?.correctCount ?? '--' }}个</view>
        </view>
        <view v-if="type === 'antiforget'" class="flex items-center justify-between mb-32rpx">
          <view class="text-32rpx font-600 text-gray-800">错误单词</view>
          <view class="text-28rpx text-gray-600">{{ practiseResult?.errorCount ?? '--' }}个</view>
        </view>

        <!-- 训练时间 -->
        <view class="flex items-center justify-between">
          <view class="text-32rpx font-600 text-gray-800">训练时间：</view>
          <view class="text-28rpx text-gray-600">
            {{ `从${practiseResult.startTime}到${practiseResult.endTime}` }}
          </view>
        </view>
      </view>

      <!-- 布置课后练习按钮 -->
      <!-- <view class="w-full max-w-400rpx">
        <wd-button
          type="primary"
          size="large"
          @click="handleAssignHomework"
          custom-class="w-full h-96rpx rounded-16rpx text-32rpx font-600 bg-blue-500"
        >
          布置课后练习
        </wd-button>
      </view> -->

      <!-- 其他操作按钮 -->
      <view class="w-full max-w-400rpx mt-602rpx flex gap-24rpx">
        <wd-button
          type="text"
          size="large"
          @click="navigateToHome"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx"
        >
          返回首页
        </wd-button>

        <wd-button
          type="text"
          size="large"
          @click="navigateToPractise"
          custom-class="flex-1 h-88rpx rounded-16rpx text-28rpx border-gray-300"
        >
          布置课后练习
        </wd-button>
      </view>
    </view>
  </sxt-container>
</template>

<script setup lang="ts">
import { classpractisStatisticsUsingPost, StudentPractiseStatisticsRespVO } from '@/service/student'
import {
  attendclassAntiforgetStatisticsUsingPost,
  AttendClassPlanStatisticsRespVO,
  attendclassStatisticsUsingPost,
} from '@/service/teacher'
import { navigateTo, navigateToSub, showToast } from '@/utils'
import dayjs from 'dayjs'
import { ref, onMounted, computed } from 'vue'

defineOptions({
  name: 'PractiseResult',
})

const attendClassPlanId = ref(0)
const practiseResult = ref<AttendClassPlanStatisticsRespVO>({})
const type = ref('')
// 格式化训练时间

// 布置课后练习
const handleAssignHomework = () => {
  showToast('布置课后练习功能开发中')
  // 这里可以跳转到布置作业页面
  // navigateToSub('/homework/homework')
}

onLoad((options: any) => {
  if (options?.attendClassPlanId) {
    attendClassPlanId.value = options?.attendClassPlanId
  }
  if (options?.type) {
    type.value = options?.type
    if (options?.type === 'antiforget') {
      fetchAntiforgetDetail()
    } else {
      fetchPractiseDetail()
    }
  }
})
const formatTrainingTime = (startTime: string, endTime: string) => {
  console.log(startTime, endTime)
  let startFormatted
  let endFormatted
  if (startTime === null) {
    startFormatted = '--'
  }
  if (endTime === null) {
    endFormatted = '--'
  } else {
    startFormatted = dayjs(startTime).format('M-D H:m')
    endFormatted = dayjs(endTime).format('M-D H:m')
  }
  // 格式化为 "M-D H:m" (月-日 时:分，不带前导零)

  return `从${startFormatted}到${endFormatted}`
}
// 新学习的单词学习结果查询
const fetchPractiseDetail = async () => {
  try {
    const res = await attendclassStatisticsUsingPost({
      body: {
        id: attendClassPlanId.value,
      },
    })
    if (res.code === 200) {
      practiseResult.value = res?.data?.item
      if (res?.data?.item.startTime === null) {
        practiseResult.value.startTime = '--'
      } else {
        practiseResult.value.startTime = dayjs(practiseResult.value?.startTime).format('M-D H:m')
      }
      if (res?.data?.item.endTime === null) {
        practiseResult.value.endTime = '--'
      } else {
        practiseResult.value.endTime = dayjs(practiseResult.value?.endTime).format('M-D H:m')
      }
    }
  } catch (error) {
    console.error('获取练习详情失败:', error)
  }
}
// 抗遗忘单词结果查询
const fetchAntiforgetDetail = async () => {
  try {
    const res = await attendclassAntiforgetStatisticsUsingPost({
      body: {
        id: attendClassPlanId.value,
      },
    })
    if (res.code === 200) {
      practiseResult.value = res?.data?.item
      if (res?.data?.item.startTime === null) {
        practiseResult.value.startTime = '--'
      } else {
        practiseResult.value.startTime = dayjs(practiseResult.value?.startTime).format('M-D H:m')
      }
      if (res?.data?.item.endTime === null) {
        practiseResult.value.endTime = '--'
      } else {
        practiseResult.value.endTime = dayjs(practiseResult.value?.endTime).format('M-D H:m')
      }
    }
  } catch (error) {
    console.error('获取练习详情失败:', error)
  }
}
const navigateToPractise = async () => {
  navigateToSub(`/self-study/self-study?classId=${attendClassPlanId.value}`)
}

const navigateToHome = async () => {
  navigateTo('/index/index')
}

// onMounted(() => {
//   fetchPractiseDetail()
// })
</script>
