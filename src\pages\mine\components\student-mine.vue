<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 用户信息区域 -->
    <view class="bg-white px-32rpx py-48rpx">
      <view class="flex items-center">
        <!-- 头像 -->
        <view class="w-120rpx h-120rpx mr-32rpx">
          <image
            :src="userInfo.avatar || '/static/images/default-avatar.png'"
            class="w-full h-full rounded-full object-cover"
            mode="aspectFill"
          />
        </view>
        <!-- 用户名 -->
        <view class="text-36rpx font-600 text-gray-800">
          {{ userInfo.name || '李木子' }}
        </view>
      </view>
    </view>

    <!-- 数据统计区域 -->
    <view class="bg-white mx-32rpx rounded-16rpx p-32rpx mb-32rpx mt-4">
      <view class="flex justify-between">
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.courseHours || 100 }}
          </view>
          <view class="text-24rpx text-gray-500">累计时长</view>
        </view>
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.totalHours || 100 }}
          </view>
          <view class="text-24rpx text-gray-500">总时长</view>
        </view>
        <view class="text-center">
          <view class="text-48rpx font-600 text-gray-800 mb-8rpx">
            {{ stats.completedCourses || 100 }}
          </view>
          <view class="text-24rpx text-gray-500">已上课</view>
        </view>
      </view>
    </view>

    <!-- 学时分钟数充值 -->
    <view class="mx-32rpx mb-32rpx">
      <view
        class="bg-blue-500 rounded-16rpx p-32rpx flex items-center justify-between cursor-pointer active:scale-98 transition-all duration-300"
        @click="handleToStudentRecharge"
      >
        <view class="text-white text-32rpx font-600">学时分钟数充值</view>
        <view class="text-white text-24rpx">></view>
      </view>
    </view>

    <!-- 自学VIP -->
    <view class="mx-32rpx mb-32rpx">
      <view class="bg-gray-800 rounded-16rpx p-32rpx flex items-center justify-between">
        <view class="text-white text-32rpx font-600">自学VIP</view>
        <view
          class="bg-yellow-400 text-gray-800 text-24rpx px-24rpx py-8rpx rounded-full cursor-pointer"
          @click="handleUpgrade"
        >
          去开通
        </view>
      </view>
    </view>

    <!-- 功能菜单列表 -->
    <view class="bg-white mx-32rpx rounded-16rpx overflow-hidden">
      <!-- 我的课时 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyCourses"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-blue-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">📚</view>
          </view>
          <view class="text-32rpx text-gray-800">我的课时</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的投诉 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyComplaints"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-orange-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">💬</view>
          </view>
          <view class="text-32rpx text-gray-800">我的投诉</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的评价 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyEvaluations"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-green-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">⭐</view>
          </view>
          <view class="text-32rpx text-gray-800">我的评价</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的测测 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyTests"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-purple-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">🧪</view>
          </view>
          <view class="text-32rpx text-gray-800">我的测测</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的测试 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyExams"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-red-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">📝</view>
          </view>
          <view class="text-32rpx text-gray-800">我的测试</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的练习记录 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyPracticeRecords"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-indigo-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">⏰</view>
          </view>
          <view class="text-32rpx text-gray-800">我的练习记录</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 我的推荐码 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleMyReferralCode"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-cyan-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">📋</view>
          </view>
          <view class="text-32rpx text-gray-800">我的推荐码</view>
        </view>
        <view class="flex items-center">
          <view class="text-blue-500 text-28rpx mr-16rpx">
            {{ userInfo.referralCode || '*********' }}
          </view>
          <view class="text-gray-400">></view>
        </view>
      </view>

      <!-- 修改密码 -->
      <view
        class="flex items-center justify-between px-32rpx py-32rpx border-b border-gray-100 cursor-pointer active:bg-gray-50"
        @click="handleChangePassword"
      >
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-yellow-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">🔒</view>
          </view>
          <view class="text-32rpx text-gray-800">修改密码</view>
        </view>
        <view class="text-gray-400">></view>
      </view>

      <!-- 当前版本 -->
      <view class="flex items-center justify-between px-32rpx py-32rpx">
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-gray-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">ℹ️</view>
          </view>
          <view class="text-32rpx text-gray-800">当前版本</view>
        </view>
        <view class="text-gray-400">></view>
      </view>
      <view @click="handleLogout" class="flex items-center justify-between px-32rpx py-32rpx">
        <view class="flex items-center">
          <view
            class="w-48rpx h-48rpx bg-gray-100 rounded-12rpx flex items-center justify-center mr-24rpx"
          >
            <view class="text-24rpx">ℹ️</view>
          </view>
          <view class="text-32rpx text-gray-800">退出登录</view>
        </view>
        <view class="text-gray-400">></view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="h-120rpx"></view>

    <!-- 底部导航栏 -->
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { checkAuth } from '@/utils/check-auth'
import { navigateToSub, showToast } from '@/utils'
import { useRoleStore } from '@/store'

const { getRole } = useRoleStore()
defineOptions({
  name: 'Mine',
})

// 用户信息
const userInfo = ref({
  name: '李木子',
  avatar: '/static/images/default-avatar.png',
  referralCode: '*********',
})

// 统计数据
const stats = ref({
  courseHours: 100,
  totalHours: 100,
  completedCourses: 100,
})

// 用户角色
const userRole = ref<string | null>(null)
const handleLogout = () => {
  uni.setStorageSync('token', '')
  navigateToSub('/role-select/role-select?to=login')
}
// 获取用户角色
const getUserRole = () => {
  try {
    userRole.value = getRole()
  } catch (e) {
    userRole.value = null
  }
}

// 跳转到登录页面
const goToLogin = () => {
  navigateToSub('/login/login')
}

// 处理充值
const handleToStudentRecharge = () => {
  showToast('跳转到充值页面')
  // navigateToSub('/student-recharge/student-recharge')
  navigateToSub('/recharge/recharge')
}

// 处理VIP开通
const handleUpgrade = () => {
  showToast('跳转到VIP开通页面')
  // navigateToSub('/vip/vip')
}

// 处理我的课时
const handleMyCourses = () => {
  showToast('跳转到我的课时页面')
  // navigateToSub('/my-courses/my-courses')
}

// 处理我的投诉
const handleMyComplaints = () => {
  showToast('跳转到我的投诉页面')
  // navigateToSub('/my-complaints/my-complaints')
}

// 处理我的评价
const handleMyEvaluations = () => {
  showToast('跳转到我的评价页面')
  // navigateToSub('/my-evaluations/my-evaluations')
}

// 处理我的测测
const handleMyTests = () => {
  showToast('跳转到我的测测页面')
  // navigateToSub('/my-tests/my-tests')
}

// 处理我的测试
const handleMyExams = () => {
  showToast('跳转到我的测试页面')
  // navigateToSub('/my-exams/my-exams')
}

// 处理我的练习记录
const handleMyPracticeRecords = () => {
  showToast('跳转到我的练习记录页面')
  // navigateToSub('/my-practice-records/my-practice-records')
}

// 处理我的推荐码
const handleMyReferralCode = () => {
  showToast('跳转到我的推荐码页面')
  // navigateToSub('/my-referral-code/my-referral-code')
}

// 处理修改密码
const handleChangePassword = () => {
  showToast('跳转到修改密码页面')
  // navigateToSub('/change-password/change-password')
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 这里可以调用API获取用户信息
    // 暂时使用模拟数据
    userInfo.value = {
      name: '李木子',
      avatar: '/static/images/default-avatar.png',
      referralCode: '*********',
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里可以调用API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      courseHours: 100,
      totalHours: 100,
      completedCourses: 100,
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(() => {
  getUserRole()
  fetchUserInfo()
  fetchStats()
})

onShow(() => {
  getUserRole()
})

onLoad(() => {
  if (!checkAuth()) {
    // 未登录，跳转到登录页面
    goToLogin()
  } else {
    // 已登录，获取用户角色
    getUserRole()
  }
})
</script>

<style>
.main-title-color {
  color: #d14328;
}
</style>
