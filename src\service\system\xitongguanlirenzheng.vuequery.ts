/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './xitongguanlirenzheng';
import * as API from './types';

/** 使用账号密码登录 POST /pc/system/auth/login */
export function usePcSystemAuthLoginUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAuthLoginRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemAuthLoginUsingPost,
    onSuccess(data: API.ResultAuthLoginRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 登出系统 POST /pc/system/auth/logout */
export function usePcSystemAuthLogoutUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemAuthLogoutUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 刷新令牌 POST /pc/system/auth/refresh-token */
export function usePcSystemAuthRefreshTokenUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAuthLoginRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemAuthRefreshTokenUsingPost,
    onSuccess(data: API.ResultAuthLoginRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 此处后端没有提供注释 POST /pc/system/auth/test */
export function usePcSystemAuthTestUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultObject) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemAuthTestUsingPost,
    onSuccess(data: API.ResultObject) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 权限测试 POST /pc/system/auth/testAuthor */
export function usePcSystemAuthTestAuthorUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultObject) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemAuthTestAuthorUsingPost,
    onSuccess(data: API.ResultObject) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
