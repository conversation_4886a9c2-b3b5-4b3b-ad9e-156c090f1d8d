<route lang="json5">
{
  layout: 'common',
  style: {
    navigationBarTitleText: '老师上课',
  },
}
</route>

<template>
  <view class="bg-[#F5F5F5]">
    <!-- 课程信息卡片 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">课程信息</view>
      <view v-if="currentClass">
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">课程类型</view>
          <view class="text-[28rpx] font-medium">
            {{ dict?.teacher_course_duration?.[currentClass.courseDuration]?.text || '未知类型' }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">学生姓名</view>
          <view class="text-[28rpx] font-medium">
            {{ currentClass.studentName || '未知学生' }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">上课时间</view>
          <view class="text-[28rpx] font-medium">
            {{ currentClass.startTime }} - {{ currentClass.endTime }}
          </view>
        </view>
        <view class="flex justify-between items-center mb-[16rpx]">
          <view class="text-[28rpx] text-[#666]">课程状态</view>
          <view class="text-[28rpx] font-medium">
            {{ dict?.class_progress?.[currentClass.progress]?.text || '未知状态' }}
          </view>
        </view>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无课程信息</view>
    </view>

    <!-- 操作按钮 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[24rpx]">课堂操作</view>
      <view class="flex justify-between">
        <wd-button type="primary" custom-class="w-[300rpx]!" @click="handleStartClass">
          开始上课
        </wd-button>
        <wd-button type="error" custom-class="w-[300rpx]!" @click="handleEndClass">
          结束上课
        </wd-button>
      </view>
    </view>

    <!-- 会议设置 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">会议设置</view>
      <view class="mb-[16rpx]">
        <wd-input
          custom-input-class="h-[80rpx]! pl-[24rpx]! text-[28rpx]!"
          type="text"
          v-model="meetingNo"
          placeholder="请输入腾讯会议号"
        />
      </view>
      <view class="flex justify-between">
        <wd-button
          type="primary"
          custom-class="w-[300rpx]!"
          :disabled="!!meetingNo"
          @click="handleSetMeetingNo"
        >
          设置会议号
        </wd-button>
        <wd-button
          type="primary"
          plain
          custom-class="w-[300rpx]!"
          :disabled="!meetingNo"
          @click="joinMeeting"
        >
          加入会议
        </wd-button>
      </view>
    </view>

    <!-- 课程资料 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">课程资料</view>
      <view v-if="currentClass && currentClass.coursewareId">
        <wd-button type="primary" plain block @click="viewCourseware(currentClass.coursewareId)">
          查看课程资料
        </wd-button>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无课程资料</view>
    </view>

    <!-- 单词学习进度 -->
    <view class="bg-white p-[32rpx] mb-[20rpx]">
      <view class="text-[36rpx] font-bold mb-[16rpx]">单词学习进度</view>
      <view v-if="currentClass && currentClass.latestWordId">
        <view class="text-[28rpx] mb-[16rpx]">当前学习单词ID: {{ currentClass.latestWordId }}</view>
        <wd-button type="primary" plain block @click="updateWordProgress">更新学习进度</wd-button>
      </view>
      <view v-else class="text-center py-[40rpx] text-[28rpx] text-[#999]">暂无单词学习记录</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { showToast, navigateBack, navigateToCoursewareDetail } from '@/utils'
import {
  attendclassStartClassPlanUsingPost,
  attendclassEndClassPlanUsingPost,
  attendclassSetTencentMeetingNoUsingPost,
  attendclassUpdateWordProssUsingPost,
  attendclassGetUsingPost,
} from '@/service/teacher/appTeacherAttendClassPlanManagement'
import { useDictStore } from '@/store'
import { AttendClassPlanDetialRespVO } from '@/service/teacher'

// 页面参数
const classId = ref<number | null>(null)
const currentClass = ref<AttendClassPlanDetialRespVO | null>({ progress: 0 })
const dict = ref<any>(null)
const meetingNo = ref('')
// 加入会议
const joinMeeting = () => {
  if (!currentClass.value?.tencentMeetingNo) {
    showToast('请先设置会议号')
    return
  }
  showToast('正在打开腾讯会议...')
  // 这里可以添加打开腾讯会议的逻辑
}

// 查看课程资料
const viewCourseware = async (coursewareId: number) => {
  if (!coursewareId) {
    showToast('无法获取课程资料ID')
    return
  }
  navigateToCoursewareDetail(coursewareId, classId.value || undefined)
}

// 设置会议号
const handleSetMeetingNo = async () => {
  if (!currentClass.value?.id || !meetingNo.value) {
    showToast('请输入会议号')
    return
  }

  try {
    const res = await attendclassSetTencentMeetingNoUsingPost({
      body: {
        id: currentClass.value.id,
        tencentMeetingNo: meetingNo.value,
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('会议号设置成功')
      // 更新当前课程的会议号
      if (currentClass.value) {
        currentClass.value.tencentMeetingNo = meetingNo.value
      }
    } else {
      showToast(res.message || '会议号设置失败')
    }
  } catch (error) {
    console.error('会议号设置失败:', error)
    showToast('会议号设置失败，请稍后重试')
  }
}

// 更新单词进度
const updateWordProgress = async () => {
  if (
    !currentClass.value?.id ||
    !currentClass.value?.latestWordId ||
    !currentClass.value?.coursewareId
  ) {
    showToast('无法获取必要的课程信息')
    return
  }

  try {
    const res = await attendclassUpdateWordProssUsingPost({
      body: {
        attendClassPlanId: currentClass.value.id,
        coursewareId: currentClass.value.coursewareId,
        wordId: currentClass.value.latestWordId,
        wordContent: '单词内容', // 这里需要传入实际的单词内容
        isCorrect: 1, // 假设正确
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('单词进度更新成功')
    } else {
      showToast(res.message || '单词进度更新失败')
    }
  } catch (error) {
    console.error('单词进度更新失败:', error)
    showToast('单词进度更新失败，请稍后重试')
  }
}

// 开始上课
const handleStartClass = async () => {
  if (!currentClass.value?.id) {
    showToast('无法获取课程ID')
    return
  }

  try {
    const res = await attendclassStartClassPlanUsingPost({
      body: {
        id: currentClass.value.id,
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('上课开始成功')
      fetchClassInfo()
    } else {
      showToast(res.message || '开始上课失败')
    }
  } catch (error) {
    showToast('开始上课失败，请稍后重试')
  }
}

// 结束上课
const handleEndClass = async () => {
  if (!currentClass.value?.id) {
    showToast('无法获取课程ID')
    return
  }

  try {
    const res = await attendclassEndClassPlanUsingPost({
      body: {
        id: currentClass.value.id,
      },
    })

    if (res.code === 200 && res.data?.item) {
      showToast('下课成功')
      fetchClassInfo()
    } else {
      showToast(res.message || '结束上课失败')
    }
  } catch (error) {
    console.error('结束上课失败:', error)
    showToast('结束上课失败，请稍后重试')
  }
}

// 获取课程信息
const fetchClassInfo = async () => {
  try {
    // 获取字典数据
    const dictStore = useDictStore()
    dict.value = dictStore.dictData
    console.log(dict, 123456)
    if (classId.value) {
      // 如果有传入ID，则直接获取课程详情
      const detailRes = await attendclassGetUsingPost({
        body: {
          id: classId.value,
        },
      })
      if (detailRes.code === 200 && detailRes.data?.item) {
        // 构造符合ClassInfo类型的对象
        currentClass.value = detailRes.data.item
        meetingNo.value = currentClass.value.tencentMeetingNo
      } else {
        showToast('获取课程详情失败')
      }
    }
  } catch (error) {
    console.error('获取课程信息失败:', error)
    showToast('获取课程信息失败')
  }
}

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    classId.value = Number(options.id)
  }
})

onMounted(() => {
  fetchClassInfo()
})
</script>
