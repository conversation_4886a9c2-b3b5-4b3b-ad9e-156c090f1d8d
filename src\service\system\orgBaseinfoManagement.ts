/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】添加推广机构信息 POST /pc/orgBaseinfo/create */
export async function pcOrgBaseinfoCreateUsingPost({
  body,
  options,
}: {
  body: API.SysOrgCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultString>('/pc/orgBaseinfo/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【推广机构】获取当前登录推广机构详情 POST /pc/orgBaseinfo/current */
export async function pcOrgBaseinfoCurrentUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSysOrgDetialRespVO>('/pc/orgBaseinfo/current', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【运营】获取推广机构详情 POST /pc/orgBaseinfo/get */
export async function pcOrgBaseinfoGetUsingPost({
  body,
  options,
}: {
  body: API.SysOrgGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSysOrgDetialRespVO>('/pc/orgBaseinfo/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】修改推广机构详情 POST /pc/orgBaseinfo/modify */
export async function pcOrgBaseinfoModifyUsingPost({
  body,
  options,
}: {
  body: API.SysOrgModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/orgBaseinfo/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】分页查询推广机构 POST /pc/orgBaseinfo/page */
export async function pcOrgBaseinfoPageUsingPost({
  body,
  options,
}: {
  body: API.SysOrgPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSysOrgDetialRespVO>(
    '/pc/orgBaseinfo/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
