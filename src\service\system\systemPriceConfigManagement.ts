/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】上架课程价格信息 POST /pc/system/priceConfig/active */
export async function pcSystemPriceConfigActiveUsingPost({
  body,
  options,
}: {
  body: API.SystemPriceConfigActiveReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/priceConfig/active', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营/学生】获取课程总价 POST /pc/system/priceConfig/calcTotalPrice */
export async function pcSystemPriceConfigCalcTotalPriceUsingPost({
  body,
  options,
}: {
  body: API.SystemCaclTotalPriceReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListInteger>(
    '/pc/system/priceConfig/calcTotalPrice',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】新增价格配置 POST /pc/system/priceConfig/create */
export async function pcSystemPriceConfigCreateUsingPost({
  body,
  options,
}: {
  body: API.SystemPriceConfigCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/system/priceConfig/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营/学生】查询课时价格配置列表信息 POST /pc/system/priceConfig/list */
export async function pcSystemPriceConfigListUsingPost({
  body,
  options,
}: {
  body: API.SystemPriceConfigGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListSysPriceConfigDO>(
    '/pc/system/priceConfig/list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】分页查询课时价格配置信息 POST /pc/system/priceConfig/page */
export async function pcSystemPriceConfigPageUsingPost({
  body,
  options,
}: {
  body: API.SystemPriceConfigPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSysPriceConfigDO>(
    '/pc/system/priceConfig/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
