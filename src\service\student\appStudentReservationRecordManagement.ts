/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生app】去约课 POST /app/student/reservation/create */
export async function reservationCreateUsingPost({
  body,
  options,
}: {
  body: API.StudentCourseReservationCreateV2ReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/app/student/reservation/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生app】获取指定id的约课记录明细信息 POST /app/student/reservation/get */
export async function reservationGetUsingPost({
  body,
  options,
}: {
  body: API.ReservationGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCourseReservationDetialRespVO>(
    '/app/student/reservation/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生app】分页查询约课记录信息 POST /app/student/reservation/page */
export async function reservationPageUsingPost({
  body,
  options,
}: {
  body: API.StudentCourseReservationPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCourseReservationDetialRespVO>(
    '/app/student/reservation/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
