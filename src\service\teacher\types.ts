/* eslint-disable */
// @ts-ignore

export type AppUserModifyReqVO = {
  /** 老师姓名 */
  name: string;
  /** 老师考核状态（0=待考核，1=已通过，2=未通过） */
  examineSuccess?: number;
  /** 身份证号 */
  idNumber: string;
  /** 性别（1=男，2=女） */
  sex: number;
  /** 紧急联系人 */
  emergencyContact: string;
  /** 紧急联系电话 */
  emergencyContactPhone: string;
  /** 微信账号 */
  wechat?: string;
  /** 所在学校 */
  school: string;
  /** 个人简介 */
  introduction: string;
  /** 手机号码 */
  mobile: string;
};

export type AttendClassAntiforgetWordDetailUpdateReqVO = {
  /** 上课计划id */
  attendClassPlanId: number;
  /** 单词所属的课件资料id */
  coursewareId: number;
  /** 单词id */
  wordId: number;
  /** 单词内容 */
  wordContent: string;
  /** 是否正确：0-错误，1-正确 */
  isCorrect: number;
};

export type AttendClassPlanAntiforgetStatisticsRespVO = {
  /** 上课计划id */
  id?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师id */
  teacherId?: number;
  /** 训练名称、课件资料名称 */
  coursewareName?: string;
  /** 复习抗遗忘单词总数 */
  wordCount?: number;
  /** 复习抗遗忘单词正确数 */
  correctCount?: number;
  /** 复习抗遗忘单词错误数 */
  errorCount?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
};

export type AttendClassPlanCoursewareIdSetReqVO = {
  /** 上课计划id */
  id: number;
  /** 资料id */
  coursewareId: number;
};

export type AttendClassPlanDetialListRespVO = {
  /** 上课日期 */
  classDate?: string;
  /** 上课计划详情列表 */
  classPlanDetialList?: AttendClassPlanDetialRespVO[];
};

export type AttendClassPlanDetialRespVO = {
  /** 上课计划id */
  id?: number;
  /** 课程类型 30分钟正课：0，60分钟正课：1，体验课：2 */
  courseDuration?: string;
  /** 课程名称 */
  coursewareName?: string;
  /** 老师名称 */
  teacherName?: string;
  /** 学生名称 */
  studentName?: string;
  /** 上课日期 */
  classDate?: string;
  startTime?: LocalTime;
  endTime?: LocalTime;
  /** 实际开始时间 */
  actualStartTime?: string;
  /** 实际结束时间 */
  actualEndTime?: string;
  /** 上课状态：0-未开始，1-进行中，2-已结束，3-已取消 */
  progress?: string;
  /** 腾讯会议号1 */
  tencentMeetingNo?: string;
  /** 腾讯会议号2 */
  tencentMeetingNoTwo?: string;
  /** 资料id */
  coursewareId?: number;
  /** 最新单词id */
  latestWordId?: number;
  /** 评价状态 0 未评价 1 已评价 */
  evaluateStatus?: string;
  /** 老师id */
  teacherId?: number;
  /** 是否要做抗遗忘： 0 不做 1 做 */
  isToDoAntiForget?: string;
  /** 是否已经做了抗遗忘： 0 未做 1 已做 */
  isDoneAntiForget?: string;
};

export type AttendClassPlanGetReqVO = {
  /** 上课计划id */
  id: number;
};

export type AttendClassPlanStartOrEndReqVO = {
  /** 上课计划id */
  id: number;
};

export type AttendClassPlanStatisticsRespVO = {
  /** 上课计划id */
  id?: number;
  /** 学生姓名 */
  studentName?: string;
  /** 老师姓名 */
  teacherName?: string;
  /** 老师id */
  teacherId?: number;
  /** 学新单词总数 */
  newWordsCount?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 是否要做抗遗忘： 0 不做 1 做 */
  isToDoAntiForget?: string;
  /** 是否已经做了抗遗忘： 0 未做 1 已做 */
  isDoneAntiForget?: string;
};

export type AttendClassPlanTencentMeetingNoSetReqVO = {
  /** 上课计划id */
  id: number;
  /** 腾讯会议号1 */
  tencentMeetingNo: string;
  /** 腾讯会议号2 */
  tencentMeetingNoTwo?: string;
};

export type AttendClassWordDetailUpdateReqVO = {
  /** 上课计划id */
  attendClassPlanId: number;
  /** 单词所属的课件资料id */
  coursewareId: number;
  /** 单词id */
  wordId: number;
  /** 单词内容 */
  wordContent: string;
  /** 是否正确：0-错误，1-正确 */
  isCorrect: number;
};

export type AttendClassWordListNeedAntiforgetPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 上课计划id */
  attendClassPlanId?: number;
};

export type AttendClassWordListNeedLearnPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 上课计划id */
  attendClassPlanId?: number;
};

export type AttendClassWordListSelectReqVO = {
  /** 上课计划id */
  attendClassPlanId: number;
  /** 需学习单词id集合 */
  needLearnWordIds?: number[];
  /** 无需学习单词id集合 */
  notNeedLearnWordIds?: number[];
};

export type CourseCyclePlanCreateReqVO = {
  /** 课程计划名称 */
  name?: string;
  /** 时长类型: 0-30分钟正课, 1-60分钟正课, 2-体验课 */
  courseDuration: number;
  /** 周一是否有空：0 - 没空，1 - 有空 */
  monday?: number;
  /** 周二是否有空：0 - 没空，1 - 有空 */
  tuesday?: number;
  /** 周三是否有空：0 - 没空，1 - 有空 */
  wednesday?: number;
  /** 周四是否有空：0 - 没空，1 - 有空 */
  thursday?: number;
  /** 周五是否有空：0 - 没空，1 - 有空 */
  friday?: number;
  /** 周六是否有空：0 - 没空，1 - 有空 */
  saturday?: number;
  /** 周日是否有空：0 - 没空，1 - 有空 */
  sunday?: number;
  /** 备注 */
  remark?: string;
};

export type CourseCyclePlanDetialRespVO = {
  id?: number;
  /** 课程计划名称 */
  name?: string;
  /** 老师名称 */
  teacherName?: string;
  /** 课程类型: 0-30分钟正课, 1-60分钟正课, 2-体验课, 3-不限 */
  courseDuration?: number;
  /** 是否全天: 0-非全天, 1-全天 */
  isAllDay?: number;
  /** 周一是否有空：0 - 没空，1 - 有空 */
  monday?: number;
  /** 周二是否有空：0 - 没空，1 - 有空 */
  tuesday?: number;
  /** 周三是否有空：0 - 没空，1 - 有空 */
  wednesday?: number;
  /** 周四是否有空：0 - 没空，1 - 有空 */
  thursday?: number;
  /** 周五是否有空：0 - 没空，1 - 有空 */
  friday?: number;
  /** 周六是否有空：0 - 没空，1 - 有空 */
  saturday?: number;
  /** 周日是否有空：0 - 没空，1 - 有空 */
  sunday?: number;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建人 */
  creator?: string;
  /** 更新人 */
  updater?: string;
};

export type CourseCyclePlanUpdateReqVO = {
  /** 课程计划名称 */
  name?: string;
  /** 时长类型: 0-30分钟正课, 1-60分钟正课, 2-体验课, 3-不限 */
  courseDuration: number;
  /** 是否全天: 0-非全天, 1-全天 */
  isAllDay?: number;
  /** 备注 */
  remark?: string;
  /** 周一是否有空：0 - 没空，1 - 有空 */
  monday?: number;
  /** 周二是否有空：0 - 没空，1 - 有空 */
  tuesday?: number;
  /** 周三是否有空：0 - 没空，1 - 有空 */
  wednesday?: number;
  /** 周四是否有空：0 - 没空，1 - 有空 */
  thursday?: number;
  /** 周五是否有空：0 - 没空，1 - 有空 */
  friday?: number;
  /** 周六是否有空：0 - 没空，1 - 有空 */
  saturday?: number;
  /** 周日是否有空：0 - 没空，1 - 有空 */
  sunday?: number;
  /** 课程计划id */
  id: number;
};

export type CoursewareByAdminPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 资料名称 */
  name?: string;
  /** 课件资料年级 */
  courseGrade?: string;
};

export type CoursewareByAdminPageRespVO = {
  /** 课件资料ID */
  id?: number;
  /** 资料名称 */
  name?: string;
  /** 课件资料年级 */
  courseGrade?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后更新时间 */
  updateTime?: string;
  /** 创建人 */
  creator?: string;
  /** 更新人 */
  updater?: string;
};

export type CoursewareDetialGetReqVO = {
  /** 资料id */
  coursewareId: number;
};

export type CoursewareWordPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 上课计划id */
  classPlanId?: number;
  /** 单词查询类型：0.抗遗忘复习 1.学习新单词 */
  wordQueryType?: string;
};

export type CoursewareWordPageRespVO = {
  /** 单词id */
  id?: number;
  /** 单词 */
  word?: string;
  /** 中文释义 */
  chinese?: string;
  /** 音频地址 */
  mp3?: string;
  /** 是否需要学习：0 不需要 ，1 需要 */
  isNeedLearn?: string;
  /** 是否已学习：0 否 ，1 是 */
  isCompletedLearn?: string;
  /** 是否已完成抗遗忘学习：0 否 ，1 是 */
  isCompletedAntiforget?: string;
};

export type CyclePlanRemoveReqVO = {
  /** 课程计划id */
  id: number;
};

export type DataWrapperAttendClassPlanAntiforgetStatisticsRespVO = {
  item?: AttendClassPlanAntiforgetStatisticsRespVO;
  items?: AttendClassPlanAntiforgetStatisticsRespVO[];
};

export type DataWrapperAttendClassPlanDetialRespVO = {
  item?: AttendClassPlanDetialRespVO;
  items?: AttendClassPlanDetialRespVO[];
};

export type DataWrapperAttendClassPlanStatisticsRespVO = {
  item?: AttendClassPlanStatisticsRespVO;
  items?: AttendClassPlanStatisticsRespVO[];
};

export type DataWrapperBoolean = {
  item?: boolean;
  items?: boolean[];
};

export type DataWrapperCourseCyclePlanDetialRespVO = {
  item?: CourseCyclePlanDetialRespVO;
  items?: CourseCyclePlanDetialRespVO[];
};

export type DataWrapperCoursewareByAdminPageRespVO = {
  item?: CoursewareByAdminPageRespVO;
  items?: CoursewareByAdminPageRespVO[];
};

export type DataWrapperHomePageStatisticsRespVO = {
  item?: HomePageStatisticsRespVO;
  items?: HomePageStatisticsRespVO[];
};

export type DataWrapperListAttendClassPlanDetialListRespVO = {
  item?: AttendClassPlanDetialListRespVO[];
  items?: AttendClassPlanDetialListRespVO[][];
};

export type DataWrapperListAttendClassPlanDetialRespVO = {
  item?: AttendClassPlanDetialRespVO[];
  items?: AttendClassPlanDetialRespVO[][];
};

export type DataWrapperListString = {
  item?: string[];
  items?: string[][];
};

export type DataWrapperLong = {
  item?: number;
  items?: number[];
};

export type DataWrapperTeacherAndStudentRespVO = {
  item?: TeacherAndStudentRespVO;
  items?: TeacherAndStudentRespVO[];
};

export type DataWrapperTeacherWalletInfoRespVo = {
  item?: TeacherWalletInfoRespVo;
  items?: TeacherWalletInfoRespVo[];
};

export type HomePageStatisticsRespVO = {
  /** 学生新增数量 */
  studentAddNumber?: number;
  /** 今日上课数量 */
  todayClassNumber?: number;
};

export type LocalTime = {
  hour?: number;
  minute?: number;
  second?: number;
  nano?: number;
};

export type PageDto = {
  /** 目标页码 */
  pageNo: number;
  /** 每页数据量 */
  pageSize: number;
};

export type PageResultCoursewareByAdminPageRespVO = {
  /** 数据列表 */
  items?: CoursewareByAdminPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultCoursewareWordPageRespVO = {
  /** 数据列表 */
  items?: CoursewareWordPageRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultResponsePageResultCoursewareByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCoursewareByAdminPageRespVO;
};

export type PageResultResponsePageResultCoursewareWordPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultCoursewareWordPageRespVO;
};

export type PageResultResponsePageResultStudentPractiseBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStudentPractiseBaseRespVO;
};

export type PageResultResponsePageResultStudentPractiseDetailBaseRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStudentPractiseDetailBaseRespVO;
};

export type PageResultResponsePageResultStuWalletDetailsRespVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: PageResultStuWalletDetailsRespVo;
};

export type PageResultStudentPractiseBaseRespVO = {
  /** 数据列表 */
  items?: StudentPractiseBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStudentPractiseDetailBaseRespVO = {
  /** 数据列表 */
  items?: StudentPractiseDetailBaseRespVO[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type PageResultStuWalletDetailsRespVo = {
  /** 数据列表 */
  items?: StuWalletDetailsRespVo[];
  /** 总记录数 */
  counts?: number;
  /** 当前页码 */
  pageCurrent?: number;
  /** 每页记录数 */
  pageSize?: number;
};

export type ResultAttendClassPlanAntiforgetStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanAntiforgetStatisticsRespVO;
};

export type ResultAttendClassPlanDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanDetialRespVO;
};

export type ResultAttendClassPlanStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperAttendClassPlanStatisticsRespVO;
};

export type ResultBoolean = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperBoolean;
};

export type ResultCourseCyclePlanDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCourseCyclePlanDetialRespVO;
};

export type ResultCoursewareByAdminPageRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperCoursewareByAdminPageRespVO;
};

export type ResultHomePageStatisticsRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperHomePageStatisticsRespVO;
};

export type ResultListAttendClassPlanDetialListRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListAttendClassPlanDetialListRespVO;
};

export type ResultListAttendClassPlanDetialRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListAttendClassPlanDetialRespVO;
};

export type ResultListString = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperListString;
};

export type ResultLong = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperLong;
};

export type ResultTeacherAndStudentRespVO = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherAndStudentRespVO;
};

export type ResultTeacherWalletInfoRespVo = {
  /** 状态码  200成功  ，  非200失败 */
  code?: number;
  /** 描述 */
  message?: string;
  data?: DataWrapperTeacherWalletInfoRespVo;
};

export type StudentEvaluateDetialCreateByTeacherReqVO = {
  /** 学生id  */
  studentId: number;
};

export type StudentPractiseBaseRespVO = {
  /** 课堂试卷id */
  id?: number;
  /** 课程计划id */
  attendClassPlanId?: number;
  /** 名称 */
  name?: string;
  /** 题目数量 */
  num?: number;
  /** 创建时间 */
  createTime?: string;
};

export type StudentPractiseCreateReqVO = {
  /** 试卷名称 */
  name?: string;
  /** 版本 */
  version: string;
  /** 年级 */
  grade: string;
  /** 单元 */
  unit: string;
  /** 课程id */
  attendClassPlanId?: number;
  /** 题库数量，不传默认为：5 */
  num?: number;
};

export type StudentPractiseDetailBaseRespVO = {
  /** 课堂试卷题目id */
  id?: number;
  /** 所属版本 */
  version?: string;
  /** 所属年级 */
  grade?: string;
  /** 所属单元 */
  unit?: string;
  /** 题库内容 */
  content?: string;
  /** 备选答案1 */
  option1?: string;
  /** 备选答案2 */
  option2?: string;
  /** 备选答案3 */
  option3?: string;
  /** 备选答案4 */
  option4?: string;
  /** 备选答案5 */
  option5?: string;
  /** 第几个备选答案是正确答案：1 第一个是正确答案 */
  answer?: number;
  /** 学生的答案 */
  studentAnswer?: number;
  /** 是否正确：0-错误，1-正确 */
  isCorrect?: number;
};

export type StudentPractiseDetailPageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 课堂试卷id */
  practiseId: number;
};

export type StudentPractisePageReqVO = {
  /** 当前页码 */
  pageNo?: number;
  /** 每页记录数默认值10条 */
  pageSize?: number;
  /** 课程id */
  attendClassPlanId?: number;
};

export type StuWalletDetailsRespVo = {
  /** 创建时间 */
  time?: string;
  /** 内容 */
  content?: string;
  /** 时长 */
  duration?: number;
};

export type TeacherAndStudentRespVO = {
  /** 创建生成的账号 */
  account?: string;
  /** 创建生成的密码 */
  password?: string;
};

export type TeacherUserDO = {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  id?: number;
  /** 老师等级:1-A，2-B，3-C，4-D，5-E */
  level?: string;
  /** 老师账号 */
  account?: string;
  /** 密码 */
  password?: string;
  /** 老师姓名 */
  name?: string;
  /** 老师编号 */
  userNo?: string;
  /** 老师考核状态（0=待考核，1=已通过，2=未通过） */
  examineSuccess?: string;
  /** 陪同方式 */
  accompanyWay?: string;
  /** 在职情况 */
  employmentStatus?: string;
  /** 职务 */
  position?: string;
  /** 技能 */
  skill?: string;
  /** 职业头衔 */
  jobTitle?: string;
  /** 服务机构 */
  serviceOrg?: string;
  /** 注册机构 */
  registOrg?: string;
  /** 身份证号 */
  idNumber?: string;
  /** 性别（1=男，2=女） */
  sex?: string;
  /** 年龄 */
  age?: number;
  /** 紧急联系人 */
  emergencyContact?: string;
  /** 紧急联系电话 */
  emergencyContactPhone?: string;
  /** 微信账号 */
  wechat?: string;
  /** 所在学校 */
  school?: string;
  /** 年级 */
  grade?: string;
  /** 班级 */
  classes?: string;
  /** 专业 */
  major?: string;
  /** 证明人 */
  certifier?: string;
  /** 籍贯 */
  nativePlace?: string;
  /** 推荐人 */
  reference?: string;
  /** 个人简介 */
  introduction?: string;
  /** 手机号码 */
  mobile?: string;
  /** 审核状态（需根据业务定义具体状态码） */
  auditStatus?: string;
  /** 教师等级 */
  userLevel?: number;
  /** 体验课授课资格（0=不能，1=能） */
  isTrialAvailable?: number;
  /** 账户状态（0=正常，1=停用） */
  status?: string;
  /** 累计已带学生数 */
  studentCount?: number;
  /** 累计已授单词数 */
  wordCount?: number;
  /** 累计评分 */
  score?: number;
  /** 学历 */
  education?: string;
};

export type TeacherWalletInfoRespVo = {
  /** 60分钟的已上课课时数量 */
  sixtyMinutesAlreadyNumber?: number;
  /** 60分钟的待上课课时数量 */
  sixtyMinutesWaitNumber?: number;
  /** 30分钟的已上课课时数量 */
  thirtyMinutesAlreadyNumber?: number;
  /** 30分钟的待上课课时数量 */
  thirtyMinutesWaitNumber?: number;
  /** 总收益 */
  totalRevenue?: number;
  /** 今日收益 */
  todayRevenue?: number;
  /** 已到账收益 */
  reachRevenue?: number;
};

export type UserLoginReqVO = {
  /** 账户 */
  account: string;
  /** 密码 */
  password: string;
};

export type UserLoginVo = {
  /** 用户编号 */
  userId?: number;
  /** 访问令牌 */
  accessToken?: string;
  /** 腾讯im的userSig */
  userSig?: string;
};

export type UserRegisterReqVO = {
  /** 姓名 */
  name: string;
  /** 考核状态（0=待考核，1=已通过,2=未通过） */
  examineSuccess?: string;
  /** 身份证号 */
  idNumber: string;
  /** 性别（1=男，2=女） */
  sex: string;
  /** 紧急联系人 */
  emergencyContact: string;
  /** 紧急联系电话 */
  emergencyContactPhone: string;
  /** 微信账号 */
  wechat?: string;
  /** 所在学校 */
  school: string;
  /** 专业 */
  major: string;
  /** 个人简介 */
  introduction?: string;
  /** 手机号码 */
  mobile: string;
  /** 学历 */
  education?: string;
  /** 密码 */
  password: string;
  /** 确认密码 */
  confirmPassword: string;
};
