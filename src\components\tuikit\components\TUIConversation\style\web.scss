.tui-conversation {
  width: 100%;
  height: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-list {
    overflow: auto;
  }
}

.tui-conversation-header {
  width: 100%;
  position: relative;

  .list {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #F4F5F9;
    padding: 7px 0;

    &-item {
      flex: 1;
      display: flex;
      position: relative;
      padding: 7px;
      cursor: pointer;
      user-select: none;
    }
  }

  &-item{
    flex: 1;
    display: flex;
    align-items: center;

    &-title {
      padding: 0 8px;
      font-size: 16px;
      font-weight: normal;
    }
  }

  &-children {
    position: absolute;
    top: 100%;
    z-index: 3;
    padding: 7px 9px;
    border-bottom: none;
    background-color: #fff;
    box-shadow: 0 3px 7px 0 #0003;
    flex-direction: column;
  }
}

.network {
  padding: 0 12px;
  display: flex;
  align-items: center;

  .icon-error{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 15px;
    height: 15px;
    border-radius: 15px;
    background: red;
    color: #fff;
    font-style: normal;
  }

  &-content {
    padding: 5px;
    font-size: 12px;
    line-height: 22px;
  }
}

.plus {
  display: inline-block;
  width: 30px;
  height: 30px;
  position: relative;
}

.plus::before,
.plus::after {
  content: "";
  position: absolute;
  background-color: #232832;
  border-radius: 0.5px;
  width: 1px;
  height: 14px;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

.plus::after {
  transform: rotate(90deg);
  width: 0.5px;
}
