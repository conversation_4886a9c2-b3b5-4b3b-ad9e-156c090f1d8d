<route lang="json5">
{
  layout: 'login',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '注册',
  },
}
</route>

<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- Logo和标题区域 -->
    <view class="flex flex-col items-center pt-80rpx pb-60rpx">
      <!-- Logo -->
      <image
        class="w-160rpx h-190rpx my-50rpx"
        src="../../assets/images/logo_login.png"
        mode="scaleToFill"
      />
    </view>

    <!-- 表单区域 -->
    <view class="flex-1 px-48rpx">
      <wd-form
        v-if="selectedRole === RoleEmu.Student"
        ref="form"
        :model="studentRegisterModel"
        :errorType="'toast'"
        class="w-full"
      >
        <view class="space-y-32rpx mt-48rpx mb-100rpx">
          <!-- 手机号输入框 -->
          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="studentRegisterModel.parentsPhone"
              prop="parentsPhone"
              placeholder="请输入手机号"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[
                {
                  required: true,
                  message: '请填写手机号',
                },
                {
                  required: false,
                  pattern: PHONE_REGEX,
                  message: '手机号格式不正确',
                },
              ]"
            />
          </view>
          <view
            v-if="studentRegisterModel.promotionCode"
            class="relative border-1rpx border-#d9d9d9 border-solid"
          >
            <wd-input
              disabled
              v-model="studentRegisterModel.promotionCode"
              prop="promotionCode"
              placeholder="请输入推广机构(选填)"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
            />
          </view>
        </view>
      </wd-form>
      <wd-form v-else ref="form" :model="teacherRegisterModel" :errorType="'toast'" class="w-full">
        <view class="space-y-32rpx mt-48rpx mb-100rpx">
          <!-- 手机号输入框 -->
          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.mobile"
              prop="mobile"
              placeholder="请输入手机号"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[
                {
                  required: true,
                  message: '请填写手机号',
                },
                {
                  required: false,
                  pattern: PHONE_REGEX,
                  message: '手机号格式不正确',
                },
              ]"
            />
          </view>
          <!-- 请设置登录密码 -->
          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.password"
              prop="password"
              placeholder="请设置登录密码"
              show-password
              custom-input-class="h-96rpx! pl-32rpx! pr-80rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请设置登录密码' }]"
            />
          </view>

          <!-- 请再次输入登录密码 -->
          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.confirmPassword"
              prop="confirmPassword"
              placeholder="请再次输入登录密码"
              show-password
              custom-input-class="h-96rpx! pl-32rpx! pr-80rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[
                { required: true, message: '请确认密码' },
                { required: true, validator: validatePassword, message: '两次密码不一致' },
              ]"
            />
          </view>
          <!-- 老师专用字段 -->
          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.name"
              prop="name"
              placeholder="请输入姓名"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写姓名' }]"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.idNumber"
              prop="idNumber"
              placeholder="请输入身份证号"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[
                { required: true, message: '请填写身份证号' },
                { required: false, pattern: ID_NUMBER_REGEX, message: '身份证号格式不正确' },
              ]"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-picker
              class="sexBox"
              v-model="teacherRegisterModel.sex"
              :columns="dictStore.getDictByType('sex')"
              placeholder="请选择性别"
              custom-class="h-96rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.school"
              prop="school"
              placeholder="请输入所在学校"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写所在学校' }]"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.major"
              prop="major"
              placeholder="请输入专业"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写专业' }]"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.emergencyContact"
              prop="emergencyContact"
              placeholder="请输入紧急联系人"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写紧急联系人' }]"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.emergencyContactPhone"
              prop="emergencyContactPhone"
              placeholder="请输入紧急联系电话"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写紧急联系电话' }]"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.wechat"
              prop="wechat"
              placeholder="请输入微信账号(选填)"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
            />
          </view>

          <view class="relative border-1rpx border-#d9d9d9 border-solid">
            <wd-input
              v-model="teacherRegisterModel.introduction"
              prop="introduction"
              placeholder="请输入个人简介"
              type="text"
              custom-input-class="h-96rpx! pl-32rpx! pr-32rpx! text-28rpx! bg-white! rounded-12rpx! border-2rpx! border-#e0e0e0!"
              :rules="[{ required: true, message: '请填写个人简介' }]"
            />
          </view>

          <!-- 根据用户类型显示不同的字段 -->
        </view>
      </wd-form>

      <!-- 注册按钮 -->
      <view class="w-full px-48rpx pb-48rpx">
        <wd-button
          custom-class="w-full! h-96rpx! rounded-12rpx! text-32rpx! font-500! bg-#3d5af5! border-#3d5af5!"
          type="primary"
          @click="handleRegister"
        >
          注册
        </wd-button>

        <!-- 登录链接 -->
        <view class="text-center mt-32rpx">
          <text class="text-28rpx text-#999">已有账号，</text>
          <text class="text-28rpx text-#3d5af5" @click="goToLogin">去登录</text>
        </view>
      </view>
    </view>

    <wd-message-box></wd-message-box>
  </view>
</template>

<script lang="ts" setup>
import * as student from '@/service/student'
import * as teacher from '@/service/teacher'
import { showToast, navigateToSub, reLaunch } from '@/utils'
import { nextTick, computed } from 'vue'
import { useMessage } from 'wot-design-uni'
import { RoleEmu, useDictStore, useRoleStore } from '@/store'
const { getRole, setRole } = useRoleStore()
const message = useMessage()
const form = ref()
const dictStore = useDictStore()
const PHONE_REGEX = /^1[3456789]\d{9}$/
const ID_NUMBER_REGEX =
  /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
// 从存储中获取选择的角色
const selectedRole = ref<RoleEmu>(getRole())

// 老师注册模型
const teacherRegisterModel = ref({
  name: '',
  examineSuccess: 0,
  idNumber: '',
  sex: 1,
  emergencyContact: '',
  emergencyContactPhone: '',
  wechat: '',
  school: '',
  major: '',
  introduction: '',
  mobile: '',
  password: '',
  confirmPassword: '',
})
const accountInfo = ref({ account: '', password: '' })
// 学生注册模型
const studentRegisterModel = ref({
  // account: '',
  // password: '',
  // confirmPassword: '',
  promotionCode: '',
  // studentName: '',
  // parentsName: '',
  parentsPhone: '',
  // school: '',
  // grade: 0,
  // sex: 1,
  // types: 2, // 用户注册
})
// 密码验证
const validatePassword = (value: any): boolean => {
  console.log('value', value, teacherRegisterModel.value.password)
  return value === teacherRegisterModel.value.password
}

// const validateTeacherPass = (value: any): boolean => {
//   return value === teacherRegisterModel.value.password
// }

// const validateStudentPass = (value: any): boolean => {
//   return value === studentRegisterModel.value.password
// }

// 发送验证码
// const sendVerificationCode = () => {
//   if (!mobileNumber.value) {
//     showToast('请先输入手机号')
//     return
//   }

//   // TODO: 调用发送验证码API
//   showToast('验证码已发送')

//   // 开始倒计时
//   countdown.value = 60
//   const timer = setInterval(() => {
//     countdown.value--
//     if (countdown.value <= 0) {
//       clearInterval(timer)
//     }
//   }, 1000)
// }

const goToLogin = () => {
  navigateToSub('/login/login')
}
const handSubmit = async () => {
  try {
    const fn =
      selectedRole.value === RoleEmu.Teacher
        ? teacher.baseinfoLoginUsingPost
        : student.baseinfoLoginUsingPost

    // 先尝试学生登录
    let resLogin: any
    try {
      resLogin = await fn({ body: accountInfo.value })
    } catch (studentError) {
      // 学生登录失败，尝试老师登录
      showToast('登录失败，请检查账号密码')
      return
    }

    if (resLogin.code === 200) {
      showToast('登录成功')
      uni.setStorageSync('token', resLogin.data.item.accessToken)
      uni.setStorageSync('userSig', resLogin.data.item.userSig)
      uni.setStorageSync('userID', accountInfo.value.account)
      uni.setStorageSync('userInfo', resLogin.data.item)
      setRole(selectedRole.value)
      reLaunch('/index/index')
    } else {
      showToast(resLogin.message || '登录失败，请检查账号密码')
    }
  } catch (error) {
    console.error('登录异常:', error)
    showToast('登录失败，请稍后重试')
  }
}

const handleRegister = async () => {
  console.log('开始注册流程')
  const res = await form.value.validate()
  if (!res.valid) {
    console.log('表单验证失败', res)
    return
  }
  try {
    let resRegister
    // 准备注册数据
    if (selectedRole.value === 'teacher') {
      // 老师注册
      const teacherData = {
        ...teacherRegisterModel.value,
      }
      console.log('提交老师注册表单', teacherData)
      resRegister = await teacher.baseinfoRegisterUsingPost({
        body: teacherData,
      })
    } else {
      // 学生注册
      const studentData = {
        ...studentRegisterModel.value,
      }
      console.log('提交学生注册表单', studentData)
      resRegister = await student.baseinfoRegisterUsingPost({
        body: studentData,
      })
    }
    console.log('注册API响应:', resRegister)

    if (resRegister.code === 200) {
      accountInfo.value.account = resRegister.data.item.account
      accountInfo.value.password = resRegister.data.item.password
      message
        .alert({
          msg: `账号:${resRegister.data.item.account} 密码:${resRegister.data.item.password}`,
          title: '注册成功,点击确定后系统将在1.5秒后自动登录',
        })
        // 完成提交答案，并确认
        .then(async () => {
          // 使用nextTick确保状态更新在DOM更新之后
          await nextTick()
          // 注册成功后跳转到登录页
          // uni.setClipboardData({
          //   data: resRegister.data.item.account,
          //   fail: (fail) => {
          //     showToast('账号复制失败')
          //   },
          // })
          setTimeout(() => {
            handSubmit()
          }, 1500)
        })
        .catch(() => {
          console.log('点击了取消按钮')
        })
    } else {
      // API返回了错误状态码
      console.error('注册失败, API返回错误:', resRegister)
      showToast(resRegister.message || '注册失败，请稍后重试')
    }
  } catch (error) {
    console.error('注册过程发生异常:', error)
    showToast('注册失败，请稍后重试')
  }
}
onLoad((option) => {
  if (option.promotionCode) {
    studentRegisterModel.value.promotionCode = option.promotionCode
  }
})
</script>
<style lang="css" scoped>
:deep(.sexBox .wd-picker__value) {
  margin-top: 10rpx;
  padding-left: 20rpx !important;
  font-size: 28rpx;
  color: rgb(153, 153, 153) !important;
  font-size: 28rpx !important;
}
:deep(.gradeBox .wd-picker__value) {
  margin-top: 10rpx;
  padding-left: 20rpx !important;
  font-size: 28rpx;
  color: rgb(153, 153, 153) !important;
  font-size: 28rpx !important;
}
</style>
