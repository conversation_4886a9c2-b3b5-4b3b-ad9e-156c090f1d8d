/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentRechargeCountConfigManagement';
import * as API from './types';

/** 【学生】查询有效的充值可选项配置详情 POST /app/student/rechargecountconfig/get */
export function useRechargecountconfigGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultConfigBaseByStuAppRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.rechargecountconfigGetUsingPost,
    onSuccess(data: API.ResultConfigBaseByStuAppRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
