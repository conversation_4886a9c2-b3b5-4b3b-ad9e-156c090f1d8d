/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './aIshijuan';
import * as API from './types';

/** 学生答题 POST /ai/exam/answer */
export function useAnswerUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.answerUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 生成试卷返回会话id POST /ai/exam/create */
export function useCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCreateExamVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.createUsingPost,
    onSuccess(data: API.ResultCreateExamVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 根据会话id查询试卷内容 POST /ai/exam/queryBySessionId */
export function useQueryBySessionIdUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultExamVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.queryBySessionIdUsingPost,
    onSuccess(data: API.ResultExamVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 学生查询自己的试卷 POST /ai/exam/queryByStudentId */
export function useQueryByStudentIdUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponsePageResultAnswerExamVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.queryByStudentIdUsingPost,
    onSuccess(data: API.PageResultResponsePageResultAnswerExamVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 老师查询试卷 POST /ai/exam/queryByTeacherId */
export function useQueryByTeacherIdUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponsePageResultAnswerExamVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.queryByTeacherIdUsingPost,
    onSuccess(data: API.PageResultResponsePageResultAnswerExamVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
