.main {
  background: #FFF;
  border: 1px solid #E0E0E0;
  box-shadow: 0 -4px 12px 0 rgba(0, 0, 0, 0.06);

  .left {
    border-right: 1px solid #E8E8E9;
  }

  .transfer-header {
    font-weight: 500;
    color: #000;
    letter-spacing: 0;

    input {
      background: #FFF;
      border: 1px solid #DEE0E3;
      font-weight: 500;
      color: #8F959E;
      letter-spacing: 0;
    }
  }

  .transfer-list {
    .transfer-text {
      font-weight: 500;
      color: #8F959E;
      letter-spacing: 0;
    }

    &-item {
      .disabled {
        background: #eee;
      }
    }
  }
}

.btn {
  background: #3370FF;
  border: 0 solid #2F80ED;
  font-weight: 400;
  color: #FFF;

  &-cancel {
    background: #FFF;
    border: 1px solid #DDD;
    color: #828282;
  }
}

.btn-no {
  background: #e8e8e9;
  border: 1px solid #DDD;
  font-weight: 400;
  color: #FFF;
}

.transfer-h5-header {
  background: #FFF;

  .title {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #000;
    letter-spacing: 0;
  }
}
