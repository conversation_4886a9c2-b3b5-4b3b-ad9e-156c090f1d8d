/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './teacherTrainingRecordManagement';
import * as API from './types';

/** 【运营】对指定老师进行培训审核操作 POST /pc/teacher/training/create */
export function usePcTeacherTrainingCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherTrainingCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】获取指定id的培训审核记录明细信息 POST /pc/teacher/training/get */
export function usePcTeacherTrainingGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTrainingRecordDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherTrainingGetUsingPost,
    onSuccess(data: API.ResultTrainingRecordDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询培训审核记录信息 POST /pc/teacher/training/page */
export function usePcTeacherTrainingPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultTrainingRecordDetialRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherTrainingPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultTrainingRecordDetialRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
