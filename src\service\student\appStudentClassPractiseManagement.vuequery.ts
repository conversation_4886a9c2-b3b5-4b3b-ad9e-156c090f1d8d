/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentClassPractiseManagement';
import * as API from './types';

/** 【学生】开始答题 POST /app/student/classpractis/answerQuestions */
export function useClasspractisAnswerQuestionsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisAnswerQuestionsUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】生成试卷 POST /app/student/classpractis/generateRandomPractice */
export function useClasspractisGenerateRandomPracticeUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisGenerateRandomPracticeUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】试卷题目分页查询 POST /app/student/classpractis/practisDetailPage */
export function useClasspractisPractisDetailPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStudentPractiseDetailBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisPractisDetailPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultStudentPractiseDetailBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】试卷分页查询 POST /app/student/classpractis/practisPage */
export function useClasspractisPractisPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStudentPractiseBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisPractisPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultStudentPractiseBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】获取指定试卷答题统计 POST /app/student/classpractis/statistics */
export function useClasspractisStatisticsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStudentPractiseStatisticsRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.classpractisStatisticsUsingPost,
    onSuccess(data: API.ResultStudentPractiseStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
