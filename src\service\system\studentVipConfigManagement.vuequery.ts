/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './studentVipConfigManagement';
import * as API from './types';

/** 【运营】上架学生vip权益配置信息 POST /pc/student/vipconfig/active */
export function usePcStudentVipconfigActiveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentVipconfigActiveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】新增学生vip权益配置 POST /pc/student/vipconfig/create */
export function usePcStudentVipconfigCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentVipconfigCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】查询学生vip权益配置详情 POST /pc/student/vipconfig/get */
export function usePcStudentVipconfigGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStudentVipConfigBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentVipconfigGetUsingPost,
    onSuccess(data: API.ResultStudentVipConfigBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询学生vip权益配置信息 POST /pc/student/vipconfig/page */
export function usePcStudentVipconfigPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStudentVipConfigBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentVipconfigPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultStudentVipConfigBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
