/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 机构钱包明细 POST /org/wallet/details */
export async function orgWalletDetailsUsingPost({
  body,
  options,
}: {
  body: API.PageDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSysOrgWalletDetailsRespVo>(
    '/org/wallet/details',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 指定id机构钱包明细 POST /org/wallet/details/id */
export async function orgWalletDetailsIdUsingPost({
  body,
  options,
}: {
  body: API.SysOrgWalletIdDetailsReqVo;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSysOrgWalletDetailsRespVo>(
    '/org/wallet/details/id',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 机构钱包信息 POST /org/wallet/info */
export async function orgWalletInfoUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSysOrgWalletInfoRespVO>('/org/wallet/info', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 查询指定id机构钱包信息 POST /org/wallet/info/id */
export async function orgWalletInfoIdUsingPost({
  body,
  options,
}: {
  body: API.SysOrgGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSysOrgWalletInfoRespVO>('/org/wallet/info/id', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
