/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentOrder';
import * as API from './types';

/** 【学生】充值 POST /app/student/order/recharge */
export function useOrderRechargeUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultWxPayInfoVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orderRechargeUsingPost,
    onSuccess(data: API.ResultWxPayInfoVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】学生钱包明细 POST /app/student/order/wallet/details */
export function useOrderWalletDetailsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStuWalletDetailsRespVo
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orderWalletDetailsUsingPost,
    onSuccess(data: API.PageResultResponsePageResultStuWalletDetailsRespVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】学生钱包信息 POST /app/student/order/wallet/info */
export function useOrderWalletInfoUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStuWalletInfoRespVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.orderWalletInfoUsingPost,
    onSuccess(data: API.ResultStuWalletInfoRespVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
