/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentComplaintManagement';
import * as API from './types';

/** 【学生app】发起投诉 POST /app/student/complaint/create */
export function useComplaintCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.complaintCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生app】根据id查看投诉详情 POST /app/student/complaint/get */
export function useComplaintGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherComplaintBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.complaintGetUsingPost,
    onSuccess(data: API.ResultTeacherComplaintBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生app】分页查询投诉列表 POST /app/student/complaint/page */
export function useComplaintPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultTeacherComplaintBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.complaintPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultTeacherComplaintBaseRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
