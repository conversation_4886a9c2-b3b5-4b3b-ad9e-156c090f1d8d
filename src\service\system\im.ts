/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 历史消息 POST /im/history */
export async function imHistoryUsingPost({
  body,
  options,
}: {
  body: API.HistoryDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponseListMessageVo>('/im/history', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
