/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentVipConfigManagement';
import * as API from './types';

/** 【学生】查询有效学生vip权益配置详情 POST /app/student/vipconfig/get */
export function useVipconfigGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStudentVipConfigBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.vipconfigGetUsingPost,
    onSuccess(data: API.ResultStudentVipConfigBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
