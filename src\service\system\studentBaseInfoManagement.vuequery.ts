/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './studentBaseInfoManagement';
import * as API from './types';

/** 【运营】添加学生信息 POST /pc/student/baseinfo/create */
export function usePcStudentBaseinfoCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherAndStudentRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentBaseinfoCreateUsingPost,
    onSuccess(data: API.ResultTeacherAndStudentRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】通过id获取学生信息 POST /pc/student/baseinfo/get */
export function usePcStudentBaseinfoGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStuUserGetRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentBaseinfoGetUsingPost,
    onSuccess(data: API.ResultStuUserGetRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】修改学生信息 POST /pc/student/baseinfo/modifyStu */
export function usePcStudentBaseinfoModifyStuUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentBaseinfoModifyStuUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页请求获取学生列表 POST /pc/student/baseinfo/page */
export function usePcStudentBaseinfoPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStuUserPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcStudentBaseinfoPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultStuUserPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
