/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentReservationRecordManagement';
import * as API from './types';

/** 【学生app】去约课 POST /app/student/reservation/create */
export function useReservationCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.reservationCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生app】获取指定id的约课记录明细信息 POST /app/student/reservation/get */
export function useReservationGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCourseReservationDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.reservationGetUsingPost,
    onSuccess(data: API.ResultCourseReservationDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生app】分页查询约课记录信息 POST /app/student/reservation/page */
export function useReservationPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCourseReservationDetialRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.reservationPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultCourseReservationDetialRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
