/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentEvaluateStatisticsManagement';
import * as API from './types';

/** 【学生】获取评测统计 POST /app/student/evaluatestatistics/get */
export function useEvaluatestatisticsGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultWordStatisticsRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.evaluatestatisticsGetUsingPost,
    onSuccess(data: API.ResultWordStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
