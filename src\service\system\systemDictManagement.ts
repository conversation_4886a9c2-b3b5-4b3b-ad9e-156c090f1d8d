/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【系统管理】添加字典信息 POST /pc/system/dict/create */
export async function pcSystemDictCreateUsingPost({
  body,
  options,
}: {
  body: API.SystemDictAddReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/system/dict/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、运营、财务、机构、老师、学生】查询指定type类型的字典列表信息 POST /pc/system/dict/detial */
export async function pcSystemDictDetialUsingPost({
  body,
  options,
}: {
  body: API.SystemDictDataReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListSystemDictDataRespVO>('/pc/system/dict/detial', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理】修改字典信息 POST /pc/system/dict/modify */
export async function pcSystemDictModifyUsingPost({
  body,
  options,
}: {
  body: API.SystemDictModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/dict/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、运营、财务、机构、老师、学生】分页查询字典信息 POST /pc/system/dict/page */
export async function pcSystemDictPageUsingPost({
  body,
  options,
}: {
  body: API.SystemDictPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSystemDictDetialRespVO>(
    '/pc/system/dict/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【系统管理】删除指定字典信息 POST /pc/system/dict/remove */
export async function pcSystemDictRemoveUsingPost({
  body,
  options,
}: {
  body: API.SystemDictRemoveReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/dict/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
