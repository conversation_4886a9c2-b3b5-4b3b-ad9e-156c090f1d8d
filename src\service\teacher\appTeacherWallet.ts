/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【老师】老师钱包明细 POST /app/teacher/wallet/details */
export async function walletDetailsUsingPost({
  body,
  options,
}: {
  body: API.PageDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStuWalletDetailsRespVo>(
    '/app/teacher/wallet/details',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】老师钱包信息 POST /app/teacher/wallet/info */
export async function walletInfoUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherWalletInfoRespVo>(
    '/app/teacher/wallet/info',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}
