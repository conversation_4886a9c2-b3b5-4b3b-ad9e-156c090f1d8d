<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的消息',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="message-list-container">
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <wd-icon name="search" size="32rpx" color="#999999" class="mr-[8rpx]"></wd-icon>
        <input v-model="searchKeyword" class="search-input" placeholder="搜索联系人" />
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-list">
      <view
        v-for="item in filteredMessageList"
        :key="item.conversationID"
        class="message-item"
        @click="goToChat(item)"
      >
        <!-- 头像 -->
        <view class="avatar-container">
          <image v-if="item.avatar" :src="item.avatar" class="avatar" mode="aspectFill" />
          <view v-else class="avatar-placeholder">
            <wd-icon name="user" size="60rpx" color="#CCCCCC"></wd-icon>
          </view>
          <!-- 未读消息红点 -->
          <view v-if="item.unreadCount > 0" class="unread-badge">
            {{ item.unreadCount > 99 ? '99+' : item.unreadCount }}
          </view>
        </view>

        <!-- 消息内容 -->
        <view class="message-content">
          <view class="message-header">
            <view class="contact-name">{{ item.name }}</view>
            <view class="message-time">{{ formatTime(item.lastMessageTime) }}</view>
          </view>
          <view class="message-preview">
            <text class="preview-text">{{ item.lastMessage }}</text>
          </view>
        </view>

        <!-- 右侧箭头 -->
        <view class="arrow-container">
          <wd-icon name="arrow-right" size="32rpx" color="#CCCCCC"></wd-icon>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredMessageList.length === 0" class="empty-state">
        <wd-icon name="chat" size="120rpx" color="#CCCCCC"></wd-icon>
        <view class="empty-text">暂无消息</view>
        <view class="empty-subtext">开始与联系人聊天吧</view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-loading size="large" />
      <view class="loading-text">加载中...</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { navigateToSub } from '@/utils'

import { useChatStore } from '@/store/chat'

defineOptions({
  name: 'MessageList',
})

interface MessageItem {
  conversationID: string
  name: string
  avatar?: string
  lastMessage: string
  lastMessageTime: Date
  unreadCount: number
  userId: string
  userType: 'student' | 'teacher'
}

// 搜索关键词
const searchKeyword = ref('')
// 消息列表
const messageList = ref<MessageItem[]>([])
// 加载状态
const loading = ref(false)

// 过滤后的消息列表
const filteredMessageList = computed(() => {
  if (!searchKeyword.value) {
    return messageList.value
  }
  return messageList.value.filter((item) =>
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const oneDay = 24 * 60 * 60 * 1000
  const oneHour = 60 * 60 * 1000
  const oneMinute = 60 * 1000

  if (diff < oneMinute) {
    return '刚刚'
  } else if (diff < oneHour) {
    return `${Math.floor(diff / oneMinute)}分钟前`
  } else if (diff < oneDay) {
    return `${Math.floor(diff / oneHour)}小时前`
  } else if (diff < 2 * oneDay) {
    return '昨天'
  } else {
    return time.toLocaleDateString()
  }
}

// 跳转到聊天页面
const goToChat = (item: MessageItem) => {
  // 跳转到聊天页面
  navigateToSub(`/chat/chat?receiverUserID=${item.userId}`)
}
const init = (chat) => {
  setTimeout(() => {
    chat.getConversationList().then((res) => {
      messageList.value = res.data.conversationList.map((item) => {
        return {
          ...item,
          conversationID: item.conversationID,
          id: item.userProfile.userID,
          name: item.userProfile.nick,
          avatar: item.userProfile.avatar,
          lastMessage: item.lastMessage.payload.text,
          lastMessageTime: new Date(item.lastMessage.lastTime * 1000), // 10分钟前
          unreadCount: item.unreadCount,
          userId: item.userProfile.userID,
          userType: item.userProfile.role === 0 ? 'teacher' : 'student',
        }
      })
      console.log('getConversationList', messageList.value)
    })
  }, 500)
}
const chatStore = useChatStore()
if (!chatStore.isReady) {
  chatStore.login(uni.getStorageSync('userID'), uni.getStorageSync('userSig'))
}
onShow(() => {
  chatStore.getChatIns().then(init)
})
</script>

<style scoped>
.message-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-container {
  padding: 24rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.message-list {
  background-color: #ffffff;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.message-item:active {
  background-color: #f8f8f8;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  line-height: 1;
  color: #ffffff;
  text-align: center;
  background-color: #ff4757;
  border-radius: 16rpx;
}

.message-content {
  flex: 1;
  margin-right: 16rpx;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.contact-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.message-time {
  font-size: 24rpx;
  color: #999999;
}

.message-preview {
  display: flex;
  align-items: center;
}

.preview-text {
  max-width: 400rpx;
  overflow: hidden;
  font-size: 28rpx;
  color: #666666;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-container {
  display: flex;
  align-items: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  color: #999999;
}

.empty-text {
  margin-top: 24rpx;
  margin-bottom: 8rpx;
  font-size: 32rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #cccccc;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #999999;
}
</style>
