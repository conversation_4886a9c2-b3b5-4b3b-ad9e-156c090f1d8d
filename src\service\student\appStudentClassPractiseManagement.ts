/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】开始答题 POST /app/student/classpractis/answerQuestions */
export async function classpractisAnswerQuestionsUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseDetailDoingReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/app/student/classpractis/answerQuestions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生】生成试卷 POST /app/student/classpractis/generateRandomPractice */
export async function classpractisGenerateRandomPracticeUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>(
    '/app/student/classpractis/generateRandomPractice',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】试卷题目分页查询 POST /app/student/classpractis/practisDetailPage */
export async function classpractisPractisDetailPageUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseDetailPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStudentPractiseDetailBaseRespVO>(
    '/app/student/classpractis/practisDetailPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】试卷分页查询 POST /app/student/classpractis/practisPage */
export async function classpractisPractisPageUsingPost({
  body,
  options,
}: {
  body: API.StudentPractisePageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStudentPractiseBaseRespVO>(
    '/app/student/classpractis/practisPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】获取指定试卷答题统计 POST /app/student/classpractis/statistics */
export async function classpractisStatisticsUsingPost({
  body,
  options,
}: {
  body: API.StudentPractiseStatisticsReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStudentPractiseStatisticsRespVO>(
    '/app/student/classpractis/statistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
