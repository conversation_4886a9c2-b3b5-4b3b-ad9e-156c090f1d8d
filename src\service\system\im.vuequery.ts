/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './im';
import * as API from './types';

/** 历史消息 POST /im/history */
export function useImHistoryUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponseListMessageVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.imHistoryUsingPost,
    onSuccess(data: API.PageResultResponseListMessageVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
