/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】根据id获取评测单词详情 POST /app/student/evaluateword/get */
export async function evaluatewordGetUsingPost({
  body,
  options,
}: {
  body: API.StudentEvaluateWordGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStudentEvaluateWordGetRespVO>(
    '/app/student/evaluateword/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】随机获取评测单词列表 POST /app/student/evaluateword/getRandomEvaluateWords */
export async function evaluatewordGetRandomEvaluateWordsUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStudentEvaluateWordGetRespVO>(
    '/app/student/evaluateword/getRandomEvaluateWords',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}
