# 学生功能页面

这个目录包含了学生相关的功能页面，包括训练结果、评价老师和投诉功能。

## 页面列表

### 1. 训练结果页面 (`training-result/training-result.vue`)

**功能描述**：展示学生的训练情况和学习成果

**页面路径**：`/pages-sub/training-result/training-result`

**主要功能**：

- 显示用户姓名、陪练姓名、训练名称
- 展示学习统计：学新单词总数、正确数、错误数
- 计算并显示正确率和学习时长
- 提供"去评价"和"去投诉"操作按钮

**使用方法**：

```javascript
// 跳转到训练结果页面
navigateToSub(`/training-result/training-result?attendClassPlanId=${attendClassPlanId}`)
```

**参数**：

- `attendClassPlanId`: 课程计划ID（必填）

### 2. 评价老师页面 (`evaluate/evaluate.vue`)

**功能描述**：学生对老师进行评价

**页面路径**：`/pages-sub/evaluate/evaluate`

**主要功能**：

- 显示评价老师信息和上课时间
- 提供四个评价维度：发音、态度、感染力、责任心
- 使用星级评分系统（1-5星）
- 计算总体评价分数
- 提交评价到后台

**使用方法**：

```javascript
// 跳转到评价页面
navigateToSub(`/evaluate/evaluate?attendClassPlanId=${attendClassPlanId}&teacherId=${teacherId}`)
```

**参数**：

- `attendClassPlanId`: 课程计划ID（必填）
- `teacherId`: 老师ID（可选，会从课程详情中获取）

**评价维度**：

- 发音：老师的发音准确性
- 态度：老师的教学态度
- 感染力：老师的课堂感染力
- 责任心：老师的责任心表现

### 3. 投诉页面 (`complaint/complaint.vue`)

**功能描述**：学生对老师或课程进行投诉

**页面路径**：`/pages-sub/complaint/complaint`

**主要功能**：

- 显示课程信息（上课时间、上课老师）
- 提供投诉内容输入框（支持500字以内）
- 显示投诉须知和注意事项
- 提交投诉到后台

**使用方法**：

```javascript
// 跳转到投诉页面
navigateToSub(`/complaint/complaint?attendClassPlanId=${attendClassPlanId}`)
```

**参数**：

- `attendClassPlanId`: 课程计划ID（必填）

**投诉须知**：

- 请如实描述问题，我们会在24小时内处理
- 恶意投诉将影响您的信用记录
- 投诉处理结果将通过消息通知您

## API 接口

### 训练结果相关

- `attendclassStatisticsUsingPost`: 获取训练统计数据
- `attendclassGetUsingPost`: 获取课程详情

### 评价相关

- `evaluateCreateUsingPost`: 提交老师评价

### 投诉相关

- `complaintCreateUsingPost`: 提交投诉

## 数据类型

### AttendClassPlanStatisticsRespVO

```typescript
{
  id?: number                    // 上课计划id
  studentName?: string           // 学生姓名
  teacherName?: string           // 老师姓名
  coursewareName?: string        // 训练名称、课件资料名称
  newWordsCount?: number         // 学新单词总数
  newWordsCorrectCount?: number  // 学新单词正确数
  newWordsErrorCount?: number    // 学新单词错误数
  startTime?: string             // 开始时间
  endTime?: string               // 结束时间
}
```

### TeacherEvaluateCreateReqVO

```typescript
{
  attendClassPlanId: number // 上课计划id
  teacherId: number // 老师id
  pronunciationScore: string // 发音评分 1-5
  attitudeScore: string // 态度评分 1-5
  appealScore: string // 感染力评分 1-5
  responsibilityScore: string // 责任心评分 1-5
}
```

### TeacherComplaintCreateReqVO

```typescript
{
  attendClassPlanId: number // 上课计划id
  content: string // 投诉内容
}
```

## 课程资料详情页面

### 4. 学生课程资料详情页面 (`student-courseware-detail/student-courseware-detail.vue`)

**功能描述**：学生查看课程资料和单词列表

**页面路径**：`/pages-sub/student-courseware-detail/student-courseware-detail`

**主要功能**：

- 显示课程资料基本信息（资料ID、名称、创建时间）
- 展示单词列表，支持分页浏览
- 播放单词发音
- 提供学习提示

**使用方法**：

```javascript
// 跳转到学生课程资料详情页面
navigateToSub(`/student-courseware-detail/student-courseware-detail?id=${coursewareId}`)

// 或使用工具函数（推荐）
navigateToCoursewareDetail(coursewareId)
```

**参数**：

- `id`: 课程资料ID（必填）

### 5. 老师课程资料详情页面 (`teacher-courseware-detail/teacher-courseware-detail.vue`)

**功能描述**：老师查看课程资料、管理单词学习进度和课堂操作

**页面路径**：`/pages-sub/teacher-courseware-detail/teacher-courseware-detail`

**主要功能**：

- 显示课程资料基本信息
- 展示单词列表，支持分页浏览
- 播放单词发音
- 标记单词学习进度
- 开始/结束上课操作

**使用方法**：

```javascript
// 跳转到老师课程资料详情页面
navigateToSub(
  `/teacher-courseware-detail/teacher-courseware-detail?id=${coursewareId}&classId=${classId}`,
)

// 或使用工具函数（推荐）
navigateToCoursewareDetail(coursewareId, classId)
```

**参数**：

- `id`: 课程资料ID（必填）
- `classId`: 课程计划ID（可选，用于课堂操作）

### 6. 我的投诉页面 (`my-complaints/my-complaints.vue`)

**功能描述**：学生查看自己的投诉记录和处理状态

**页面路径**：`/pages-sub/my-complaints/my-complaints`

**主要功能**：

- 分页显示投诉列表
- 显示投诉时间、投诉老师、投诉内容、处理状态
- 查看处理方案（已处理的投诉）
- 支持加载更多

**使用方法**：

```javascript
// 跳转到我的投诉页面
navigateToSub('/my-complaints/my-complaints')
```

## 工具函数

### navigateToCoursewareDetail

根据用户角色自动跳转到对应的课程资料详情页面

```javascript
import { navigateToCoursewareDetail } from '@/utils'

// 基本用法
navigateToCoursewareDetail(coursewareId)

// 带课程计划ID（主要用于老师）
navigateToCoursewareDetail(coursewareId, classId)
```

**参数**：

- `coursewareId`: 课程资料ID（必填）
- `classId`: 课程计划ID（可选）

**逻辑**：

- 如果用户角色是 `teacher`，跳转到老师版本页面
- 否则跳转到学生版本页面

## 样式特点

- 使用渐变色背景设计
- 卡片式布局，圆角和阴影效果
- 响应式设计，适配不同屏幕
- 统一的色彩搭配和交互效果

## 注意事项

1. 所有页面都需要传递正确的 `attendClassPlanId` 参数
2. 页面会自动处理API调用失败的情况
3. 评价页面的 `teacherId` 可以通过URL参数传递，也可以从课程详情中获取
4. 投诉内容限制在500字以内
5. 时间格式统一为 'YYYY-MM-DD HH:mm:ss'
