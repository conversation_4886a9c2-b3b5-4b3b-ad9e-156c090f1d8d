/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【系统管理】添加菜单信息 POST /pc/system/menu/create */
export async function pcSystemMenuCreateUsingPost({
  body,
  options,
}: {
  body: API.SystemMenuAddRespVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/system/menu/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、运营、财务、机构、老师、学生】获取指定菜单信息 POST /pc/system/menu/get */
export async function pcSystemMenuGetUsingPost({
  body,
  options,
}: {
  body: API.SystemMenuGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultSystemMenuBaseRespVO>('/pc/system/menu/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理】修改指定菜单信息 POST /pc/system/menu/modify */
export async function pcSystemMenuModifyUsingPost({
  body,
  options,
}: {
  body: API.SystemMenuModifyRespVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/menu/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理、运营、财务、机构、老师、学生】菜单列表分页 POST /pc/system/menu/page */
export async function pcSystemMenuPageUsingPost({
  body,
  options,
}: {
  body: API.SystemMenuPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultSystemMenuDO>(
    '/pc/system/menu/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【系统管理】删除指定菜单信息 POST /pc/system/menu/remove */
export async function pcSystemMenuRemoveUsingPost({
  body,
  options,
}: {
  body: API.SystemMenuRemoveReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/system/menu/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【系统管理】树形结构返回有权限菜单列表 POST /pc/system/menu/treeList */
export async function pcSystemMenuTreeListUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListSystemMenuTreeRespVO>(
    '/pc/system/menu/treeList',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}
