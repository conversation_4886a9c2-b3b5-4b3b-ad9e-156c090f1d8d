/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appTeacherWallet';
import * as API from './types';

/** 【老师】老师钱包明细 POST /app/teacher/wallet/details */
export function useWalletDetailsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStuWalletDetailsRespVo
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.walletDetailsUsingPost,
    onSuccess(data: API.PageResultResponsePageResultStuWalletDetailsRespVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】老师钱包信息 POST /app/teacher/wallet/info */
export function useWalletInfoUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherWalletInfoRespVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.walletInfoUsingPost,
    onSuccess(data: API.ResultTeacherWalletInfoRespVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
