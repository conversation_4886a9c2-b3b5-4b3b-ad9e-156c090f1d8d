
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#FFFFFF","navigationBar":{"backgroundColor":"#f8f8f8","titleText":"unibest","style":"default","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"unibest","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.29","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"navigationBar":{"titleText":"首页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/mine/mine","meta":{"navigationBar":{"titleText":"个人中心","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/self-study/self-study","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"自测","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/attend-class/attend-class","meta":{"navigationBar":{"titleText":"课堂","type":"default"},"isNVue":false}},{"path":"pages-sub/book-class/book-class","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"约课","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/chat/chat","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"沟通","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/complaint/complaint","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"投诉","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/courseware-detail/courseware-detail","meta":{"navigationBar":{"titleText":"课程资料详情","type":"default"},"isNVue":false}},{"path":"pages-sub/courseware-forget-detail/courseware-forget-detail","meta":{"navigationBar":{"titleText":"课程资料详情","type":"default"},"isNVue":false}},{"path":"pages-sub/evaluate/evaluate","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"评价老师","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/login/login","meta":{"navigationBar":{"titleText":"登录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages-sub/message-list/message-list","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"我的消息","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/more-teacher/more-teacher","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"更多老师","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/my-complaints/my-complaints","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"我的投诉","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/practise-result/practise-result","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"答题结果","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/recharge/recharge","meta":{"navigationBar":{"titleText":"课时充值","type":"default"},"isNVue":false}},{"path":"pages-sub/register/register","meta":{"navigationBar":{"titleText":"注册","style":"custom","type":"default"},"isNVue":false}},{"path":"pages-sub/start-answer/start-answer","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"答题","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/student-courseware-detail/student-courseware-detail","meta":{"navigationBar":{"titleText":"课程资料详情","type":"default"},"isNVue":false}},{"path":"pages-sub/teacher-class/teacher-class","meta":{"navigationBar":{"titleText":"老师上课","type":"default"},"isNVue":false}},{"path":"pages-sub/teacher-courseware-detail/teacher-courseware-detail","meta":{"navigationBar":{"titleText":"课程资料详情","type":"default"},"isNVue":false}},{"path":"pages-sub/teacher-detail/teacher-detail","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"老师详情","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages-sub/training-result/training-result","meta":{"navigationBar":{"backgroundColor":"#f8f8f8","titleText":"训练结果","type":"default","titleColor":"#000000"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  