/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './teacherReservationRecordManagement';
import * as API from './types';

/** 【运营】获取上课抗遗忘复习统计详情 POST /pc/teacher/reservation/classPlanAntiforgetStatistics */
export function usePcTeacherReservationClassPlanAntiforgetStatisticsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.ResultAttendClassPlanAntiforgetStatisticsRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherReservationClassPlanAntiforgetStatisticsUsingPost,
    onSuccess(data: API.ResultAttendClassPlanAntiforgetStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询课程计划 POST /pc/teacher/reservation/classPlanPage */
export function usePcTeacherReservationClassPlanPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultAttendClassPlanDetialPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherReservationClassPlanPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultAttendClassPlanDetialPageRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】获取上课统计详情 POST /pc/teacher/reservation/classPlanStatistics */
export function usePcTeacherReservationClassPlanStatisticsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAttendClassPlanStatisticsRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherReservationClassPlanStatisticsUsingPost,
    onSuccess(data: API.ResultAttendClassPlanStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】去约课  POST /pc/teacher/reservation/create */
export function usePcTeacherReservationCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherReservationCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】获取指定id的约课记录明细信息 POST /pc/teacher/reservation/get */
export function usePcTeacherReservationGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCourseReservationDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherReservationGetUsingPost,
    onSuccess(data: API.ResultCourseReservationDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询约课记录信息 POST /pc/teacher/reservation/page */
export function usePcTeacherReservationPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCourseReservationDetialRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherReservationPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultCourseReservationDetialRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
