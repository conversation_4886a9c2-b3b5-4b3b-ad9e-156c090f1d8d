/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './systemOrder';
import * as API from './types';

/** 充值明细 POST /pc/system/order/details */
export function usePcSystemOrderDetailsUsingPostMutation(options?: {
  onSuccess?: (value?: API.PageResultResponsePageResultOrderDetailsVo) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemOrderDetailsUsingPost,
    onSuccess(data: API.PageResultResponsePageResultOrderDetailsVo) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】PC端充值 POST /pc/system/order/recharge */
export function usePcSystemOrderRechargeUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcSystemOrderRechargeUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
