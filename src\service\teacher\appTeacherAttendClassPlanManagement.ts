/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【老师】获取上课抗遗忘复习统计详情 POST /app/teacher/attendclass/antiforgetStatistics */
export async function attendclassAntiforgetStatisticsUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanAntiforgetStatisticsRespVO>(
    '/app/teacher/attendclass/antiforgetStatistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】完成抗遗忘单词复习 POST /app/teacher/attendclass/completeAntiforgetWord */
export async function attendclassCompleteAntiforgetWordUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanStartOrEndReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>(
    '/app/teacher/attendclass/completeAntiforgetWord',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】分页查询课程资料信息 POST /app/teacher/attendclass/coursewarePage */
export async function attendclassCoursewarePageUsingPost({
  body,
  options,
}: {
  body: API.CoursewareByAdminPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareByAdminPageRespVO>(
    '/app/teacher/attendclass/coursewarePage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】结束上课 POST /app/teacher/attendclass/endClassPlan */
export async function attendclassEndClassPlanUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanStartOrEndReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/attendclass/endClassPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【老师】根据id查询课程详情 POST /app/teacher/attendclass/get */
export async function attendclassGetUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanDetialRespVO>(
    '/app/teacher/attendclass/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】根据资料id查询资料详情 POST /app/teacher/attendclass/getCoursewareDetial */
export async function attendclassGetCoursewareDetialUsingPost({
  body,
  options,
}: {
  body: API.CoursewareDetialGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCoursewareByAdminPageRespVO>(
    '/app/teacher/attendclass/getCoursewareDetial',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】app首页统计详情 POST /app/teacher/attendclass/homePageStatistics */
export async function attendclassHomePageStatisticsUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultHomePageStatisticsRespVO>(
    '/app/teacher/attendclass/homePageStatistics',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【老师】查询7天课程计划 POST /app/teacher/attendclass/listSevenDay */
export async function attendclassListSevenDayUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListAttendClassPlanDetialListRespVO>(
    '/app/teacher/attendclass/listSevenDay',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【老师】查询当天课程计划 POST /app/teacher/attendclass/listToday */
export async function attendclassListTodayUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListAttendClassPlanDetialRespVO>(
    '/app/teacher/attendclass/listToday',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【老师】筛选本堂课需要学习的单词 POST /app/teacher/attendclass/selectWordsLearn */
export async function attendclassSelectWordsLearnUsingPost({
  body,
  options,
}: {
  body: API.AttendClassWordListSelectReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>(
    '/app/teacher/attendclass/selectWordsLearn',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】设置课堂上课资料 POST /app/teacher/attendclass/setCourseware */
export async function attendclassSetCoursewareUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanCoursewareIdSetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/attendclass/setCourseware', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【老师】设置约课腾会议号 POST /app/teacher/attendclass/setTencentMeetingNo */
export async function attendclassSetTencentMeetingNoUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanTencentMeetingNoSetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>(
    '/app/teacher/attendclass/setTencentMeetingNo',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】开始上课 POST /app/teacher/attendclass/startClassPlan */
export async function attendclassStartClassPlanUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanStartOrEndReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/teacher/attendclass/startClassPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【老师】获取上课统计详情 POST /app/teacher/attendclass/statistics */
export async function attendclassStatisticsUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanStatisticsRespVO>(
    '/app/teacher/attendclass/statistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】更新 抗遗忘 单词复习进度 POST /app/teacher/attendclass/updateAntiforgetWordPross */
export async function attendclassUpdateAntiforgetWordProssUsingPost({
  body,
  options,
}: {
  body: API.AttendClassAntiforgetWordDetailUpdateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>(
    '/app/teacher/attendclass/updateAntiforgetWordPross',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】更新单词学习进度 POST /app/teacher/attendclass/updateWordPross */
export async function attendclassUpdateWordProssUsingPost({
  body,
  options,
}: {
  body: API.AttendClassWordDetailUpdateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>(
    '/app/teacher/attendclass/updateWordPross',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】未筛选的单词分页查询 POST /app/teacher/attendclass/wordAllPage */
export async function attendclassWordAllPageUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareWordPageRespVO>(
    '/app/teacher/attendclass/wordAllPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】需要抗遗忘的单词分页查询 POST /app/teacher/attendclass/wordAntiforgetPage */
export async function attendclassWordAntiforgetPageUsingPost({
  body,
  options,
}: {
  body: API.AttendClassWordListNeedAntiforgetPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareWordPageRespVO>(
    '/app/teacher/attendclass/wordAntiforgetPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【老师】需要学习的单词分页查询 POST /app/teacher/attendclass/wordNeedLearnPage */
export async function attendclassWordNeedLearnPageUsingPost({
  body,
  options,
}: {
  body: API.AttendClassWordListNeedLearnPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareWordPageRespVO>(
    '/app/teacher/attendclass/wordNeedLearnPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
