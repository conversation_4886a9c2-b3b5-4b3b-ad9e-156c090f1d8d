/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】课程资料绑定词汇资料信息 POST /pc/courseware/word/bind */
export async function pcCoursewareWordBindUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordBindReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/word/bind', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】新增词汇资料信息 POST /pc/courseware/word/create */
export async function pcCoursewareWordCreateUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultLong>('/pc/courseware/word/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】下载词汇资料模板 POST /pc/courseware/word/downTemplate */
export async function pcCoursewareWordDownTemplateUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<string>('/pc/courseware/word/downTemplate', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 【运营】查询词汇资料详情信息 POST /pc/courseware/word/get */
export async function pcCoursewareWordGetUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCoursewareWordByAdminPageRespVO>(
    '/pc/courseware/word/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】批量导入课件词汇资料 POST /pc/courseware/word/import */
export async function pcCoursewareWordOpenApiImportUsingPost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pcCoursewareWordOpenApiImportUsingPostParams;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/word/import', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 【运营】修改词汇资料信息 POST /pc/courseware/word/modify */
export async function pcCoursewareWordModifyUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/word/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】分页查询词汇资料信息 POST /pc/courseware/word/page */
export async function pcCoursewareWordPageUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordByAdminPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareWordByAdminPageRespVO>(
    '/pc/courseware/word/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】删除词汇资料信息 POST /pc/courseware/word/remove */
export async function pcCoursewareWordRemoveUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/word/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】课程资料解绑词汇资料信息 POST /pc/courseware/word/unbind */
export async function pcCoursewareWordUnbindUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordUnBindReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/courseware/word/unbind', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
