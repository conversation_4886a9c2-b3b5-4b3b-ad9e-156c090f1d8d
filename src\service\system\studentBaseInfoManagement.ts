/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【运营】添加学生信息 POST /pc/student/baseinfo/create */
export async function pcStudentBaseinfoCreateUsingPost({
  body,
  options,
}: {
  body: API.StuUserCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherAndStudentRespVO>(
    '/pc/student/baseinfo/create',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【运营】通过id获取学生信息 POST /pc/student/baseinfo/get */
export async function pcStudentBaseinfoGetUsingPost({
  body,
  options,
}: {
  body: API.StuUserGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStuUserGetRespVO>('/pc/student/baseinfo/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】修改学生信息 POST /pc/student/baseinfo/modifyStu */
export async function pcStudentBaseinfoModifyStuUsingPost({
  body,
  options,
}: {
  body: API.StuUserModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/student/baseinfo/modifyStu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【运营】分页请求获取学生列表 POST /pc/student/baseinfo/page */
export async function pcStudentBaseinfoPageUsingPost({
  body,
  options,
}: {
  body: API.StuUserPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStuUserPageRespVO>(
    '/pc/student/baseinfo/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
