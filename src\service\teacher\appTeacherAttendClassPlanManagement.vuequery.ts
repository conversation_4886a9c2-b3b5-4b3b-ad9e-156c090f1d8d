/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appTeacherAttendClassPlanManagement';
import * as API from './types';

/** 【老师】获取上课抗遗忘复习统计详情 POST /app/teacher/attendclass/antiforgetStatistics */
export function useAttendclassAntiforgetStatisticsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.ResultAttendClassPlanAntiforgetStatisticsRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassAntiforgetStatisticsUsingPost,
    onSuccess(data: API.ResultAttendClassPlanAntiforgetStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】完成抗遗忘单词复习 POST /app/teacher/attendclass/completeAntiforgetWord */
export function useAttendclassCompleteAntiforgetWordUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassCompleteAntiforgetWordUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】分页查询课程资料信息 POST /app/teacher/attendclass/coursewarePage */
export function useAttendclassCoursewarePageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareByAdminPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassCoursewarePageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultCoursewareByAdminPageRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】结束上课 POST /app/teacher/attendclass/endClassPlan */
export function useAttendclassEndClassPlanUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassEndClassPlanUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】根据id查询课程详情 POST /app/teacher/attendclass/get */
export function useAttendclassGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAttendClassPlanDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassGetUsingPost,
    onSuccess(data: API.ResultAttendClassPlanDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】根据资料id查询资料详情 POST /app/teacher/attendclass/getCoursewareDetial */
export function useAttendclassGetCoursewareDetialUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCoursewareByAdminPageRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassGetCoursewareDetialUsingPost,
    onSuccess(data: API.ResultCoursewareByAdminPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】app首页统计详情 POST /app/teacher/attendclass/homePageStatistics */
export function useAttendclassHomePageStatisticsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultHomePageStatisticsRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassHomePageStatisticsUsingPost,
    onSuccess(data: API.ResultHomePageStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】查询7天课程计划 POST /app/teacher/attendclass/listSevenDay */
export function useAttendclassListSevenDayUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListAttendClassPlanDetialListRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassListSevenDayUsingPost,
    onSuccess(data: API.ResultListAttendClassPlanDetialListRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】查询当天课程计划 POST /app/teacher/attendclass/listToday */
export function useAttendclassListTodayUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListAttendClassPlanDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassListTodayUsingPost,
    onSuccess(data: API.ResultListAttendClassPlanDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】筛选本堂课需要学习的单词 POST /app/teacher/attendclass/selectWordsLearn */
export function useAttendclassSelectWordsLearnUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassSelectWordsLearnUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】设置课堂上课资料 POST /app/teacher/attendclass/setCourseware */
export function useAttendclassSetCoursewareUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassSetCoursewareUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】设置约课腾会议号 POST /app/teacher/attendclass/setTencentMeetingNo */
export function useAttendclassSetTencentMeetingNoUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassSetTencentMeetingNoUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】开始上课 POST /app/teacher/attendclass/startClassPlan */
export function useAttendclassStartClassPlanUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassStartClassPlanUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】获取上课统计详情 POST /app/teacher/attendclass/statistics */
export function useAttendclassStatisticsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAttendClassPlanStatisticsRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassStatisticsUsingPost,
    onSuccess(data: API.ResultAttendClassPlanStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】更新 抗遗忘 单词复习进度 POST /app/teacher/attendclass/updateAntiforgetWordPross */
export function useAttendclassUpdateAntiforgetWordProssUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassUpdateAntiforgetWordProssUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】更新单词学习进度 POST /app/teacher/attendclass/updateWordPross */
export function useAttendclassUpdateWordProssUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassUpdateWordProssUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】未筛选的单词分页查询 POST /app/teacher/attendclass/wordAllPage */
export function useAttendclassWordAllPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareWordPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassWordAllPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultCoursewareWordPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】需要抗遗忘的单词分页查询 POST /app/teacher/attendclass/wordAntiforgetPage */
export function useAttendclassWordAntiforgetPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareWordPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassWordAntiforgetPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultCoursewareWordPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【老师】需要学习的单词分页查询 POST /app/teacher/attendclass/wordNeedLearnPage */
export function useAttendclassWordNeedLearnPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareWordPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassWordNeedLearnPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultCoursewareWordPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
