/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】充值 POST /app/student/order/recharge */
export async function orderRechargeUsingPost({
  body,
  options,
}: {
  body: API.RechargeReqVo;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultWxPayInfoVo>('/app/student/order/recharge', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生】学生钱包明细 POST /app/student/order/wallet/details */
export async function orderWalletDetailsUsingPost({
  body,
  options,
}: {
  body: API.PageDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStuWalletDetailsRespVo>(
    '/app/student/order/wallet/details',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】学生钱包信息 POST /app/student/order/wallet/info */
export async function orderWalletInfoUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStuWalletInfoRespVo>(
    '/app/student/order/wallet/info',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}
