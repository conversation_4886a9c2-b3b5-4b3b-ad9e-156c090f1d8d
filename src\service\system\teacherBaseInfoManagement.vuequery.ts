/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './teacherBaseInfoManagement';
import * as API from './types';

/** 【运营】添加老师信息 POST /pc/teacher/baseinfo/create */
export function usePcTeacherBaseinfoCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherAndStudentRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherBaseinfoCreateUsingPost,
    onSuccess(data: API.ResultTeacherAndStudentRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】获取指定id的老师信息详情 POST /pc/teacher/baseinfo/get */
export function usePcTeacherBaseinfoGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherUserDO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherBaseinfoGetUsingPost,
    onSuccess(data: API.ResultTeacherUserDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】修改指定老师信息 POST /pc/teacher/baseinfo/modify */
export function usePcTeacherBaseinfoModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherBaseinfoModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】根据指定条件获取老师分页列表 返回值: default response POST /pc/teacher/baseinfo/page */
export function usePcTeacherBaseinfoPageUsingPostMutation(options?: {
  onSuccess?: (value?: API.TeacherUserDO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherBaseinfoPageUsingPost,
    onSuccess(data: API.TeacherUserDO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
