/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【财务】财务钱包明细 POST /finance/wallet/details */
export async function financeWalletDetailsUsingPost({
  body,
  options,
}: {
  body: API.PageDto;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultFinanceWalletInfoDetailsRespVO>(
    '/finance/wallet/details',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【财务】财务钱包信息 POST /finance/wallet/info */
export async function financeWalletInfoUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultFinanceWalletInfoRespVO>('/finance/wallet/info', {
    method: 'POST',
    ...(options || {}),
  });
}
