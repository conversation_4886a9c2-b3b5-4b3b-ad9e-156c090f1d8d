/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】获取上课抗遗忘复习统计详情 POST /app/student/attendclass/antiforgetStatistics */
export async function attendclassAntiforgetStatisticsUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanAntiforgetStatisticsRespVO>(
    '/app/student/attendclass/antiforgetStatistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】结束上课 POST /app/student/attendclass/endClassPlan */
export async function attendclassEndClassPlanUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanStartOrEndReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/student/attendclass/endClassPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生】根据id查询课程详情 POST /app/student/attendclass/get */
export async function attendclassGetUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanDetialRespVO>(
    '/app/student/attendclass/get',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】根据资料id查询资料详情 POST /app/student/attendclass/getCoursewareDetial */
export async function attendclassGetCoursewareDetialUsingPost({
  body,
  options,
}: {
  body: API.CoursewareDetialGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultCoursewareByAdminPageRespVO>(
    '/app/student/attendclass/getCoursewareDetial',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】查询已完成的课程计划列表 POST /app/student/attendclass/listCompleted */
export async function attendclassListCompletedUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListAttendClassPlanDetialRespVO>(
    '/app/student/attendclass/listCompleted',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【学生】查询7天课程计划 POST /app/student/attendclass/listSevenDay */
export async function attendclassListSevenDayUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListAttendClassPlanDetialListRespVO>(
    '/app/student/attendclass/listSevenDay',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【学生】查询当天课程计划 POST /app/student/attendclass/listToday */
export async function attendclassListTodayUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultListAttendClassPlanDetialRespVO>(
    '/app/student/attendclass/listToday',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}

/** 【学生】分页查询已完成的课程计划 POST /app/student/attendclass/pageCompleted */
export async function attendclassPageCompletedUsingPost({
  body,
  options,
}: {
  body: API.CompletedClassPlanPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareWordPageRespVO>(
    '/app/student/attendclass/pageCompleted',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】开始上课 POST /app/student/attendclass/startClassPlan */
export async function attendclassStartClassPlanUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanStartOrEndReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/app/student/attendclass/startClassPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【学生】获取上课统计详情 POST /app/student/attendclass/statistics */
export async function attendclassStatisticsUsingPost({
  body,
  options,
}: {
  body: API.AttendClassPlanGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultAttendClassPlanStatisticsRespVO>(
    '/app/student/attendclass/statistics',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】获取老师详情 POST /app/student/attendclass/teacherDetail */
export async function attendclassTeacherDetailUsingPost({
  body,
  options,
}: {
  body: API.TeacherDetialReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherDetialRespVO>(
    '/app/student/attendclass/teacherDetail',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】获取推荐老师列表 POST /app/student/attendclass/teacherPage */
export async function attendclassTeacherPageUsingPost({
  body,
  options,
}: {
  body: API.StuSearchTeacherPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStuSearchTeacherPageRespVO>(
    '/app/student/attendclass/teacherPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 【学生】单词查询接口 POST /app/student/attendclass/wordPage */
export async function attendclassWordPageUsingPost({
  body,
  options,
}: {
  body: API.CoursewareWordPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultCoursewareWordPageRespVO>(
    '/app/student/attendclass/wordPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
