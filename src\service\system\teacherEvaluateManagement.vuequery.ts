/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './teacherEvaluateManagement';
import * as API from './types';

/** 【运营】查询评价详情 POST /pc/teacher/evaluate/get */
export function usePcTeacherEvaluateGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherEvaluateBaseRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherEvaluateGetUsingPost,
    onSuccess(data: API.ResultTeacherEvaluateBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询评价列表 POST /pc/teacher/evaluate/page */
export function usePcTeacherEvaluatePageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultTeacherEvaluateBaseRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcTeacherEvaluatePageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultTeacherEvaluateBaseRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
