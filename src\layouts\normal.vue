<template>
  <wd-config-provider :themeVars="theme">
    <scroll-view
      :scroll-y="true"
      :class="`h-100vh relative bg-[#F5F6FA] ${className}`"
      @scroll="handleScroll"
    >
      <wd-navbar
        :bordered="false"
        custom-class="!bg-transparent  !pb-2 "
        :left-arrow="!isTab"
        fixed
        :title="title"
        safeAreaInsetTop
        @click-left="handleClickLeft"
        :custom-style="`background-color: rgba(255,255,255, ${opacity})!important;padding-top:${safeAreaInsets}px`"
      ></wd-navbar>
      <view class="min-h-100vh" :style="{ marginTop: safeAreaInsets?.top + 'px' }">
        <slot></slot>
      </view>
    </scroll-view>
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { themeVars } from '@/constant'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { getSystemInfoSync } from '@/utils/system'
import { navigateBack, navigateToSub } from '@/utils'
const theme: ConfigProviderThemeVars = { ...themeVars }
const { title, getCurrentPage } = useNavigation()
const { isTab = false, style } = getCurrentPage()
const { safeAreaInsets } = getSystemInfoSync()
const opacity = ref(0)
defineProps({
  showHeaderBg: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
  },
})
const handleClickLeft = () => {
  navigateBack()
}
const handleScroll = (e) => {
  if (e.detail.scrollTop > 100) {
    opacity.value = 1
  } else {
    opacity.value = e.detail.scrollTop / 100
  }
}
</script>
