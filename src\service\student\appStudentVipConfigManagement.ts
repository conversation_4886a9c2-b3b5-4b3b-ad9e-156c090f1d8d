/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】查询有效学生vip权益配置详情 POST /app/student/vipconfig/get */
export async function vipconfigGetUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStudentVipConfigBaseRespVO>(
    '/app/student/vipconfig/get',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}
