/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【学生】查询有效的充值可选项配置详情 POST /app/student/rechargecountconfig/get */
export async function rechargecountconfigGetUsingPost({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<API.ResultConfigBaseByStuAppRespVO>(
    '/app/student/rechargecountconfig/get',
    {
      method: 'POST',
      ...(options || {}),
    }
  );
}
