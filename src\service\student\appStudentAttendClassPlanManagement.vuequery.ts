/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './appStudentAttendClassPlanManagement';
import * as API from './types';

/** 【学生】获取上课抗遗忘复习统计详情 POST /app/student/attendclass/antiforgetStatistics */
export function useAttendclassAntiforgetStatisticsUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.ResultAttendClassPlanAntiforgetStatisticsRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassAntiforgetStatisticsUsingPost,
    onSuccess(data: API.ResultAttendClassPlanAntiforgetStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】结束上课 POST /app/student/attendclass/endClassPlan */
export function useAttendclassEndClassPlanUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassEndClassPlanUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】根据id查询课程详情 POST /app/student/attendclass/get */
export function useAttendclassGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAttendClassPlanDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassGetUsingPost,
    onSuccess(data: API.ResultAttendClassPlanDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】根据资料id查询资料详情 POST /app/student/attendclass/getCoursewareDetial */
export function useAttendclassGetCoursewareDetialUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCoursewareByAdminPageRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassGetCoursewareDetialUsingPost,
    onSuccess(data: API.ResultCoursewareByAdminPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】查询已完成的课程计划列表 POST /app/student/attendclass/listCompleted */
export function useAttendclassListCompletedUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListAttendClassPlanDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassListCompletedUsingPost,
    onSuccess(data: API.ResultListAttendClassPlanDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】查询7天课程计划 POST /app/student/attendclass/listSevenDay */
export function useAttendclassListSevenDayUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListAttendClassPlanDetialListRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassListSevenDayUsingPost,
    onSuccess(data: API.ResultListAttendClassPlanDetialListRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】查询当天课程计划 POST /app/student/attendclass/listToday */
export function useAttendclassListTodayUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultListAttendClassPlanDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassListTodayUsingPost,
    onSuccess(data: API.ResultListAttendClassPlanDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】分页查询已完成的课程计划 POST /app/student/attendclass/pageCompleted */
export function useAttendclassPageCompletedUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareWordPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassPageCompletedUsingPost,
    onSuccess(data: API.PageResultResponsePageResultCoursewareWordPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】开始上课 POST /app/student/attendclass/startClassPlan */
export function useAttendclassStartClassPlanUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassStartClassPlanUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】获取上课统计详情 POST /app/student/attendclass/statistics */
export function useAttendclassStatisticsUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultAttendClassPlanStatisticsRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassStatisticsUsingPost,
    onSuccess(data: API.ResultAttendClassPlanStatisticsRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】获取老师详情 POST /app/student/attendclass/teacherDetail */
export function useAttendclassTeacherDetailUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherDetialRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassTeacherDetailUsingPost,
    onSuccess(data: API.ResultTeacherDetialRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】获取推荐老师列表 POST /app/student/attendclass/teacherPage */
export function useAttendclassTeacherPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStuSearchTeacherPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassTeacherPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultStuSearchTeacherPageRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【学生】单词查询接口 POST /app/student/attendclass/wordPage */
export function useAttendclassWordPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareWordPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.attendclassWordPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultCoursewareWordPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
