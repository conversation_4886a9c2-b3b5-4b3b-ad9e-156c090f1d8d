/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './orgStudentManagement';
import * as API from './types';

/** 【机构】添加学生信息 POST /pc/orgStudent/create */
export function usePcOrgStudentCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultTeacherAndStudentRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgStudentCreateUsingPost,
    onSuccess(data: API.ResultTeacherAndStudentRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【机构】通过id获取学生信息 POST /pc/orgStudent/get */
export function usePcOrgStudentGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultStuUserGetRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgStudentGetUsingPost,
    onSuccess(data: API.ResultStuUserGetRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【机构】修改学生信息 POST /pc/orgStudent/modify */
export function usePcOrgStudentModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgStudentModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【机构】分页请求获取学生列表 POST /pc/orgStudent/page */
export function usePcOrgStudentPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultStuUserPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcOrgStudentPageUsingPost,
    onSuccess(data: API.PageResultResponsePageResultStuUserPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
