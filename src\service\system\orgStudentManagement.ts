/* eslint-disable */
// @ts-ignore
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';

/** 【机构】添加学生信息 POST /pc/orgStudent/create */
export async function pcOrgStudentCreateUsingPost({
  body,
  options,
}: {
  body: API.SysOrgStuUserCreateReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultTeacherAndStudentRespVO>('/pc/orgStudent/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【机构】通过id获取学生信息 POST /pc/orgStudent/get */
export async function pcOrgStudentGetUsingPost({
  body,
  options,
}: {
  body: API.StuUserGetReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultStuUserGetRespVO>('/pc/orgStudent/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【机构】修改学生信息 POST /pc/orgStudent/modify */
export async function pcOrgStudentModifyUsingPost({
  body,
  options,
}: {
  body: API.SysOrgStuUserModifyReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.ResultBoolean>('/pc/orgStudent/modify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 【机构】分页请求获取学生列表 POST /pc/orgStudent/page */
export async function pcOrgStudentPageUsingPost({
  body,
  options,
}: {
  body: API.SysOrgStuUserPageReqVO;
  options?: CustomRequestOptions;
}) {
  return request<API.PageResultResponsePageResultStuUserPageRespVO>(
    '/pc/orgStudent/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
