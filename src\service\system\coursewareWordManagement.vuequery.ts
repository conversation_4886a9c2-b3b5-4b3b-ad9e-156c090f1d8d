/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './coursewareWordManagement';
import * as API from './types';

/** 【运营】课程资料绑定词汇资料信息 POST /pc/courseware/word/bind */
export function usePcCoursewareWordBindUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordBindUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】新增词汇资料信息 POST /pc/courseware/word/create */
export function usePcCoursewareWordCreateUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordCreateUsingPost,
    onSuccess(data: API.ResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】下载词汇资料模板 POST /pc/courseware/word/downTemplate */
export function usePcCoursewareWordDownTemplateUsingPostMutation(options?: {
  onSuccess?: (value?: string) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordDownTemplateUsingPost,
    onSuccess(data: string) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】查询词汇资料详情信息 POST /pc/courseware/word/get */
export function usePcCoursewareWordGetUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultCoursewareWordByAdminPageRespVO) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordGetUsingPost,
    onSuccess(data: API.ResultCoursewareWordByAdminPageRespVO) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】批量导入课件词汇资料 POST /pc/courseware/word/import */
export function usePcCoursewareWordOpenApiImportUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordOpenApiImportUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】修改词汇资料信息 POST /pc/courseware/word/modify */
export function usePcCoursewareWordModifyUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordModifyUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】分页查询词汇资料信息 POST /pc/courseware/word/page */
export function usePcCoursewareWordPageUsingPostMutation(options?: {
  onSuccess?: (
    value?: API.PageResultResponsePageResultCoursewareWordByAdminPageRespVO
  ) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordPageUsingPost,
    onSuccess(
      data: API.PageResultResponsePageResultCoursewareWordByAdminPageRespVO
    ) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】删除词汇资料信息 POST /pc/courseware/word/remove */
export function usePcCoursewareWordRemoveUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordRemoveUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 【运营】课程资料解绑词汇资料信息 POST /pc/courseware/word/unbind */
export function usePcCoursewareWordUnbindUsingPostMutation(options?: {
  onSuccess?: (value?: API.ResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.pcCoursewareWordUnbindUsingPost,
    onSuccess(data: API.ResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
