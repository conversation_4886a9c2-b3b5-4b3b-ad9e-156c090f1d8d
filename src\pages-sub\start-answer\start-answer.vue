<route lang="json5">
{
  style: {
    navigationBarTitleText: '答题',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>
<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- 题目内容区域 -->
    <view class="flex-1 flex flex-col items-center justify-center px-48rpx">
      <!-- 题目标题 -->
      <view class="text-64rpx font-600 text-#333 text-center mb-32rpx">
        {{ currentQuestion.content }}
      </view>

      <!-- 提示文字 -->
      <view class="text-24rpx text-#999 text-center mb-80rpx">备注：错误状态和正确状态</view>

      <!-- 选项区域 -->
      <view class="w-full space-y-24rpx">
        <wd-radio-group
          v-model="currentQuestion.studentAnswer"
          shape="button"
          class="option-group w-full"
        >
          <!-- 休息选项 -->
          <view
            class="option-item rest-option"
            :class="{
              isCorrect: isCorrectAnswer(1),
              isInCorrect: isInCorrectAnswer(1),
            }"
            @click="selectOption(1)"
          >
            <text class="option-text">{{ currentQuestion.option1 || '--' }}</text>
          </view>

          <!-- 呼吸选项 -->
          <view
            class="option-item breath-option"
            :class="{
              isCorrect: isCorrectAnswer(2),
              isInCorrect: isInCorrectAnswer(2),
            }"
            @click="selectOption(2)"
          >
            <text class="option-text">{{ currentQuestion.option2 || '--' }}</text>
          </view>

          <!-- 胸部选项 -->
          <view
            class="option-item chest-option"
            :class="{
              isCorrect: isCorrectAnswer(3),
              isInCorrect: isInCorrectAnswer(3),
            }"
            @click="selectOption(3)"
          >
            <text class="option-text">{{ currentQuestion.option3 || '--' }}</text>
          </view>

          <!-- 早晨选项 -->
          <view
            class="option-item morning-option"
            :class="{
              isCorrect: isCorrectAnswer(4),
              isInCorrect: isInCorrectAnswer(4),
            }"
            @click="selectOption(4)"
          >
            <text class="option-text">{{ currentQuestion.option4 || '--' }}</text>
          </view>

          <!-- 品种选项 -->
          <view
            class="option-item variety-option"
            :class="{
              isCorrect: isCorrectAnswer(5),
              isInCorrect: isInCorrectAnswer(5),
            }"
            @click="selectOption(5)"
          >
            <text class="option-text">{{ currentQuestion.option5 || '--' }}</text>
          </view>

          <!-- 不认识选项 -->
          <view
            class="option-item unknown-option"
            :class="{ unknownOptions: currentQuestion.studentAnswer === -1 }"
            @click="selectOption(-1)"
          >
            <text class="option-text">不认识</text>
          </view>
        </wd-radio-group>
      </view>
    </view>

    <!-- 底部统计区域 -->
    <view class="bg-white px-48rpx py-32rpx">
      <!-- 提示文字 -->
      <view class="text-24rpx text-#999 text-center mb-32rpx">停留页面超过10s，建议选择不认识</view>

      <!-- 进度和统计 -->
      <view class="flex items-center justify-between mb-32rpx">
        <view class="text-28rpx text-#333">
          {{ currentQuestionNum + 1 }}/{{ practiseDetail.length }}
        </view>

        <view class="flex items-center space-x-32rpx">
          <!-- 正确数量 -->
          <view class="flex items-center">
            <view class="w-32rpx h-32rpx bg-#4caf50 rounded-full mr-16rpx"></view>
            <text class="text-28rpx text-#333">{{ correctCount }}</text>
          </view>

          <!-- 错误数量 -->
          <view class="flex items-center">
            <view class="w-32rpx h-32rpx bg-#f44336 rounded-full mr-16rpx"></view>
            <text class="text-28rpx text-#333">{{ errorCount }}</text>
          </view>
        </view>
      </view>

      <!-- 导航按钮 -->
      <!-- <view class="flex gap-24rpx">
        <wd-button
          custom-class="nav-btn prev-btn"
          :disabled="currentQuestionNum === 0"
          @click="prevQuestion"
        >
          上一题
        </wd-button>

        <wd-button
          v-if="currentQuestionNum === practiseDetail.length - 1"
          custom-class="nav-btn submit-btn"
          type="primary"
          @click="handleSubmit"
        >
          提交试卷
        </wd-button>
        <wd-button v-else custom-class="nav-btn next-btn" type="primary" @click="nextQuestion">
          下一题
        </wd-button>
      </view> -->
    </view>

    <wd-message-box selector="checkResult">
      <view class="success-dialog-content">
        <!-- 成功图标 -->
        <view class="success-icon">
          <image
            class="w-120rpx h-120rpx"
            src="../../assets/images/book-class-success.png"
            mode="scaleToFill"
          />
        </view>

        <!-- 成功文字 -->
        <view class="success-title">您已完成测试</view>
        <view class="success-subtitle">
          {{ `一共${practiseDetail.length}道测试题，答对${correctCount}道测试题,加油哦` }}
        </view>
      </view>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
import {
  classpractisAnswerQuestionsUsingPost,
  classpractisPractisDetailPageUsingPost,
  StudentPractiseDetailBaseRespVO,
} from '@/service/student'
import { useMessage } from 'wot-design-uni'
import { ref, onMounted, computed } from 'vue'

import { navigateTo, navigateToSub } from '@/utils'

// 防抖函数
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout | null = null
  return (...args: any[]) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      func.apply(null, args)
    }, delay)
  }
}

const practiseId = ref(0)
const message = useMessage('checkResult')
const answerFrom = ref({
  // 试卷题目Id
  id: 0,
  // 答案
  studentAnswer: 1,
})
const currentQuestionNum = ref(0)
const currentQuestion = ref<StudentPractiseDetailBaseRespVO>({})
const fetchPractiseFrom = ref({})
const practiseDetail = ref([])

// 选项点击的实际处理函数
const handleSelectOption = (value: number) => {
  currentQuestion.value.studentAnswer = value
  // 完成对试题答案的提交
  setTimeout(() => {
    submitAnswer()
  }, 500)
}

// 带防抖的选项点击方法（300ms防抖延迟）
const selectOption = debounce(handleSelectOption, 300)

// 计算正确和错误数量
const correctCount = computed(() => {
  return practiseDetail.value.filter(
    (item) => item.studentAnswer === item.answer && item.studentAnswer !== null,
  ).length
})
const errorCount = computed(() => {
  return practiseDetail.value.filter(
    (item) => item.studentAnswer !== item.answer && item.studentAnswer !== null,
  ).length
})
// 获取试卷详情
const fetchPractiseDetail = async () => {
  try {
    fetchPractiseFrom.value = {
      pageSize: 9999,
      pageNo: 1,
      practiseId: practiseId.value,
    }
    const res = await classpractisPractisDetailPageUsingPost({ body: fetchPractiseFrom.value })
    if (res.code === 200) {
      console.log('data', res.data)
      practiseDetail.value = res.data.items
      if (practiseDetail.value.length > 0) {
        // 设置当前题目
        currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
      }
    }
  } catch (err) {
    console.log('获取试卷err', err)
  }
}
// 上一题实现逻辑
// const prevQuestion = () => {
//   currentQuestionNum.value--
//   currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
// }
// 下一题逻辑实现
const submitAnswer = async () => {
  // 完成对上一题答案提交
  console.log('执行了', currentQuestionNum.value, currentQuestion.value.studentAnswer)
  if (currentQuestion.value.studentAnswer) {
    answerFrom.value.id = currentQuestion.value.id
    answerFrom.value.studentAnswer = currentQuestion.value.studentAnswer
    const res = await classpractisAnswerQuestionsUsingPost({ body: answerFrom.value })
    // 将当前试题的答案更新到维护的试卷中，方便后续对题目的查阅
    if (res.code === 200) {
      // 将当前成绩的答案更新到维护的试卷中
      practiseDetail.value[currentQuestionNum.value].studentAnswer =
        currentQuestion.value.studentAnswer
    }
  }
  // 实现下一题相关逻辑
  if (currentQuestionNum.value < practiseDetail.value.length - 1) {
    currentQuestionNum.value++
    currentQuestion.value = practiseDetail.value[currentQuestionNum.value]
  } else {
    handleSubmit()
  }
}
// 最后一题提交逻辑
const handleSubmit = async () => {
  // 还需要对最后一题的答案进行提交
  if (currentQuestionNum.value === practiseDetail.value.length - 1) {
    answerFrom.value.id = currentQuestion.value.id
    answerFrom.value.studentAnswer = currentQuestion.value.studentAnswer
    const res = await classpractisAnswerQuestionsUsingPost({ body: answerFrom.value })
    // 将当前试题的答案更新到维护的试卷中，方便后续对题目的查阅
    if (res.code === 200) {
      // 将当前成绩的答案更新到维护的试卷中
      practiseDetail.value[currentQuestionNum.value].studentAnswer =
        currentQuestion.value.studentAnswer
    }
  }
  message
    .confirm({
      // msg: '请及时前往上课',
      title: '',
      confirmButtonText: '重新测试',
      cancelButtonText: '返回首页',
    })
    .then(() => {
      // 回到第一题
      navigateTo('/self-study/self-study')
    })
    .catch(() => {
      navigateTo('/index/index')
    })
}
onLoad((options: any) => {
  if (options?.practiseId) practiseId.value = options?.practiseId
})
// 返回判断正确答案的结果
const isCorrectAnswer = (studentAnswer: number) => {
  return (
    currentQuestion.value.studentAnswer === studentAnswer &&
    currentQuestion.value.answer === studentAnswer
  )
}
const isInCorrectAnswer = (studentAnswer: number) => {
  return (
    currentQuestion.value.studentAnswer === studentAnswer &&
    currentQuestion.value.answer !== studentAnswer
  )
}
onMounted(() => fetchPractiseDetail())
</script>

<style lang="scss" scoped>
:deep(.option-group) {
  background-color: rgb(245, 245, 245);
}
// 选项样式
.option-item {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  .option-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
  //  正确的颜色
  &.isCorrect {
    background: #e8f5e8;
    border: 2rpx solid #c8e6c9;
    transform: scale(0.98);
  }
  &.isInCorrect {
    background: #fce4ec;
    border: 2rpx solid #f8bbd9;
  }
  &.currentAnswer {
    background: #e8f5e8;
    border: 2rpx solid #c8e6c9;
  }
  &.unknownOptions {
    background: #9e9e9e;
    border-color: #757575;
  }
}

// 导航按钮样式
.nav-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.prev-btn {
  background: #f5f5f5;
  color: #666;
  border: none;

  &:disabled {
    background: #f0f0f0;
    color: #ccc;
  }
}

.next-btn,
.submit-btn {
  background: #3d5af5;
  border: none;
}
.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}
</style>
