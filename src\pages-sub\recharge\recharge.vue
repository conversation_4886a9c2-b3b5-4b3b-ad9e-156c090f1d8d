<route lang="json5">
{
  layout: 'common',
  style: {
    navigationBarTitleText: '课时充值',
  },
}
</route>

<template>
  <view class="min-h-screen bg-gray-50 p-32rpx">
    <!-- 课时类型选择 -->
    <view class="bg-white rounded-16rpx p-32rpx mb-32rpx shadow-sm">
      <view class="text-32rpx font-600 text-gray-800 mb-24rpx flex items-center">
        <view class="w-8rpx h-32rpx bg-purple-500 rounded-full mr-16rpx"></view>
        选择课时类型
      </view>

      <view class="flex gap-16rpx">
        <view
          v-for="(type, index) in durationTypes"
          :key="index"
          class="flex-1 py-24rpx text-center text-28rpx rounded-12rpx cursor-pointer transition-all duration-300 active:scale-95"
          :class="
            selectedType === type.value
              ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          "
          @click="handleSelectType(type.value)"
        >
          <view class="font-600">{{ type.label }}</view>
        </view>
      </view>
    </view>

    <!-- 课时套餐选择 -->
    <view class="bg-white rounded-16rpx p-32rpx mb-32rpx shadow-sm">
      <view class="text-32rpx font-600 text-gray-800 mb-24rpx flex items-center">
        <view class="w-8rpx h-32rpx bg-green-500 rounded-full mr-16rpx"></view>
        选择课时套餐
      </view>

      <view class="grid grid-cols-2 gap-16rpx" v-if="priceConfigs && priceConfigs.length > 0">
        <view
          v-for="(item, index) in priceConfigs"
          :key="index"
          class="relative p-24rpx rounded-12rpx border-2 cursor-pointer transition-all duration-300 active:scale-95"
          :class="
            selectedPriceId === item.id
              ? 'border-green-500 bg-green-50 shadow-lg'
              : 'border-gray-200 bg-white hover:border-green-300'
          "
          @click="handleSelectPrice(item.id)"
        >
          <!-- 选中标识 -->
          <view
            v-if="selectedPriceId === item.id"
            class="absolute -top-8rpx -right-8rpx w-32rpx h-32rpx bg-green-500 rounded-full flex items-center justify-center"
          >
            <view class="text-white text-20rpx">✓</view>
          </view>

          <view class="text-28rpx font-600 text-gray-800 mb-12rpx">{{ item.durationName }}</view>

          <view class="flex items-center justify-between mb-8rpx">
            <view class="text-32rpx font-700 text-red-500">
              ¥{{ item.discountPrice || item.price }}
            </view>
            <view
              v-if="item.discount && item.discount < 100"
              class="text-24rpx text-gray-400 line-through"
            >
              ¥{{ item.price }}
            </view>
          </view>

          <view
            v-if="item.discount && item.discount < 100"
            class="inline-block px-12rpx py-4rpx bg-red-100 text-red-600 text-20rpx rounded-full"
          >
            {{ item.discount / 10 }}折优惠
          </view>
        </view>
      </view>

      <view v-else class="text-center py-80rpx">
        <view
          class="w-120rpx h-120rpx bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-24rpx"
        >
          <view class="text-48rpx">📦</view>
        </view>
        <view class="text-28rpx text-gray-500">暂无套餐信息</view>
      </view>
    </view>

    <!-- 课时数量和总价 -->
    <view class="bg-white rounded-16rpx p-32rpx mb-32rpx shadow-sm">
      <view class="text-32rpx font-600 text-gray-800 mb-24rpx flex items-center">
        <view class="w-8rpx h-32rpx bg-orange-500 rounded-full mr-16rpx"></view>
        课时数量
      </view>

      <view class="space-y-24rpx">
        <!-- 课时数量 -->
        <view class="flex flex-row items-center justify-center flex-wrap">
          <!-- <view class="text-28rpx font-500 text-gray-700 mb-16rpx">课时数量</view> -->
          <view
            v-for="(countType, index) in rechargeCount"
            :key="index"
            class="flex-1 py-24rpx text-center text-28rpx rounded-12rpx cursor-pointer transition-all duration-300 active:scale-95"
            :class="
              selectCountType === countType.value
                ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            "
            @click="handleSelectCountType(countType.value)"
          >
            <view class="font-600">{{ countType.label }}</view>
          </view>
        </view>

        <!-- 总价显示 -->
        <view
          class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-12rpx p-24rpx border border-orange-200"
        >
          <view class="flex items-center justify-between">
            <view class="text-28rpx font-500 text-gray-700">总价</view>
            <view class="text-48rpx font-700 text-orange-600">¥{{ totalPrice }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="bg-white rounded-16rpx p-32rpx mb-32rpx shadow-sm">
      <view class="text-32rpx font-600 text-gray-800 mb-24rpx flex items-center">
        <view class="w-8rpx h-32rpx bg-indigo-500 rounded-full mr-16rpx"></view>
        选择支付方式
      </view>

      <view class="space-y-16rpx">
        <view
          v-for="(method, index) in paymentMethods"
          :key="index"
          class="flex items-center justify-between p-24rpx rounded-12rpx border-2 cursor-pointer transition-all duration-300 active:scale-95"
          :class="
            selectedPaymentMethod === method.value
              ? 'border-indigo-500 bg-indigo-50 shadow-lg'
              : 'border-gray-200 bg-white hover:border-indigo-300'
          "
          @click="selectedPaymentMethod = method.value"
        >
          <view class="flex items-center">
            <view
              class="w-48rpx h-48rpx rounded-12rpx flex items-center justify-center mr-24rpx"
              :style="{ backgroundColor: method.color + '20' }"
            >
              <wd-icon :name="method.icon" size="32rpx" :color="method.color"></wd-icon>
            </view>
            <view class="text-28rpx font-500 text-gray-800">{{ method.label }}</view>
          </view>

          <view
            class="w-32rpx h-32rpx rounded-full border-2 flex items-center justify-center"
            :class="
              selectedPaymentMethod === method.value
                ? 'border-indigo-500 bg-indigo-500'
                : 'border-gray-300'
            "
          >
            <view
              v-if="selectedPaymentMethod === method.value"
              class="w-16rpx h-16rpx rounded-full bg-white"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部充值按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-32rpx border-t border-gray-200 shadow-lg">
      <view class="flex items-center justify-between mb-24rpx">
        <view class="text-28rpx text-gray-600 font-500">应付金额</view>
        <view class="text-48rpx font-700 text-red-500">¥{{ totalPrice }}</view>
      </view>

      <wd-button
        type="primary"
        size="large"
        :loading="loading"
        :disabled="!selectedPriceId || !selectedPaymentMethod"
        @click="handleRecharge"
        custom-class="w-full h-96rpx rounded-16rpx text-32rpx font-600 bg-gradient-to-r from-blue-500 to-blue-600"
      >
        <view v-if="loading" class="flex items-center">
          <view
            class="w-32rpx h-32rpx border-4 border-white border-t-transparent rounded-full animate-spin mr-16rpx"
          ></view>
          处理中...
        </view>
        <view v-else>立即支付</view>
      </wd-button>
    </view>

    <!-- 底部安全间距 -->
    <view class="h-200rpx"></view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { showToast, navigateBack } from '@/utils'
import {
  orderRechargeUsingPost,
  orderWalletInfoUsingPost,
  rechargecountconfigGetUsingPost,
} from '@/service/student'
import { pcSystemPriceConfigListUsingPost } from '@/service/system/systemPriceConfigManagement'
import { StuWalletInfoRespVo } from '@/service/student/types'
import { SysPriceConfigDO } from '@/service/system'

// 钱包信息
const walletInfo = ref<StuWalletInfoRespVo | null>(null)

// 课时类型选项
const durationTypes = [
  { label: '30分钟课时', value: 0 },
  { label: '60分钟课时', value: 1 },
  { label: '体验课', value: 2 },
]
const rechargeCount = ref({})
// 选中的课时类型
const selectedType = ref(1) // 默认选择60分钟课时
// 选择的课时次数，默认为0
const selectCountType = ref(0)
// 充值选项配置
const rechargeConfig = ref({})
// 价格配置列表
const priceConfigs = ref<SysPriceConfigDO[]>()

// 选中的价格配置ID
const selectedPriceId = ref<number | null>(null)

// 课时数量
const classNumber = ref(1)

// 支付方式
const paymentMethods = [
  { label: '微信支付', value: 'wechat', icon: 'wechat', color: '#07C160' },
  { label: '支付宝支付', value: 'alipay', icon: 'alipay', color: '#1677FF' },
]

// 选中的支付方式
const selectedPaymentMethod = ref('wechat')

// 加载状态
const loading = ref(false)

// 计算总价
const totalPrice = computed(() => {
  if (!selectedPriceId.value) return 0

  const selectedConfig = priceConfigs.value.find((item) => item.id === selectedPriceId.value)
  if (!selectedConfig) return 0

  const price = selectedConfig.discountPrice || selectedConfig.price || 0
  return (price * classNumber.value).toFixed(2)
})

const getRechargeConfig = async () => {
  try {
    const res = await rechargecountconfigGetUsingPost({})
    console.log('充值配置:', res)

    if (res.code === 200) {
      rechargeConfig.value = res.data.item
      // const countObj = { ...rechargeConfig.value, sysPriceConfigDOs: null }
      const countObj = {
        option1: 30,
        option2: 60,
        option3: 70,
        option4: 80,
      }
      rechargeCount.value = Object.values(countObj).map((item) => {
        return {
          label: item,
          value: item,
        }
      })
      selectCountType.value = rechargeCount.value[0].value
      console.log('rechargeCount', rechargeCount.value)
    } else {
      showToast('获取充值配置失败')
    }
  } catch (error) {
    console.error('获取充值配置失败:', error)
    showToast('获取充值配置失败')
  }
}
// 计算总价接口，在每一次改变课程或者课程价格类型时触发计算
// 选择课时类型
const handleSelectType = (type: number) => {
  selectedType.value = type
  // 重置选中的价格配置
  selectedPriceId.value = null
  // 获取对应类型的价格配置
  fetchPriceConfigs()
}
const handleSelectCountType = (type: number) => {
  selectCountType.value = type
}
// 选择价格配置
const handleSelectPrice = (id: number) => {
  selectedPriceId.value = id
}

// 获取钱包信息
const fetchWalletInfo = async () => {
  try {
    const res = await orderWalletInfoUsingPost({})
    console.log('钱包信息:', res)

    if (res.code === 200 && res.data?.item) {
      walletInfo.value = res.data.item
    } else {
      showToast('获取钱包信息失败')
    }
  } catch (error) {
    console.error('获取钱包信息失败:', error)
    showToast('获取钱包信息失败')
  }
}

// 获取价格配置
const fetchPriceConfigs = async () => {
  try {
    const res = await pcSystemPriceConfigListUsingPost({
      body: {
        durationType: selectedType.value,
      },
    })
    console.log('价格配置:', res)

    if (res.code === 200) {
      priceConfigs.value = res.data.items
      // 默认选中第一个价格配置
      if (priceConfigs.value.length > 0) {
        selectedPriceId.value = priceConfigs.value[0].id
      }
    } else {
      showToast('获取价格配置失败')
    }
  } catch (error) {
    console.error('获取价格配置失败:', error)
    showToast('获取价格配置失败')
  }
}

// 处理充值
const handleRecharge = async () => {
  if (!selectedPriceId.value) {
    showToast('请选择课时套餐')
    return
  }

  loading.value = true
  try {
    const res = await orderRechargeUsingPost({
      body: {
        priceId: selectedPriceId.value,
        courseDuration: selectedType.value + '',
        classNumber: classNumber.value,
      },
    })
    console.log('充值结果:', res)

    if (res.code === 200 && res.data?.item) {
      showToast('充值成功')
      // 刷新钱包信息
      fetchWalletInfo()
      // 延迟返回
      setTimeout(() => {
        navigateBack()
      }, 1500)
    } else {
      showToast(res.message || '充值失败')
    }
  } catch (error) {
    console.error('充值失败:', error)
    showToast('充值失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 获取钱包信息
  fetchWalletInfo()
  // 获取价格配置
  fetchPriceConfigs()
  // 获取充值配置
  getRechargeConfig()
})
</script>
